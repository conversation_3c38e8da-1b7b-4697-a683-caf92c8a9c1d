#!/bin/sh

if  [ -n "$BACKEND_API_URL" ]; then
    BACKEND_API_URL_ESCAPED=$(printf '%s\n' "$BACKEND_API_URL" | sed -e 's/[\/&]/\\&/g')

    cd /usr/share/nginx/html/vEdge/services || exit

    echo "Replacing REPLACE_WITH_BACKEND_API_URL with escaped $BACKEND_API_URL_ESCAPED"
    sed -i -- "s/<REPLACE_WITH_BACKEND_API_URL>/$BACKEND_API_URL_ESCAPED/g" *.js
fi

if  [ -n "$BACKEND_AO_URL" ]; then
    BACKEND_AO_URL_ESCAPED=$(printf '%s\n' "$BACKEND_AO_URL" | sed -e 's/[\/&]/\\&/g')

    cd /usr/share/nginx/html/vEdge/services || exit

    echo "Replacing REPLACE_WITH_BACKEND_AO_URL with escaped $BACKEND_AO_URL_ESCAPED"
    sed -i -- "s/<REPLACE_WITH_BACKEND_AO_URL>/$BACKEND_AO_URL_ESCAPED/g" *.js
fi

if  [ -n "$DATE_RANGE" ]; then
    DATE_RANGE_ESCAPED=$(printf '%s\n' "$DATE_RANGE" | sed -e 's/[\/&]/\\&/g')

    cd /usr/share/nginx/html/vEdge/common-ui || exit

    echo "Replacing REPLACE_WITH_DATE_RANGE with escaped $DATE_RANGE_ESCAPED"
    sed -i -- "s/<REPLACE_WITH_DATE_RANGE>/$DATE_RANGE_ESCAPED/g" *.js
fi

if  [ -n "$USER_TIMEOUT" ]; then
    USER_TIMEOUT_ESCAPED=$(printf '%s\n' "$USER_TIMEOUT" | sed -e 's/[\/&]/\\&/g')

    cd /usr/share/nginx/html/vEdge/services || exit

    echo "Replacing REPLACE_WITH_USER_TIMEOUT with escaped $USER_TIMEOUT_ESCAPED"
    sed -i -- "s/<REPLACE_WITH_USER_TIMEOUT>/$USER_TIMEOUT_ESCAPED/g" *.js
fi
