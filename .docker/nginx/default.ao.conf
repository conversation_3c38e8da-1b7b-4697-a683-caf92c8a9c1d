server {
    listen 80;
    root /usr/share/nginx/html/vEdge;
    index index.html;

    server_tokens off;

    # Brotli settings
    brotli on; # Enable dynamic Brotli compression
    # Types of files to compress with <PERSON><PERSON><PERSON> dynamically
    brotli_types text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript image/svg+xml application/x-font-ttf font/opentype image/x-icon;

    # Optional: Fallback to gzip if brotli is not supported by client
    gzip on;
    gzip_vary on;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_buffers 16 8k;
    gzip_http_version 1.1;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript image/svg+xml application/x-font-ttf font/opentype image/x-icon;

    proxy_buffers 4 256k;
    proxy_buffer_size 128k;
    proxy_busy_buffers_size 256k;
    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection 'upgrade';
    proxy_set_header Host $host;
    proxy_cache_bypass $http_upgrade;
    proxy_ssl_server_name on;

    location / {
         # kill cache
         add_header Last-Modified $date_gmt;
         expires off;
         if_modified_since off;
         etag off;
        try_files $uri $uri/ =404;
    }

    location /v2 {
        alias /usr/share/nginx/html/vEdge/main;
        try_files $uri /v2/index.html;
        add_header Cache-Control "no-cache, must-revalidate";
    }

    location /v2/vedge-commonui/ {
        alias /usr/share/nginx/html/vEdge/common-ui/;
    }

    location /v2/vedge-services/ {
        alias /usr/share/nginx/html/vEdge/services/;
    }

    location /v2/vedge-customer/ {
        alias /usr/share/nginx/html/vEdge/customer/;
    }

    location /v2/vedge-reporting/ {
        alias /usr/share/nginx/html/vEdge/reporting/;
    }

    location /v2/vedge-network/ {
        alias /usr/share/nginx/html/vEdge/network/;
    }

    location /v2/storybook/ {
        alias /usr/share/nginx/html/vEdge/storybook/;
    }

    location /v2/vedge-admincp/ {
        alias /usr/share/nginx/html/vEdge/admincp/;
    }

    location /v2/vedge-techtoolbox/ {
        alias /usr/share/nginx/html/vEdge/techtoolbox/;
    }

    location /v2/vedge-batchfw/ {
        alias /usr/share/nginx/html/vEdge/batchfw/;
    }

    location /bff {
      proxy_pass http://0.0.0.0:4000/bff;
    }
}
