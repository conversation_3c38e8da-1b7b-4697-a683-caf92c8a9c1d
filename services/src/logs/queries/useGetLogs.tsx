import { useQuery } from '@tanstack/react-query';
import { AxonQueryOptions, BFFResponseDTO } from '../../types';
import { getLogs } from '../apis/getLogs';
import { QUERY_KEYS } from '../constants/queryKeys';
import { LogParams, LogsResponse } from '../types/Logs';

export const useGetLogs = (params: LogParams, options?: AxonQueryOptions<BFFResponseDTO<LogsResponse>>) => {
  return useQuery({ queryKey: QUERY_KEYS.LOGS_DRAWER(params), queryFn: () => getLogs(params), ...options });
};
