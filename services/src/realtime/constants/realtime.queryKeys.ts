import { ENDPOINTS } from './realtime.endpoint';
import {
  RealtimeParams,
  RealtimeInsightParams,
  RealtimeWifiSpeedtestParams,
  RealtimeWanStatisticsParams,
} from '../types/realtime.type';

export const QUERY_KEYS = {
  requestRealtimeId: (params: RealtimeParams) => [ENDPOINTS.REALTIME_REQUEST_ID, params],
  realtimeHealthcheck: (params: RealtimeParams) => [ENDPOINTS.REALTIME_HEALTH_CHECK, params],
  realtimeInsight: (params: RealtimeInsightParams) => [ENDPOINTS.REALTIME_INSIGHT, params],
  realtimeWifiSpeedtest: (params: RealtimeWifiSpeedtestParams) => [ENDPOINTS.REALTIME_WIFI_SPEED_TEST, params],
  realtimeWanStatistics: (params: RealtimeWanStatisticsParams) => [ENDPOINTS.REALTIME_WAN_STATISTICS, params],
  realtimeWanStatsDrawer: (params: RealtimeWanStatisticsParams) => [ENDPOINTS.REALTIME_WAN_STATS_DRAWER, params],
  realtimeStatus: (params: RealtimeParams) => [ENDPOINTS.REALTIME_STATUS, params],
};
