import { AxonQueryOptions, BFFResponseDTO } from '../../types';
import { useQuery } from '@tanstack/react-query';
import {
  RealtimeParams,
  RealtimeInsightParams,
  RealtimeOptions,
  IRealtimeStatus,
  RealtimeWifiSpeedtestParams,
  RealtimeWanStatisticsParams,
  TRealtimeMetric,
} from '../types/realtime.type';
import { QUERY_KEYS } from '../constants/realtime.queryKeys';
import {
  getRealtimeDataRequestId,
  getRealtimeDataStatus,
  getHealthcheckRealtime,
  getInsightRealtime,
  getWifiSpeedtestRealtime,
  getWanStatisticsRealtime,
  getWanStatsDrawerRealtime,
} from '../api/realtime.api';
import { IHealthCheck } from '@/types/healthcheck.types';
import get from 'lodash/get';
import { InsightReponse } from '@/insight/types/insightTypes';
import { useCallback } from 'react';
import { WifiStatisticsResponse } from '@/wifiStatistics/types/WifiStatistics';
import { IWanStatisticDrawerResponseData, IWanStatisticResponseData } from '../../cpe/wanStatistics/types';

export type RealtimeRequestStatus = 'PROCESSING' | 'SUCCESS' | 'FAILED';

export type StepState = 'INIT' | 'IN_PROGRESS' | 'SUCCESS' | 'ERROR' | 'CLEANUP';

type RealtimeCallback = (lineId: string, step: StepState, metaData: any) => void;

/**
 * Sets initial state for realtime process
 */
const setInitialRealtimeState = (lineId: string, metric: TRealtimeMetric[], callback: RealtimeCallback) => {
  callback(lineId, 'INIT', {
    metric,
  });
};

/**
 * Updates progress during realtime process
 */
const updateRealtimeProgress = (lineId: string, callback: RealtimeCallback) => {
  return (res: IRealtimeStatus) => {
    callback(lineId, 'IN_PROGRESS', res);
  };
};

/**
 *  Sets success state after realtime process completion
 */
const setRealtimeSuccessState = (lineId: string, requestId: string, callback: RealtimeCallback) => {
  callback(lineId, 'SUCCESS', {});
};

/**
 * Sets error state after realtime process failure
 */
const setRealtimeErrorState = (lineId: string, callback: RealtimeCallback) => {
  callback(lineId, 'ERROR', {});
};

/**
 * Cleans up realtime state
 */
const cleanupRealtimeState = (lineId: string, callback: RealtimeCallback) => {
  callback(lineId, 'CLEANUP', {});
};

export async function pollRealtimeProcess(
  lineId: string,
  requestId: string,
  callback: RealtimeCallback,
): Promise<{ status: RealtimeRequestStatus }> {
  try {
    const statusRes = await getRealtimeDataStatus(
      {
        lineId,
        realtimeRequestId: requestId,
      },
      updateRealtimeProgress(lineId, callback),
    );
    // Handle success
    const status = get(statusRes, 'data.status') ?? '';
    return {
      status: status === 'SUCCESS' ? 'SUCCESS' : 'FAILED',
    };
  } catch (error) {
    return {
      status: 'FAILED',
    };
  }
}

export async function startRealtimeProcess(
  params: RealtimeParams & RealtimeOptions,
  callback: RealtimeCallback,
  onFinish?: (status: RealtimeRequestStatus) => void,
) {
  let requestStatus: RealtimeRequestStatus = 'PROCESSING';
  const { lineId } = params;
  try {
    // Initialize state.
    setInitialRealtimeState(lineId, params.metric, callback);
    const requestIdRes = await getRealtimeDataRequestId(params);
    const requestId = get(requestIdRes, 'data.requestId');
    if (!requestId) {
      throw new Error('Realtime request failed');
    }
    callback(lineId, 'IN_PROGRESS', {
      realtimeRequestId: requestId,
    });
    const result = await pollRealtimeProcess(lineId, requestId, callback);
    requestStatus = result.status;
    if (requestStatus === 'SUCCESS') {
      setRealtimeSuccessState(lineId, requestId, callback);
    } else {
      throw new Error('Realtime request failed');
    }
  } catch (error) {
    requestStatus = 'FAILED';
    setRealtimeErrorState(lineId, callback);
  } finally {
    cleanupRealtimeState(lineId, callback);
    onFinish?.(requestStatus);
  }
}

/**
 * TRealtimeActionType is a type to distinguish whether user is action real-time at line level, wifi level, or wan level.
 */
export type TRealtimeActionType = 'line' | 'wifi' | 'wan';

export const useStartRealtimeProcess = () => {
  const startRealtimeProcess = useCallback(
    async (
      params: RealtimeParams & RealtimeOptions,
      callback: (lineId: string, step: StepState, metaData: any) => void,
      onFinish?: (status: RealtimeRequestStatus) => void,
    ) => {
      let requestStatus: RealtimeRequestStatus = 'PROCESSING';
      const lineId = params.lineId;
      try {
        // Initialize state.
        setInitialRealtimeState(lineId, params.metric, callback);
        // Get request ID and process.
        const requestIdRes = await getRealtimeDataRequestId(params);
        const requestId = get(requestIdRes, 'data.requestId') ?? '';
        const statusRes = await getRealtimeDataStatus(
          {
            lineId,
            realtimeRequestId: requestId,
          },
          updateRealtimeProgress(lineId, callback),
        );
        // Handle success
        const status = get(statusRes, 'data.status') ?? '';
        const isDataReady = status === 'SUCCESS';
        requestStatus = isDataReady ? 'SUCCESS' : 'FAILED';

        setRealtimeSuccessState(lineId, requestId, callback);
      } catch (error) {
        requestStatus = 'FAILED';
        setRealtimeErrorState(lineId, callback);
      } finally {
        cleanupRealtimeState(lineId, callback);
        onFinish?.(requestStatus);
      }
    },
    [],
  );
  return {
    startRealtimeProcess,
  };
};

export const useGetRealtimeHealthcheck = (
  params: Required<RealtimeParams>,
  options?: AxonQueryOptions<BFFResponseDTO<IHealthCheck>>,
) => {
  return useQuery({
    queryKey: QUERY_KEYS.realtimeHealthcheck(params),
    queryFn: () => getHealthcheckRealtime(params),
    enabled: Boolean(params.realtimeRequestId),
    retry: false,
    staleTime: Infinity,
    gcTime: Infinity,
    ...options,
  });
};

export const useGetRealtimeInsight = (
  params: RealtimeInsightParams,
  options?: AxonQueryOptions<BFFResponseDTO<InsightReponse>>,
) => {
  return useQuery({
    queryKey: QUERY_KEYS.realtimeInsight(params),
    queryFn: () => getInsightRealtime(params),
    enabled: Boolean(params.realtimeRequestId),
    staleTime: Infinity,
    gcTime: Infinity,
    retry: false,
    ...options,
  });
};

export const useGetRealtimeWifiSpeedtest = (
  params: RealtimeWifiSpeedtestParams,
  options?: AxonQueryOptions<BFFResponseDTO<WifiStatisticsResponse>>,
) => {
  return useQuery({
    queryKey: QUERY_KEYS.realtimeWifiSpeedtest(params),
    queryFn: () => getWifiSpeedtestRealtime(params),
    enabled: Boolean(params.realtimeRequestId),
    staleTime: Infinity,
    gcTime: Infinity,
    retry: false,
    ...options,
  });
};

export const useGetRealtimeWanStatistics = (
  params: RealtimeWanStatisticsParams,
  options?: AxonQueryOptions<BFFResponseDTO<IWanStatisticResponseData>>,
) => {
  return useQuery({
    queryKey: QUERY_KEYS.realtimeWanStatistics(params),
    queryFn: () => getWanStatisticsRealtime(params),
    enabled: Boolean(params.realtimeRequestId),
    staleTime: Infinity,
    gcTime: Infinity,
    retry: false,
    ...options,
  });
};

export const useGetRealtimeWanStatsDrawer = (
  params: RealtimeWanStatisticsParams,
  options?: AxonQueryOptions<BFFResponseDTO<IWanStatisticDrawerResponseData>>,
) => {
  return useQuery({
    queryKey: QUERY_KEYS.realtimeWanStatsDrawer(params),
    queryFn: () => getWanStatsDrawerRealtime(params),
    enabled: Boolean(params.realtimeRequestId),
    staleTime: Infinity,
    gcTime: Infinity,
    retry: false,
    ...options,
  });
};
