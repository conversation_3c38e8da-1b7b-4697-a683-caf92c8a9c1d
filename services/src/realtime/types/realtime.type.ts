import { IWanStatisticsQuery } from '../../cpe/wanStatistics/types';
import { WifiStatisticsParams } from '../../wifiStatistics/types/WifiStatistics';

export type TRealtimeMetric = 'broadbandSpeed' | 'operational' | 'wifiSpeed' | 'wifiScan';

export type RealtimeOptions = {
  metric: TRealtimeMetric[];
};

export type RealtimeParams = {
  lineId: string;
  realtimeRequestId?: string;
};

export interface IRealtimeRequestIdRes {
  requestId: string;
}

export interface IRealtimeStatus {
  status?: 'SUCCESS' | 'FAILED' | 'IN_PROGRESS';
  realtimeRequestFinished: boolean;
  realtimeRequestId: string;
  lastUpdated: number | null;
  currentStepId?: string;
  currentStepText?: string;
  progress?: number;
}

export type RealtimeMetric = 'healthCheck' | 'insight' | 'all';

export interface RealtimeInsightParams extends RealtimeParams {
  startDate: number;
  deviceId?: string;
  endDate: number;
}

export interface RealtimeWifiSpeedtestParams extends WifiStatisticsParams {
  realtimeRequestId: string;
}
export interface RealtimeWanStatisticsParams extends IWanStatisticsQuery {
  realtimeRequestId: string;
}
