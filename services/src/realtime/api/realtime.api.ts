import api from '@/api/apiService';
import { IHealthCheck } from '@/types/healthcheck.types';
import { BFFResponseDTO } from '@/types';
import { ENDPOINTS } from '../constants/realtime.endpoint';
import {
  IRealtimeRequestIdRes,
  RealtimeParams,
  IRealtimeStatus,
  RealtimeInsightParams,
  RealtimeWifiSpeedtestParams,
  RealtimeWanStatisticsParams,
  RealtimeOptions,
} from '../types/realtime.type';
import pollingRequest from '@/utils/polling.util';
import get from 'lodash/get';
import { InsightReponse } from '@/insight/types/insightTypes';
import { WifiStatisticsResponse } from '@/wifiStatistics/types/WifiStatistics';
import { IWanStatisticDrawerResponseData, IWanStatisticResponseData } from '@/cpe/wanStatistics/types';

export const getRealtimeDataRequestId = async (
  params: RealtimeParams & RealtimeOptions,
): Promise<BFFResponseDTO<IRealtimeRequestIdRes>> => {
  const { metric } = params;
  const metricParams = metric?.join(',') ?? '';
  const response = await api.get(ENDPOINTS.REALTIME_REQUEST_ID, {
    params: {
      ...params,
      metric: metricParams,
    },
  });
  return response.data;
};

export const getRealtimeDataStatus = async (
  params: Required<RealtimeParams>,
  callback: (config: IRealtimeStatus) => void,
): Promise<BFFResponseDTO<IRealtimeStatus>> => {
  const response = await pollingRequest<IRealtimeStatus>(
    {
      url: ENDPOINTS.REALTIME_STATUS,
      params,
    },
    {
      endCondition: (data: IRealtimeStatus) => {
        callback?.(data);
        return get(data, 'realtimeRequestFinished');
      },
    },
  );
  if (!response.data) {
    throw new Error('Healthcheck request failed');
  }
  return response.data;
};

export const getHealthcheckRealtime = async (
  params: Required<RealtimeParams>,
): Promise<BFFResponseDTO<IHealthCheck>> => {
  const response = await api.get(ENDPOINTS.REALTIME_HEALTH_CHECK, { params });

  return response.data;
};

export const getInsightRealtime = async (params: RealtimeInsightParams): Promise<BFFResponseDTO<InsightReponse>> => {
  const response = await api.get(ENDPOINTS.REALTIME_INSIGHT, { params });

  return response.data;
};

export const getWifiSpeedtestRealtime = async (
  params: RealtimeWifiSpeedtestParams,
): Promise<BFFResponseDTO<WifiStatisticsResponse>> => {
  const response = await api.get(ENDPOINTS.REALTIME_WIFI_SPEED_TEST, { params });

  return response.data;
};
export const getWanStatisticsRealtime = async (
  params: RealtimeWanStatisticsParams,
): Promise<BFFResponseDTO<IWanStatisticResponseData>> => {
  const response = await api.get(ENDPOINTS.REALTIME_WAN_STATISTICS, { params });

  return response.data;
};
export const getWanStatsDrawerRealtime = async (
  params: RealtimeWanStatisticsParams,
): Promise<BFFResponseDTO<IWanStatisticDrawerResponseData>> => {
  const response = await api.get(ENDPOINTS.REALTIME_WAN_STATS_DRAWER, { params });

  return response.data;
};
