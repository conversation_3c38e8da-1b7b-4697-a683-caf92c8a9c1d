import { useQuery } from '@tanstack/react-query';
import { AxonQueryOptions, BFFResponseDTO } from '../../types';
import { QUERY_KEYS } from '../constants/queryKeys';
import { GetLineInfo } from '../types';
import { getLineInfo } from '../apis/lineInfo.apis';

export const useGetLineInfo = (lineId: string, options?: AxonQueryOptions<BFFResponseDTO<GetLineInfo>>) => {
  return useQuery({
    queryKey: QUERY_KEYS.getLineInfo(lineId),
    queryFn: () => getLineInfo(lineId),
    staleTime: Infinity,
    gcTime: Infinity,
    ...options,
  });
};
