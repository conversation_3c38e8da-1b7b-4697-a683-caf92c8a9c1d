import { EQoeStatus, ConnectionType } from './index';

interface BaseOverview {
  stable: number | null;
  unstable: number | null;
  veryUnstable: number | null;
  disconnected: number | null;
}

interface IClient {
  deviceName: string;
  deviceType: string;
  deviceId: string;
  connectionType: string; // wifi, eth
  connectionInterface: string;
  connectionBand: string | null;
  stationMac: string;
  capabilities: string[];
  parentId: string;
  status: EQoeStatus;
  isOnline: boolean;
}

export interface ICpe {
  modelName: string;
  cpeType: string;
  cpeId: string;
  cpeStatus: EQoeStatus;
  installedTime: number | null;
  cpeFirmwareVersion: string | null;
  lastRebootTime: number | null;
  powerCycle: number | null;
  cpuDetection: number | null;
  freeMemoryDetection: number | null;
  cpuStatus: EQoeStatus;
  freeMemoryStatus: EQoeStatus;
  isGoldenFirmware: boolean;
  firmwareStatus: EQoeStatus;
}

export interface IHealthCheckParams {
  lineId: string;
  startDate: number;
  requestRealtimeId?: string;
}

export interface HealthCheckCpeResponse {
  results: ICpe[];
}

export interface HealthCheckClientResponse {
  results: IClient[];
}

export interface IConnection {
  status?: EQoeStatus; // only for wifi connection
  connectionType: ConnectionType;
  band?: string | null;
  name?: string | null;
  duplexMode?: string | null;
  isActive: boolean;
  networkType?: string;
}

export interface INetwork {
  modelName: string;
  cpeType: string;
  cpeId: string;
  connections: IConnection[];
}

export interface INetwork {
  modelName: string;
  cpeType: string;
  cpeId: string;
  connections: IConnection[];
}

export interface ILinkQuality {
  isAlive: boolean;
  status: EQoeStatus;
  lastBootTime: number | null;
  latency: number | null;
  latencyStatus: EQoeStatus;
  jitter: number | null;
  packetLoss?: number | null;
  downCount: number | null;
  errorRate: number | null;
  errorRateStatus: EQoeStatus;
}

export interface ISpeedTest {
  status: EQoeStatus;
  downloadSpeed: number | null;
  uploadSpeed: number | null;
  latency: number | null;
}

export interface IBroadBandTest {
  status: EQoeStatus;
}

export interface IInternetUsage {
  uploadUsage: number | null;
  downloadUsage: number | null;
}

export interface IWan {
  speedTest: ISpeedTest;
  broadbandTest: IBroadBandTest;
  linkQuality: ILinkQuality;
  internetUsage: IInternetUsage;
}

export interface IService {
  name: string;
  logo: string;
  status: EQoeStatus;
  latency: number | null;
  jitter: number | null;
  downloadSpeed: number | null;
  uploadSpeed: number | null;
  speedStatus: EQoeStatus;
  latencyStatus: EQoeStatus;
  trafficStatus: EQoeStatus;
  downloadTraffic: number | null;
  uploadTraffic: number | null;
}

export interface IHealthCheck {
  overview: {
    clients: BaseOverview;
    services: BaseOverview;
    networks: BaseOverview;
    wans: {
      status: EQoeStatus;
    };
    cpes: BaseOverview;
  };
  details: {
    clients: IClient[];
    services: IService[];
    networks: INetwork[];
    wans: IWan;
    cpes: ICpe[];
  };
  lastUpdatedTimestamp?: number;
  realtimeRequestFinished: boolean;
}

export interface IRealtimeHealthcheck {
  requestId: string;
}
