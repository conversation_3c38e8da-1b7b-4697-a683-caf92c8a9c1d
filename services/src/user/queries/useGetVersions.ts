import { AxonQueryOptions, BFFResponseDTO } from './../../types';
import { useQuery } from '@tanstack/react-query';
import { getVersions } from '../apis/versions';
import { queryKeys } from '../constants/queryKeys';
import { VersionResponse } from '../types/user.type';

export const useGetVersions = (option?: AxonQueryOptions<BFFResponseDTO<VersionResponse>>) => {
  return useQuery({
    queryKey: queryKeys.getVersions(),
    queryFn: () => getVersions(),
    ...option,
  });
};
