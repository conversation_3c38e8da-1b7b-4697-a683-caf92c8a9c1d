import { BFFResponseDTO } from '../types';
import api from '@/api/apiService';
import { ACTION_ENDPOINTS } from './action.constant';
import {
  PingTestRequest,
  PingTestDTO,
  RestartWifiInterfaceRequest,
  RestartWifiInterfaceResponse,
  RunManualLatencyTestResponse,
  RunManualLatencyTestParams,
  ResetClientParentalControlParams,
  ResetClientParentalControlResponse,
} from './action.type';

export const restartWifiInterface = async (
  requestBody: RestartWifiInterfaceRequest,
): Promise<BFFResponseDTO<RestartWifiInterfaceResponse>> => {
  const response = await api.post(ACTION_ENDPOINTS.RESTART_WIFI_INTERFACE, requestBody);
  return response.data;
};

export const triggerPingTest = async (requestBody: PingTestRequest): Promise<BFFResponseDTO<PingTestDTO>> => {
  const response = await api.post(ACTION_ENDPOINTS.TRIGGER_PING_TEST, requestBody);
  return response.data;
};

export const resetLineParentalControl = async (lineId: string): Promise<BFFResponseDTO<{ success: boolean }>> => {
  const response = await api.post(ACTION_ENDPOINTS.RESET_LINE_PARENTAL_CONTROL, {
    lineId,
  });
  return response.data;
};

export const resetClientParentalControl = async ({
  lineId,
  stationMacAddress,
}: ResetClientParentalControlParams): Promise<BFFResponseDTO<ResetClientParentalControlResponse>> => {
  const response = await api.post(ACTION_ENDPOINTS.CLIENT_RESET_PARENTAL_CONTROL, { lineId, stationMacAddress });
  return response.data;
};

export const runManualLatencyTest = async (
  params: RunManualLatencyTestParams,
): Promise<BFFResponseDTO<RunManualLatencyTestResponse>> => {
  const response = await api.post<BFFResponseDTO<RunManualLatencyTestResponse>>(
    ACTION_ENDPOINTS.RUN_MANUAL_LATENCY_TEST,
    params,
  );
  return response.data;
};
