export type RestartWifiInterfaceRequest = {
  bandId?: string;
  deviceId: string;
};

export type RestartWifiInterfaceResponse = {
  message: string;
};

export type PingTestRequest = {
  lineId: string;
  numOfPingRequests: number; // min 1 and max 30
} & ({ ipAddress: string; macAddress?: string } | { ipAddress?: string; macAddress: string });

export type PingTestDTO = {
  rawOutput: string[];
  minimumMs: number; // float
  maximumMs: number; // float
  averageMs: number; // float
  numberOfPacketsSent: number; // integer
  lostPacketsCount: number; // integer
  lostPacketsPercentage: number; // integer
  stationMac: string;
  stationIp: string;
  accessPointId: string;
};

export interface ResetClientParentalControlParams {
  lineId: string;
  stationMacAddress: string;
}

export interface ResetClientParentalControlResponse {
  success: boolean;
}

export interface RunManualLatencyTestParams {
  deviceId: string;
  url: string;
}

export interface RunManualLatencyTestResponse {
  url: string;
  ipAddress: string | null;
  connectTimeMs: number | null;
  startTransferTimeMs: number | null;
  error: string | null;
}
