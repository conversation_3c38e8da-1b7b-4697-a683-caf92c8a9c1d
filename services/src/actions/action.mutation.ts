import { BFFResponseDTO } from '@/types';
import { useMutation } from '@tanstack/react-query';
import { AxiosResponse } from 'axios';
import {
  resetClientParentalControl,
  resetLineParentalControl,
  restartWifiInterface,
  runManualLatencyTest,
  triggerPingTest,
} from './action.api';
import {
  PingTestRequest,
  RestartWifiInterfaceRequest,
  RunManualLatencyTestParams,
  ResetClientParentalControlParams,
  PingTestDTO,
} from './action.type';

export const useRestartWifiInterface = () => {
  return useMutation({
    mutationFn: (requestBody: RestartWifiInterfaceRequest) => restartWifiInterface(requestBody),
  });
};

export const usePingTest = () => {
  return useMutation<
    BFFResponseDTO<PingTestDTO>,
    AxiosResponse<{
      code: number;
      message: string;
      data: null;
    }>,
    PingTestRequest
  >({
    mutationFn: (requestBody: PingTestRequest) => triggerPingTest(requestBody),
  });
};

export const useResetLineParentalControl = () => {
  return useMutation({
    mutationFn: (lineId: string) => resetLineParentalControl(lineId),
  });
};

export const useResetClientParentalControl = () => {
  return useMutation({
    mutationFn: ({ lineId, stationMacAddress }: ResetClientParentalControlParams) =>
      resetClientParentalControl({ lineId, stationMacAddress }),
  });
};

export const useRunManualLatencyTest = () => {
  return useMutation({
    mutationFn: (params: RunManualLatencyTestParams) => runManualLatencyTest(params),
  });
};
