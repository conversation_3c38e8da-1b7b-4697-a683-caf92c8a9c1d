import { useQuery } from '@tanstack/react-query';
import { GetDataParams } from './getData.types';
import { getData } from './getData.apis';
import { AxonQueryOptions, BFFResponseDTO } from '../types';
import { GET_DATA_ENDPOINTS } from './getData.constants';

export const useGetData = <T extends object>(params: GetDataParams, options?: AxonQueryOptions<BFFResponseDTO<T>>) => {
  return useQuery({
    queryKey: [GET_DATA_ENDPOINTS.GET_DATA, params],
    queryFn: () => getData<T>(params),
    ...options,
  });
};
