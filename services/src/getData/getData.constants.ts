export const GET_DATA_ENDPOINTS = {
  GET_DATA: `/cc/v1/getData`,
};

export enum EDataSection {
  SIDEBAR = 'sidebar',
  EVENTS = 'events',
  INSIGHTS = 'insights',
  HEALTH_CHECK = 'health-check',
  CPE_STATISTICS = 'cpe-statistics',
  CPE_HISTORY = 'cpe-history',
  WIFI_STATISTICS = 'wifi-statistics',
  // LAN_PORTS = 'lan-ports',
  WIFI_BAND = 'wifi-band',
  LAN_WLAN_HISTORY = 'lan-wlan-history',
  WAN_CONFIG = 'wan-config',
  WAN_STATISTICS = 'wan-statistics',
  WAN_HISTORY = 'wan-history',
  CELLULAR_MODEM = 'cellular-modem',
  CLIENT_CONNECTION = 'client-connection',
  CLIENT_HISTORY = 'client-history',
  SERVICE_HISTORY = 'service-history',
}
