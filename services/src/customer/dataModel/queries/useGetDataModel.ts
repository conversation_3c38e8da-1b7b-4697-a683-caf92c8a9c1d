import { useQuery } from '@tanstack/react-query';
import { getDataModel } from '../apis/getDataModel';
import { QUERY_KEYS } from '../constants/queryKeys';
import { DataModelResponse } from '../type';
import { AxonQueryOptions, BFFResponseDTO } from './../../../types/index';

export const useGetDataModel = (customerId: string, options?: AxonQueryOptions<BFFResponseDTO<DataModelResponse>>) => {
  return useQuery({
    queryKey: QUERY_KEYS.getDataModel(customerId),
    queryFn: () => getDataModel(customerId),
    ...options,
  });
};
