import i18n from '@/i18n';

export function humanizeDuration(seconds: number): string {
  if (seconds < 60) return i18n.t('time_unit:humanizeTime.lessThanOneMinute');
  if (seconds < 120) return i18n.t('time_unit:humanizeTime.aboutOneMinute');
  if (seconds < 3600) return i18n.t('time_unit:humanizeTime.minutes', { count: Math.floor(seconds / 60) });
  if (seconds < 7200) return i18n.t('time_unit:humanizeTime.aboutOneHour');
  if (seconds < 86400) return i18n.t('time_unit:humanizeTime.hours', { count: Math.floor(seconds / 3600) });
  if (seconds < 172800) return i18n.t('time_unit:humanizeTime.aboutOneDay');
  if (seconds < 604800) return i18n.t('time_unit:humanizeTime.days', { count: Math.floor(seconds / 86400) });
  if (seconds < 1209600) return i18n.t('time_unit:humanizeTime.aboutOneWeek');
  if (seconds < 2419200) return i18n.t('time_unit:humanizeTime.weeks', { count: Math.floor(seconds / 604800) });
  if (seconds < 4838400) return i18n.t('time_unit:humanizeTime.aboutOneMonth');
  if (seconds < 29030400) return i18n.t('time_unit:humanizeTime.months', { count: Math.floor(seconds / 2419200) });
  if (seconds < 58060800) return i18n.t('time_unit:humanizeTime.aboutOneYear');
  return i18n.t('time_unit:humanizeTime.years', { count: Math.floor(seconds / 29030400) });
}
