/*
This function copied from https://github.com/JLarky/react-lazily
Because it's simple and over a year has no updates
so keep it in the project to easily maintain in the future
*/
import load from '@loadable/component';

/*
Example how to use loadable:
const { OneComponent, TwoComponent, ThreeComponent } = AXONLoad(() => import('./MyComponent'), {
  fallback: <div>Loading...</div>
});
return (
  <OneComponent />
  <TwoComponent />
  <ThreeComponent />
);
*/
export const AXONLoad = <T extends NonNullable<unknown>>(loader: () => Promise<T>, opts?: any) =>
  new Proxy({} as unknown as T, {
    get: (target, componentName: string | symbol) => {
      if (typeof componentName === 'string') {
        return load(() => loader().then((x) => x[componentName as keyof T]), opts);
      }
    },
  });
