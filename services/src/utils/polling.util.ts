import api from '@/api/apiService';
import { BFFResponseDTO } from '@/types';
import { AxiosRequestConfig } from 'axios';
import get from 'lodash/get';

type PollingRequestOptions = {
  endCondition: (data: any) => boolean;
  interval?: number;
  maxRetries?: number;
};

type PollingStatus = 'success' | 'error' | 'cancelled';

type PollingRequestResponse<T extends object> = {
  data: BFFResponseDTO<T> | null;
  status: PollingStatus;
};

export default async function pollingRequest<T extends object>(
  requestConfig: AxiosRequestConfig,
  options: PollingRequestOptions,
): Promise<PollingRequestResponse<T>> {
  try {
    const { endCondition, interval = 3000, maxRetries = 40 } = options;
    let retries = maxRetries;
    while (retries > 0) {
      const response = await api.request<BFFResponseDTO<T>>(requestConfig);
      const data = get(response, 'data.data');
      if (endCondition(data)) {
        return {
          data: response.data,
          status: 'success',
        };
      }
      await new Promise((resolve) => setTimeout(resolve, interval));
      retries--;
    }
    throw new Error('Polling request failed');
  } catch (error) {
    return {
      data: null,
      status: 'error',
    };
  }
}
