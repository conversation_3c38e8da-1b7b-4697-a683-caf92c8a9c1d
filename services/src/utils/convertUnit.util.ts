import { isNumber } from './number.util';

export enum SpeedMeasurementEnum {
  bit = 1,
  Kbps = 1000 * bit,
  Mbps = 1000 * Kbps,
  Gbps = 1000 * Mbps,
}

export const convertSpeedMeasurement = (
  input: number,
  from: SpeedMeasurementEnum,
  to: SpeedMeasurementEnum,
): number => {
  return input * (from / to);
};

export enum StorageMeasurementEnum {
  B = 1,
  bytes = 8 * B,
  KB = 1024 * bytes,
  MB = 1024 * KB,
  GB = 1024 * MB,
}

export const convertStorageMeasurement = (
  input: number,
  from: StorageMeasurementEnum,
  to: StorageMeasurementEnum,
): number | null => {
  if (!isNumber(input)) {
    return null;
  }
  return input * (from / to);
};
