import { useQuery, UseQueryOptions } from '@tanstack/react-query';
import { QUERY_KEYS } from '../constants/queryKeys';
import {
  WifiStatisticsParams,
  WifiStatisticsResponse,
  WifiStatsDrawerParams,
  WifiStatsDrawerResponse,
} from '../types/WifiStatistics';
import { getWifiStatistics, getWifiStatsDrawer } from '../apis/getWifiStatistics';

export const useGetWifiStatistics = (
  params: WifiStatisticsParams,
  options?: Omit<UseQueryOptions<WifiStatisticsResponse>, 'queryKey' | 'queryFn'>,
) => {
  return useQuery({
    queryKey: QUERY_KEYS.getWifiStatistics(params),
    queryFn: () => getWifiStatistics(params),
    staleTime: Infinity,
    gcTime: Infinity,
    retry: false,
    ...options,
  });
};
export const useGetWifiStatsDrawer = (
  params: WifiStatsDrawerParams,
  options?: Omit<UseQueryOptions<WifiStatsDrawerResponse>, 'queryKey' | 'queryFn'>,
) => {
  return useQuery({
    queryKey: QUERY_KEYS.getWifiStatisticsDrawer(params),
    queryFn: () => getWifiStatsDrawer(params),
    staleTime: Infinity,
    gcTime: Infinity,
    retry: false,
    ...options,
  });
};
