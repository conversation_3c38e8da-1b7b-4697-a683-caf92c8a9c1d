import { ServiceParams, ServiceStatisticsResponse, ServiceSummaryResponse } from '@/service/types/service.type';
import { BFFResponseDTO } from './../../types';
import api from '@/api/apiService';
import { ENDPOINTS } from '@/service/constants/endpoints';

export const getServiceSummary = async (params: ServiceParams): Promise<BFFResponseDTO<ServiceSummaryResponse>> => {
  const response = await api.get(ENDPOINTS.SERVICE_SUMMARY, { params });
  return response.data;
};
export const getServiceStats = async (params: ServiceParams): Promise<BFFResponseDTO<ServiceStatisticsResponse>> => {
  const response = await api.get(ENDPOINTS.SERVICE_STATS, { params });
  return response.data;
};
