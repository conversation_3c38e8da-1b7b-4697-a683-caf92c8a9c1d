import { EQoeStatus } from '@/types';

export type ServiceParams = {
  customerId: string;
  startDate: number;
  endDate?: number;
};

export interface SlaComparison {
  value: string | null;
  comparedTo: string | null;
}

export interface MetricItem {
  type: string | null;
  unit: string | null;
  minValue: number | null;
  maxValue: number | null;
  percentile60: number | null;
  latestResult: number | null;
  slaComparison: SlaComparison | null;
  averageValue: number | null;
}

export interface SpeedSummary {
  dn: number | null;
  up: number | null;
  latency: number | null;
  jitter: number | null;
}

export interface ServiceSummary {
  serviceName: string | null;
  status: string | null;
  type: 'Broadband' | 'Streaming' | 'VoIP' | string | null;
  activeSince: number | null;
  interfaceName: string | null;
  vlanId: string | null;
  speedSummary: SpeedSummary | null;
  logo: string | null;
  lastTested: string | null;
}

export interface ServiceSummaryResponse {
  results: ServiceSummary[];
}
interface ServiceWeeklyTrend {
  status: EQoeStatus;
  date: number;
}
export type ServiceStatistics = {
  serviceId: string;
  serviceType: string;
  serviceValue: number | null;
  serviceMinValue: number | null;
  serviceMaxValue: number | null;
  serviceAverage: number | null;
  service60thPercentile: number | null;
  serviceLatestResult: number | string | null;
  serviceUnit: string | null;
  serviceLastDayValue: number | null;
  weeklyTrend: ServiceWeeklyTrend[] | null;
};
export interface ServiceStatisticsResponse {
  results: ServiceStatistics[];
}
