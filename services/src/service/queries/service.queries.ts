import { useQuery } from '@tanstack/react-query';
import { ServiceParams, ServiceStatisticsResponse, ServiceSummaryResponse } from '../types/service.type';
import { AxonQueryOptions, BFFResponseDTO } from '../../types';
import { QUERY_KEYS } from '../constants/queryKeys';
import { getServiceStats, getServiceSummary } from '../apis/service.api';

export const useGetServiceSummary = (
  params: ServiceParams,
  options?: AxonQueryOptions<BFFResponseDTO<ServiceSummaryResponse>>,
) => {
  return useQuery({
    queryKey: QUERY_KEYS.SERVICE_SUMMARY(params),
    queryFn: () => getServiceSummary(params),
    ...options,
  });
};
export const useGetServiceStats = (
  params: ServiceParams,
  options?: AxonQueryOptions<BFFResponseDTO<ServiceStatisticsResponse>>,
) => {
  return useQuery({
    queryKey: QUERY_KEYS.SERVICE_STATS(params),
    queryFn: () => getServiceStats(params),
    ...options,
  });
};
