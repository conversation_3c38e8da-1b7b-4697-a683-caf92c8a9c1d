import { useQuery } from '@tanstack/react-query';
import { getClientHistory, getClientStatisticsTableDrawer } from '../apis/client.api';
import { QUERY_KEYS } from '../constants/queryKeys';
import {
  ClientHistoryParams,
  ClientHistoryResponse,
  ClientStatisticsTableDrawerParams,
  ClientStatisticsTableDrawerResponse,
} from '../types/client.type';
import { AxonQueryOptions, BFFResponseDTO } from './../../types';

export const useGetClientHistory = (
  params: ClientHistoryParams,
  options?: AxonQueryOptions<BFFResponseDTO<ClientHistoryResponse>>,
) => {
  return useQuery({
    queryKey: QUERY_KEYS.CLIENT_HISTORY(params),
    queryFn: () => getClientHistory(params),
    ...options,
  });
};

export const useGetClientStatisticsTableDrawer = (
  params: ClientStatisticsTableDrawerParams,
  options?: AxonQueryOptions<BFFResponseDTO<ClientStatisticsTableDrawerResponse>>,
) => {
  return useQuery({
    queryKey: QUERY_KEYS.CLIENT_STATISTICS_TABLE_DRAWER(params),
    queryFn: () => getClientStatisticsTableDrawer(params),
    ...options,
  });
};
