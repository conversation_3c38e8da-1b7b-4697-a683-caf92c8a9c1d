import api from '@/api/apiService';
import { ENDPOINTS } from '../constants/endpoints';
import {
  ClientConnectionResponse,
  ClientHistoryParams,
  ClientHistoryResponse,
  ClientStatisticsTableDrawerResponse,
} from '../types/client.type';
import { BFFResponseDTO } from './../../types';

export const getClientHistory = async (params: ClientHistoryParams): Promise<BFFResponseDTO<ClientHistoryResponse>> => {
  const response = await api.get(ENDPOINTS.CLIENT_HISTORY, { params });
  return response.data;
};

export const getClientConnection = async (
  params: ClientHistoryParams,
): Promise<BFFResponseDTO<ClientConnectionResponse>> => {
  const response = await api.get(ENDPOINTS.CLIENT_CONNECTION, { params });
  return response.data;
};

export const getClientStatisticsTableDrawer = async (
  params: ClientHistoryParams,
): Promise<BFFResponseDTO<ClientStatisticsTableDrawerResponse>> => {
  const response = await api.get(ENDPOINTS.CLIENT_STATISTICS_TABLE_DRAWER, { params });
  return response.data;
};
