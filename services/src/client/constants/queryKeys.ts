import { ClientHistoryParams } from '../types/client.type';
import { ENDPOINTS } from './endpoints';

export const QUERY_KEYS = {
  CLIENT_HISTORY: (params: ClientHistoryParams) => [ENDPOINTS.CLIENT_HISTORY, params],
  CLIENT_CONNECTION: (params: ClientHistoryParams) => [ENDPOINTS.CLIENT_CONNECTION, params],
  CLIENT_STATISTICS_TABLE_DRAWER: (params: ClientHistoryParams) => [ENDPOINTS.CLIENT_STATISTICS_TABLE_DRAWER, params],
};
