import { useQuery } from '@tanstack/react-query';
import { getTopologyHistory } from '../api/getTopologyHistory';
import { queryKeys } from '../queryKeys';
import { IGetTopologyHistoryParams, ITopologyHistory } from '../types/NetworkTopology';
import { AxonQueryOptions } from './../../types/index';

export const useGetTopologyHistory = (
  { deviceId, startDate, endDate }: IGetTopologyHistoryParams,
  option?: AxonQueryOptions<ITopologyHistory | null>,
) => {
  return useQuery({
    queryKey: queryKeys.useGetTopologyHistory({ deviceId, startDate, endDate }),
    queryFn: async (): Promise<ITopologyHistory | null> => {
      const res = await getTopologyHistory({ deviceId, startDate, endDate });
      return res.data;
    },
    ...option,
  });
};
