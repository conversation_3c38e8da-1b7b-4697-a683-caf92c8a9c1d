import api from '@/api/apiService';
import { ENDPOINTS } from '../constants/endpoints';
import { IGetTopologyHistoryParams, ITopologyHistory } from '../types/NetworkTopology';
import { BFFResponseDTO } from '@/types';

export async function getTopologyHistory(
  params?: IGetTopologyHistoryParams,
): Promise<BFFResponseDTO<ITopologyHistory>> {
  const res = await api.get(`${ENDPOINTS.TOPOLOGY_HISTORY}`, {
    params: params,
  });
  return res.data;
}
