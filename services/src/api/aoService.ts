import axios, { AxiosResponse, InternalAxiosRequestConfig } from 'axios';

const apiUrl =
  process.env.BACKEND_AO_URL && process.env.BACKEND_AO_URL !== ''
    ? process.env.BACKEND_AO_URL
    : '<REPLACE_WITH_BACKEND_AO_URL>';

const api = axios.create({
  baseURL: apiUrl,
});

export const retryDelay = () => {
  return 1000 * Math.random();
};

export const onRequestFulfilled = (config: InternalAxiosRequestConfig) => {
  const token = localStorage.getItem('accessToken');
  if (token && config.headers && !config.headers.Authorization) {
    config.headers.authorization = `Bearer ${token}`;
  }
  if (config.headers && config.headers.newApi) {
    delete config.headers.newApi;
  }
  config.withCredentials = true;
  config.headers['Access-Control-Allow-Methods'] = 'GET, POST, DELETE, OPTIONS, HEAD';
  config.headers['Content-Type'] = 'application/json';
  return config;
};

export const onResponseFulfilled = (response: AxiosResponse) => {
  if (response.status === 401) {
    localStorage.removeItem('lastTokenCheck');
  }

  // const responseContentType = response.headers ? response.headers['content-type'] : null

  // const authExpired =
  //   typeof response.data === 'string' &&
  //   response.data.includes('kc-button-login') &&
  //   responseContentType.includes('text/html')
  // if (authExpired && process.env.NODE_ENV !== 'development')
  //   window.location.replace(process.env.REACT_APP_LOGOUT_URL as string)

  return response;
};

api.interceptors.request.use(onRequestFulfilled);

api.interceptors.response.use(onResponseFulfilled);

export default api;
