import { useQuery } from '@tanstack/react-query';
import { AxonQueryOptions, BFFResponseDTO } from '../../../types';
import { QUERY_KEYS } from '../constants/queryKeys';
import { getCellularModemParameters } from '../apis/getCellularModemParameters';
import { CellularModemParamRequest, CellularModemParamResponse } from '../types';

export const useGetCellularModemParameters = (
  params: CellularModemParamRequest,
  options?: AxonQueryOptions<BFFResponseDTO<CellularModemParamResponse>>,
) => {
  return useQuery({
    queryKey: QUERY_KEYS.CELLULAR_MODEM(params),
    queryFn: () => getCellularModemParameters(params),
    ...options,
  });
};
