import { useQuery } from '@tanstack/react-query';
import { AxonQueryOptions, BFFResponseDTO } from '../../../types';
import { getCellularModemDetails } from '../apis/getCellularModemDetails';
import { QUERY_KEYS } from '../constants/queryKeys';
import { CellularModemDetailsParamRequest, CellularModemDetailsReponse } from '../types';

export const useGetCellularModemDetails = (
  params: CellularModemDetailsParamRequest,
  options?: AxonQueryOptions<BFFResponseDTO<CellularModemDetailsReponse>>,
) => {
  return useQuery({
    queryKey: QUERY_KEYS.CELLULAR_MODEM_DETAILS(params),
    queryFn: () => getCellularModemDetails(params),
    ...options,
  });
};
