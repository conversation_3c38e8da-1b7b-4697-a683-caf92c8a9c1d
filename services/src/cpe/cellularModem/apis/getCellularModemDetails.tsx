import api from '@/api/apiService';
import { BFFResponseDTO } from '../../../types';
import { ENDPOINTS } from '../constants/endpoints';
import { CellularModemDetailsParamRequest, CellularModemDetailsReponse } from '../types';

export const getCellularModemDetails = async (
  params: CellularModemDetailsParamRequest,
): Promise<BFFResponseDTO<CellularModemDetailsReponse>> => {
  const res = await api.get(ENDPOINTS.CELLULAR_MODEM_DETAILS, {
    params,
  });
  return res.data;
};
