import api from '@/api/apiService';
import { BFFResponseDTO } from '../../../types';
import { ENDPOINTS } from '../constants/endpoints';
import { CellularModemParamRequest, CellularModemParamResponse } from '../types';

export const getCellularModemParameters = async (
  params: CellularModemParamRequest,
): Promise<BFFResponseDTO<CellularModemParamResponse>> => {
  const res = await api.get(ENDPOINTS.CELLULAR_MODEM_PARAMETERS, { params });
  return res.data;
};
