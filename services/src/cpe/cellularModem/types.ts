export type CellularModemParamRequest = {
  customerId: string;
  deviceId: string;
  startDate: number;
  endDate?: number;
};

export interface CellularModemParamResponse {
  parameters: Parameter[];
  infos: Infos;
}

interface Infos {
  status?: string;
  serviceStatus?: string;
  standard?: string;
  lastUpdated?: number;
}

interface Parameter {
  type?: string;
  value?: number;
  unit?: string;
  status?: null;
}

export type CellularModemDetailsReponse = {
  status: string | null;
  currentType: string | null;
  supportedType: string | null;
};

export type CellularModemDetailsParamRequest = {
  deviceId: string;
};
