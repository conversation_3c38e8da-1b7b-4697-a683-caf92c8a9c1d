import api from '@/api/apiService';
import { BFFResponseDTO } from '@/types';
import { ENDPOINTS } from '../constants/endpoints';
import { IWanStatisticDrawerResponseData, IWanStatisticsQuery } from '../types';

export const getWanStatisticsDrawer = async ({
  deviceId,
  startDate,
  endDate,
}: IWanStatisticsQuery): Promise<BFFResponseDTO<IWanStatisticDrawerResponseData>> => {
  const res = await api.get(ENDPOINTS.WAN_STATISTICS_DRAWER, {
    params: {
      deviceId,
      startDate,
      endDate,
    },
  });
  return res.data;
};
