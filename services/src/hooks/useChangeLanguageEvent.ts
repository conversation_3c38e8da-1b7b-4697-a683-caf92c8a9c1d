import { useEffect } from 'react';
import { CHANGE_LANGUAGE_EVENT } from '../constants';

export const useChangeLanguageEvent = (i18nInstance) => {
  useEffect(() => {
    const handler = (e: CustomEvent<{ language: string }>) => {
      const lang = e.detail.language;
      i18nInstance.changeLanguage(lang);
    };

    window.addEventListener(CHANGE_LANGUAGE_EVENT, handler as EventListener);
    return () => {
      window.removeEventListener(CHANGE_LANGUAGE_EVENT, handler as EventListener);
    };
  }, [i18nInstance]);
};
