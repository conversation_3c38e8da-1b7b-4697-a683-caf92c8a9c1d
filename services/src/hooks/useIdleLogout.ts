import { useLogout } from '../user';
import { useIdleTimer } from 'react-idle-timer';
import { useNavigate } from 'react-router-dom';

const TIMEOUT =
  process.env.USER_TIMEOUT && process.env.USER_TIMEOUT !== ''
    ? process.env.USER_TIMEOUT
    : '<REPLACE_WITH_USER_TIMEOUT>'; // in minutes

export const useIdleLogout = () => {
  const navigate = useNavigate();
  const userTimeout = parseInt(TIMEOUT) * 60 * 1000;
  const { mutate: logout } = useLogout({
    onSettled: () => {
      localStorage.removeItem('accessToken');
      localStorage.removeItem('remember');
      navigate('/');
    },
  });

  useIdleTimer({
    timeout: userTimeout,
    onIdle: () => logout(),
    debounce: 1000,
    disabled: process.env.NODE_ENV !== 'production' || localStorage.getItem('remember') === 'true',
  });
};
