{"since": "seit", "noStatusData": "<PERSON><PERSON> verfügbar", "deviceImageAlt": "Gerätebild", "deviceInfo": {"seeCPEStats": "CPE-Statistiken anzeigen", "cellularModem": "Mobilfunkmodem", "action": {"actionButton": "Aktion", "confirmText": "Bestätigen", "cancelText": "Abbrechen", "testing": "Testing...", "close": "<PERSON><PERSON><PERSON> en", "back": "Zurück", "viewLog": "<PERSON><PERSON> an<PERSON><PERSON>", "dialogDescription": "Sind <PERSON> sicher, dass Sie diese Aktion auf dem Gerät „{{deviceId}}“ durchführen möchten?", "notAllowedActionTooltip": "Nicht erlaubt aufgrund fehlender Benutzerberechtigungen oder fehlender Geräteunterstützung für Remoteaktionen.", "cpe": {"title": "CPE", "deviceReboot": "Gerät neu starten", "environmentReboot": "Umfeld neu starten", "autoRebootConfig": "Auto-Neustart-Konfiguration", "factoryReset": "Werkseinstellungen zurücksetzen", "parentalControlReset": "Kindersicherung zurücksetzen", "tr069ProcessRestart": "TR069-<PERSON><PERSON><PERSON> neu starten", "firmwareUpdate": {"title": "Firmware-Update", "dialog": {"title": "Firmware aktualisieren ({{deviceId}})", "description": "Wählen Sie die neue Firmware zur Installation aus.", "currentVersion": "Aktuelle Version", "availableVersion": "Verfügbare Version", "updateText": "Update", "confirmUpdateText": "Möchten Sie die Firmware wirklich aktualisieren?"}}, "cpeConfigBackup": "CPE-Konfigurations-Backup", "cpeConfigRestore": "CPE-Konfigurations-Wiederherstellung", "downloadDeviceLog": "Ger<PERSON>ep<PERSON><PERSON><PERSON>"}, "wifi": {"title": "WIFI", "wifiConfig": "WI-FI-Konfiguration", "optimizeWifiChannels": "Wi-Fi-Kanäle optimieren", "restartWifiDriver": "Wi-Fi-Treiber neu starten", "restartThisInterface": "<PERSON><PERSON> neu starten", "restartWifiInterface": {"title": "Wi-Fi-Schnittstelle neu starten", "dialog": {"title": "Wi-Fi-Schnittstelle neu starten", "description": "Wählen Sie das Band zum Neustart aus.", "wifiBand": "Wi-Fi-Band", "restartButton": "<PERSON>eu starten", "restartTitle": "Wi-Fi-Schnittstelle neu starten", "restartConfirmText": "Sind <PERSON> sicher, dass Sie diese Schnittstelle zurücksetzen möchten?"}}}, "wan": {"title": "WAN", "restartWanInterface": "WAN-Schnittstelle neu starten", "restartPpp": "PPP neu starten", "pushPppConfig": {"title": "PPP-Konfiguration übertragen", "dialog": {"title": "PPP-Konfiguration übertragen ({{deviceId}})", "description": "Geben Sie Optionen für die Übertragung der PPP-Konfiguration ein.", "username": "<PERSON><PERSON><PERSON><PERSON>", "password": "Passwort", "protocolType": "Protokolltyp", "pppoe": "PPPoE (Standard)", "serviceName": "Dienstname (Optional)", "serviceNamePlaceHolder": "Dienstname", "mtuSize": "MTU-Größe (Optional)", "mtuSizePlaceHolder": "MTU", "authMethod": "Authentifizierungsmethode", "pap": "PAP (auto)", "connectionMode": "<PERSON>er<PERSON><PERSON>ng<PERSON><PERSON>", "alwaysOn": "Immer an", "ipMode": "IP-Modus", "dynamic": "<PERSON><PERSON><PERSON><PERSON> (Standard)"}}}, "message": {"restartWifiInterface": {"success": "Wi-Fi-Schnittstelle erfolgreich neu gestartet", "error": "Fehler beim Neustart der Wi-Fi-Schnittstelle"}, "pingTest": {"success": "Ping-<PERSON> er<PERSON><PERSON><PERSON><PERSON><PERSON>", "error": "<PERSON><PERSON> be<PERSON>", "defaultError": "Etwas ist schiefgelaufen (Code: {{errorCode}})"}}, "pingTest": {"title": "Ping-Test", "dialog": {"title": "Client-Ping-<PERSON>", "description": "Legen Sie die Parameter fest, mit denen Sie den Test durchführen möchten", "numOfPingRequests": "<PERSON><PERSON><PERSON> der Ping-Anfragen", "numOfPingRequestsWarning": "Bitte geben Sie eine Nummer zwischen 1 und 15 ein.", "macAddress": "MAC-Adresse", "ipAddress": "IP-Adresse", "clientName": "Client-Name", "runButton": "Ausführen", "result": {"title": {"success": "Test erfol<PERSON><PERSON>ich", "error": "Test fehlgeschlagen", "failToStartTest": "Test konnte nicht gestartet werden"}, "connectedTime": "Verbindungszeit", "startTransferTime": "Start-Übertragungszeit"}, "packets": "Pakete:", "sent": "Gesendet", "received": "Empfangen", "lost": "Verloren", "approximateRoundTripTime": "Näherungsweise RTT-Zeiten", "min": "Min", "max": "Max", "avg": "Durchschnitt"}}}, "detail": {"cellularModem": {"currentType": "Aktueller Typ", "supportedType": "Unterstützter Typ"}, "deviceInfo": {"details": {"moreDetails": "Mehr Details", "deviceDetails": "G<PERSON><PERSON><PERSON><PERSON>", "seeStatistics": "Statistiken anzeigen", "interface": "Schnittstelle", "type": "<PERSON><PERSON>", "id": "ID", "id2": "ID #2", "model": "<PERSON><PERSON>", "firmware": "Firmware", "agentVer": "Agenten-Version", "publicIp": "Öffentliche IP", "lanIp": "LAN-IP", "mgmtIp": "Mgmt IP", "ipv6": "IPv6", "macAddress": "MAC-Adresse", "lastReboot": "Letzter Neustart", "lastUpload": "Letzter Upload", "configurationGroups": "Konfigurationsgruppen"}}}, "cpeStats": {"title": "CPE-Statistiken -", "searchInputPlaceholder": "<PERSON><PERSON>", "table": {"header": {"type": "<PERSON><PERSON>", "minMax": "Min-<PERSON>", "average": "Durchschnitt", "latestResult": "<PERSON><PERSON><PERSON><PERSON>", "noOfTests": "<PERSON><PERSON><PERSON>", "weeklyTrend": "Wöchentlicher Trend"}, "body": {"warning": "<PERSON><PERSON><PERSON>", "tooHigh": "zu hoch"}}}}, "cpe": {"title": "CPE", "cpeStats": {"title": "CPE-Statistiken"}, "cpeHistory": {"title": "CPE<PERSON><PERSON><PERSON><PERSON><PERSON>", "metric": {"title": "<PERSON><PERSON>", "qoe": {"title": "QoE", "yLabel": "AP-Gesundheit QoE"}, "cpuAndMemory": {"title": "CPU- und Speichernutzung", "cpuUsage": "CPU-Auslastung (%)", "cpuLoad": "CPU-Last", "freeMemory": "<PERSON><PERSON><PERSON> (MB)", "cpuUsageTooltip": "CPU-Auslastung", "cpuLoadTooltip": "CPU-Last", "freeMemoryTooltip": "<PERSON><PERSON><PERSON>"}, "chipsetTemp": {"title": "Temperatur", "yLabel": "Temperatur ({{unit}})"}, "powerCycle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rssi": "Wi-Fi-Backhaul RSSI", "disconnectionEvent": "Wi-Fi-Backhaul-Trennungen pro Stunde"}}}, "lanWlan": {"title": "LAN/WLAN", "wifiStats": {"title": "Wi-Fi-Statistiken", "selectBand": "Band auswählen", "runSpeedTest": {"button": "Geschwindigkeitstest durchführen", "modal": {"title": "Wi-Fi-Geschwindigkeitstest", "description": "<PERSON><PERSON><PERSON>en Sie die Parameter aus, die Si<PERSON> testen möchten.", "speed": "Geschwindigkeit", "latency": "<PERSON><PERSON>", "congestion": "Überlastung"}}, "speed": "Geschwindigkeit", "latency": "<PERSON><PERSON>", "jitter": "Jitter (Up/Dn)", "congestion": "Überlastung", "traffic": "Datenverkehr (Up/Dn)", "transmission": "Übertragungsfehler (Tx/Rx)", "interferenceAndNoise": "Kanal-Interferenz/Rauschen", "temperature": "Chipsatz-Temperatur", "trafficUp": "Datenverkehr (Up)", "trafficDown": "Datenverkehr (Down)", "interferenceScore": "Interferenz-Score", "noise": "<PERSON><PERSON><PERSON>", "txStatus": "Tx-Status", "rxStatus": "Rx-Status"}, "lanPorts": "LAN-Ports", "wifiBands": "Wi-Fi-<PERSON><PERSON><PERSON>"}, "lanWLANHistory": {"title": "LAN/WLAN-<PERSON><PERSON><PERSON><PERSON>", "selectInterface": "Schnittstelle auswählen", "wifiQoE": {"wifiQoE": "Wi-Fi QoE", "channel": "<PERSON><PERSON>", "connectivity": "Konnektivität", "rssi": "RSSI", "speed": "Wi-Fi-Geschwindigkeit", "latency": "Wi-Fi-Latenz", "jitter": "Wi-Fi-Jitter", "transmissionErrors": "Wi-Fi-Übertragungsfehler", "channelNoise": "Wi-Fi-Ka<PERSON><PERSON>chen", "chipsetTemperature": "Wi-Fi-Chipsatztemperatur", "wifiTraffic": "Wi-Fi-Verkehr", "ethernetTraffic": "Ethernet-Verkehr", "channelInterference": "Wi-Fi-Kanalstörungen"}}, "wanHistory": {"options": {"qoe": "Breitband-QoE", "status": "Status", "packetLost": "Paketverlust", "stability": "Stabilität", "speed": "Geschwindigkeit", "latency": "<PERSON><PERSON>", "jitter": "Jitter", "traffic": "Datenverkehr", "transmissionError": "Übertragungsfehler", "lteCellId": "Neuester LTE Cell-ID-Wert", "lteBand": "Neuester LTE-Band-Wert", "5gBand": "Neuester 5G-Band-Wert", "voiceRedundancy": "Neuester Sprachredundanzwert"}, "chart": {"qoe": "Breitband-QoE", "status": "Status", "linkStatus": "<PERSON>er<PERSON><PERSON>ng<PERSON><PERSON>", "upSpeed": "Upload-Geschwindigkeit", "downSpeed": "Download-Geschwindigkeit", "upTraffic": "Upload-Verkehr", "downTraffic": "Download-Verkehr", "latency": "<PERSON><PERSON>", "transmissionError": "Übertragungsfehler", "packetLost": "Paketverlust", "jitter": "Jitter", "cellId": "Cell Id", "band": "Band", "voiceRedundancy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "eventType": "Ereignistyp", "down": "Unten", "up": "<PERSON><PERSON>"}}, "serviceHistory": {"speed": "Internetgeschwindigkeit", "latency": "Internetlatenz", "jitter": "Internet-Jitter", "traffic": "Internetverkehr", "upSpeed": "Internet-Upload-Geschwindigkeit", "downSpeed": "Internet-Download-Geschwindigkeit", "upTraffic": "Internet-Upload-Verkehr", "downTraffic": "Internet-Download-Verkehr", "jitterFirst": "Internet-Jitter0", "jitterSecond": "Internet-Jitter1"}, "clientHistory": {"title": "Client<PERSON><PERSON><PERSON><PERSON><PERSON>", "selectBandPlacehoder": "Wähle eine Band", "selectBand": "Band auswählen", "selectInterface": "Schnittstelle auswählen", "selectCpe": "CPE auswählen", "selectClient": "Client auswählen", "selectAll": "Alle auswählen", "wifiThroughput": {"selectMetric": "<PERSON><PERSON>", "qoe": "QoE", "wifiPhyRate": "Wi-Fi Phy-Rate", "wifiThroughput": "Wi-Fi-Durchsatz", "latency": "<PERSON><PERSON>", "trafficDown": "Datenverkehr Downlink", "trafficUp": "Datenverkehr Uplink", "signalToNoiseRatio": "Signal-Rausch-Verhältnis", "rssiLevel": "RSSI-Pegel"}, "chart": {"up": "Upload-Verkehr", "down": "Download-Verkehr", "latency": "<PERSON><PERSON>", "jitterFirst": "Jitter0", "jitterSecond": "Jitter1"}}, "cellularModem": {"title": "Mobilfunkmodem", "settings": {"title": "Einstellungen", "general": "Allgemein", "roaming": "Roaming", "autoSelectNetwork": "Netzwerk automatisch auswählen", "requestedNetwork": "Angefordertes Netzwerk", "tmobile": "T-Mobile", "vodafone": "Vodafone", "gigaclear": "G<PERSON><PERSON><PERSON>", "simPinCheckMethod": "SIM-PIN-Prüfmethode", "onNetworkAccess": "Bei Netzwerkzugriff", "changeSimPin": "SIM-PIN <PERSON>"}, "moreDetails": {"title": "<PERSON><PERSON><PERSON> Details", "details": "Details", "interface": "Schnittstelle"}, "helpDrawer": {"title": "Was ist ein Mobilfunkmodem?", "description": "Das Mobilfunkmodem-Widget zeigt den aktuellen Betriebskanal zusammen mit der aktuellen Betriebsbandbreite an. Eine höhere Bandbreite ist in Abwesenheit einer starken externen Kanalauslastung typischerweise wünschenswert.", "descriptionRSSI": "Empfangssignalstärkeindikator: Der Träger-RSSI (Empfangssignalstärkeindikator) misst die durchschnittliche Gesamtempfangsleistung, die nur in OFDM-Symbolen beobachtet wird, die Referenzsymbole für Antennenport 0 enthalten (d.h. OFDM-Symbol 0 & 4 in einem Slot) in der Messbandbreite über N Ressourcenblöcke. Die Gesamtempfangsleistung des Träger-RSSI umfasst die Leistung von Co-Channel-Serving- und Non-Serving-Zellen, Nachbarkanalinterferenzen, thermisches Rauschen usw. Insgesamt gemessen über 12 Subträger einschließlich RS von der Serving-Zelle, Verkehr in der Serving-Zelle.", "descriptionRSRP": "Referenzsignal-Empfangsleistung: RSRP ist eine Art RSSI-Messung, wie folgt gibt es einige Definitionen und einige Details dazu. Es ist die Leistung der LTE-Referenzsignale, die über die gesamte Bandbreite und Schmalband verteilt sind. Ein Minimum von -20 dB SINR (des S-Synch-Kanals) ist erforderlich, um RSRP/RSRQ zu erkennen.", "descriptionRSRQ": "Referenzsignal-Empfangsqualität: Qualität unter Berücksichtigung von RSSI und der Anzahl der verwendeten Ressourcenblöcke (N) RSRQ = (N * RSRP) / RSSI gemessen über dieselbe Bandbreite. RSRQ ist eine C/I-Art von Messung und zeigt die Qualität des empfangenen Referenzsignals an. Die RSRQ-Messung liefert zusätzliche Informationen, wenn RSRP nicht ausreicht, um eine zuverlässige Übergabe- oder Zellneuauswahlentscheidung zu treffen."}}, "wanStatistics": {"searchInputPlaceholder": "<PERSON><PERSON>", "table": {"header": {"type": "<PERSON><PERSON>", "minMax": "Min-<PERSON>", "average": "Durchschnitt", "latestResult": "<PERSON><PERSON><PERSON><PERSON>", "noOfTests": "<PERSON><PERSON><PERSON>"}}, "wanSpeedTest": {"title": "WAN-Geschwindigkeitstest", "confirmButton": "Bestätigen", "description": "Die folgenden Punkte werden getestet:", "desc1": "Geschwindigkeit", "desc2": "<PERSON><PERSON>", "desc3": "Jitter", "runManual": "Manuellen Latenztest ausführen", "manualLatencyTest": {"title": "Manuellen Latenztest ausführen", "description": "Legen Sie den Zielserver fest, mit dem Sie den Ping-Test ausführen möchten.", "urlLabel": "Server-<PERSON><PERSON><PERSON>", "urlPlaceholder": "http://test.telekom.de", "testing": "<PERSON><PERSON>", "testFailed": "Test fehlgeschlagen", "testSuccess": "Test erfol<PERSON><PERSON>ich", "connectedTime": "Verbindungszeit", "startTransferTime": "Startzeit der Übertragung"}}}, "downlink": "Downlink-Datenverkehr", "uplink": "Uplink-Datenverkehr", "broadbandConnectivity": "Breitband-Konnektivität", "wifiQoe": "Wi-Fi QoE", "broadbandQoe": "Breitband-QoE", "Band2G": "Band 2G", "Band5G": "Band 5G", "temperature": "Temperatur", "client": {"action": {"moreInfo": "Me<PERSON> Infos", "changeConfigs": "Konfigurationen ändern", "resetParentalControl": "Kindersicherung zurücksetzen", "runSpeedTest": "Geschwindigkeitstest durchführen", "runPingTest": "Ping-Test durchführen"}}, "Ethernet": "Ethernet", "EthernetPort1": "Ethernet-Port 1", "EthernetPort2": "Ethernet-Port 2", "EthernetPort3": "Ethernet-Port 3", "EthernetPort4": "Ethernet-Port 4", "EthernetPort5": "Ethernet-Port 5", "EthernetPort6": "Ethernet-Port 6", "EthernetPort7": "Ethernet-Port 7", "EthernetPort8": "Ethernet-Port 8"}