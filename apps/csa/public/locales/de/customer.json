{"customerInfo": {"customerNetwork": "Kundennetzwerk", "ispInfo": {"isp": "ISP", "city/province": "Stadt/Provinz", "timezone": "Zeitzone", "wanType": "WAN-Typ", "connectedExtenders": "Verbundene Extender", "connectedClients": "Verbundene Clients", "official": "Offiziell", "thirdParty": "Dritt<PERSON><PERSON><PERSON>", "ethernet": "Ethernet", "wifi": "Wi-Fi"}}, "healthCheck": {"healthCheck": "Systemprüfung", "latency": "<PERSON><PERSON>", "jitter": "Jitter", "wheel": {"sliceText": {"wan": "WAN", "lanWlan": "LAN/WLAN", "clients": "GERÄTE", "services": "DIENSTE", "cpe": "CPE", "unknown": "Unbekannt"}, "footer": {"eth": "Eth", "wifi": "Wi-Fi", "unknown": "Unbekannt"}, "client": {"drawer": {"summary": {"clients": "Clients", "switchToStats": "Zu Statistiken wechseln", "searchByDeviceName": "<PERSON><PERSON> Gerätenamen suchen", "cpe": "CPE", "allCpe": "Alle CPEs", "type": "<PERSON><PERSON>", "allType": "Alle Gerätetypen", "connection": "Verbindung", "allConnection": "Alle Verbindungen", "parental": "Kindersicherung", "enable": "Aktivieren", "disable": "Deaktivieren", "all": "Alle", "connected": "Verbunden", "disconnected": "Nicht verbunden", "header": {"cpeId": "CPE-ID", "device": "G<PERSON><PERSON>", "ipMac": "IP / MAC", "dataUsage": "Datennutzung", "insights": "Erkenntnisse", "rssi": "RSSI"}, "body": {"vsLastWeek": "vs. <PERSON><PERSON><PERSON>", "vsLast24h": "vs. 1 Tag zuvor", "latest": "Neueste", "rssiValue": "RSSI-Wert", "online": "Online", "offline": "Offline"}}, "statistics": {"clientStats": "Client-Statistiken - Wayne-iPhone14Pro-391422", "switchToSummary": "Zu Zusammenfassung wechseln", "searchByType": "<PERSON><PERSON>", "runSpeedTest": "Geschwindigkeitstest starten", "vsLastPeriod": "vs. let<PERSON><PERSON> Periode", "header": {"label": "<PERSON><PERSON>", "minMax": "Min / Max", "average": "Durchschnitt", "latestResult": "<PERSON><PERSON><PERSON><PERSON>", "noOfTests": "<PERSON><PERSON><PERSON>", "weeklyTrends": "Wöchentlicher Trend"}, "body": {"openGraph": "<PERSON><PERSON>", "status": "Status", "wifiPhyRate": "Wi-Fi Phy-Rate", "wifiThroughput": "Wi-Fi Durchsatz", "speedDn": "Geschwindigkeit (Dn)", "wifiLatencyDn": "<PERSON><PERSON><PERSON><PERSON> (Dn)", "wifiLatencyUp": "<PERSON><PERSON><PERSON><PERSON> (Up)", "trafficDnHourly": "Datenverkehr (Dn, stündlich)", "trafficUpHourly": "Datenverkehr (Up, stündlich)", "rssi": "RSSI", "snr": "SNR"}}}}, "services": {"speedDnUp": "Geschwindigkeit (Dn/Up)", "trafficDnUp": "Datenverkehr (Dn/Up)", "types": {"broadband": "Breitband", "streaming": "Streaming", "cybersecurity": "Cybersicherheit", "voip": "VoIP", "homeSecurity": "Heimsicherheit"}, "drawer": {"title": "<PERSON><PERSON><PERSON>", "table": {"switchToSummary": "Zu Zusammenfassung wechseln", "selectType": "Servicetyp", "searchPlaceholder": "Suche nach Dienstnamen", "clearAll": "Alle löschen", "header": {"serviceName": "Dienstname", "type": "<PERSON><PERSON>", "activeSince": "Aktiv seit", "interface": "Schnittstelle", "speedTest": "Geschwindigkeitstest (Dn/Up/Latenz/Jitter)"}}, "serviceStats": {"headerSelect": "Dienststatistiken – Internet", "table": {"header": {"type": "<PERSON><PERSON>", "minMax": "Min-<PERSON>", "average": "Durchschnitt", "latestResult": "<PERSON>z<PERSON> Ergebnis", "60thPercentile": "60. <PERSON><PERSON><PERSON>", "weeklyTrends": "Wöchentlicher Trend"}}}}}, "cpe": {"installed": "Installiert", "firmware": "Firmware", "rebootCountDescription": "Neustarts", "cpuStatusDescription": "CPU", "freeMemoryStatus": "<PERSON><PERSON><PERSON><PERSON>", "lastReboot": "Letzter Neustart", "router": "Router", "extender": "<PERSON><PERSON><PERSON>"}, "lanWlan": {"fullDuplex": "Vollduplex", "halfDuplex": "Halbduplex", "autoDuplex": "Automatischer Duplexdruck", "unknownDuplex": "Unbekannter Duplexmodus", "inUse": "In Gebrauch", "notInUse": "Nicht in Gebrauch"}, "wan": {"speedTest": "Geschwindigkeitstest (Dn/Up/Latenz)", "broadbandCongestion": "Breitbandüberlastung", "linkQuality": "Verbindungsqualität (Typ)", "errorRate": "Fehlerrate", "downCount": "Internet-Abbrüche", "activeSince": "Aktiv seit", "internetUsage": "Internetnutzung (Dn/Up)"}}}, "realtime": {"success": "Echtzeitbetrieb wurde erfolgreich abgeschlossen.", "error": "Echtzeitbetrieb fehlgeschlagen", "speedTest": {"success": "Geschwindigkeitstest wurde erfolgreich abgeschlossen.", "error": "Geschwindigkeitstest fehlgeschlagen"}}}