{"since": "since", "noStatusData": "No Status Data Available", "deviceImageAlt": "Device Image", "deviceInfo": {"seeCPEStats": "See CPE statistics", "cellularModem": "Cellular Modem", "action": {"actionButton": "Action", "confirmText": "Confirm", "cancelText": "Cancel", "testing": "Testing...", "close": "Close", "back": "Back", "viewLog": "View log", "dialogDescription": "Are you sure you want to perform this action on the device “{{deviceId}}”?", "notAllowedActionTooltip": "Not allowed due to lack of user permissions or device support for remote actions.", "cpe": {"title": "CPE", "deviceReboot": "<PERSON>ce reboot", "environmentReboot": "Environment reboot", "autoRebootConfig": "Auto-reboot config", "factoryReset": "Factory Reset", "parentalControlReset": "Parental control reset", "tr069ProcessRestart": "TR069 process restart", "firmwareUpdate": {"title": "Firmware update", "dialog": {"title": "Update firmware ({{deviceId}})", "description": "Select the new firmware to install.", "currentVersion": "Current Version", "availableVersion": "Available Version", "updateText": "Update", "confirmUpdateText": "Are you sure you would like to update the firmware?"}}, "cpeConfigBackup": "CPE config backup", "cpeConfigRestore": "CPE config restore", "downloadDeviceLog": "Download device log"}, "wifi": {"title": "WIFI", "wifiConfig": "WI-FI configuration", "optimizeWifiChannels": "Optimize Wi-Fi channels", "restartWifiDriver": "<PERSON><PERSON> Wi-Fi driver", "restartThisInterface": "Restart this interface", "restartWifiInterface": {"title": "Restart Wi-Fi interface", "dialog": {"title": "Restart Wi-Fi interface", "description": "Select the band to restart.", "wifiBand": "Wi-Fi Band", "restartButton": "<PERSON><PERSON>", "restartTitle": "Restart Wi-Fi interface", "restartConfirmText": "Are you sure you want to reset this interface?"}}}, "wan": {"title": "WAN", "restartWanInterface": "Restart WAN interface", "restartPpp": "Restart PPP", "pushPppConfig": {"title": "Push PPP config", "dialog": {"title": "Push PPP config ({{deviceId}})", "description": "Enter options for pushing the PPP config.", "username": "Username", "password": "Password", "protocolType": "Protocol type", "pppoe": "PPPoE (default)", "serviceName": "Service Name (Optional)", "serviceNamePlaceHolder": "Service Name", "mtuSize": "MTU Size (Optional)", "mtuSizePlaceHolder": "MTU", "authMethod": "Authentication method", "pap": "PAP (auto)", "connectionMode": "Connection Mode", "alwaysOn": "Always On", "ipMode": "IP Mode", "dynamic": "Dynamic (Default)"}}}, "message": {"restartWifiInterface": {"success": "Wi-Fi interface restarted successfully", "error": "Failed to restart Wi-Fi interface"}, "pingTest": {"success": "Ping test completed successfully", "error": "Failed to run ping test", "defaultError": "Something went wrong (code: {{errorCode}})"}}, "pingTest": {"title": "Ping test", "dialog": {"title": "Client ping test", "description": "Set the parameters you want to run the test", "numOfPingRequests": "No. of ping requests", "numOfPingRequestsWarning": "Please enter a number between 1 and 15.", "macAddress": "MAC Address", "ipAddress": "IP Address", "clientName": "Client Name", "runButton": "Run", "result": {"title": {"success": "Test success", "error": "Test failed", "failToStartTest": "Failed to start test"}, "connectedTime": "Connected time", "startTransferTime": "Start transfer time"}, "packets": "Packets:", "sent": "<PERSON><PERSON>", "received": "Received", "lost": "Lost", "approximateRoundTripTime": "Approximate round trip times", "min": "Min", "max": "Max", "avg": "Avg"}}}, "detail": {"cellularModem": {"currentType": "Current type", "supportedType": "Supported type"}, "deviceInfo": {"details": {"moreDetails": "More details", "deviceDetails": "Device details", "seeStatistics": "See Statistics", "interface": "Interface", "type": "Type", "id": "ID", "id2": "ID #2", "model": "Model", "firmware": "Firmware", "agentVer": "Agent ver.", "publicIp": "Public IP", "lanIp": "LAN IP", "mgmtIp": "Mgmt IP", "ipv6": "IPv6", "macAddress": "MAC Address", "lastReboot": "Last reboot", "lastUpload": "Last upload", "configurationGroups": "Configuration groups"}}}, "cpeStats": {"title": "CPE statistics -", "searchInputPlaceholder": "Search by type", "table": {"header": {"type": "Type", "minMax": "Min-<PERSON>", "average": "Average", "latestResult": "Latest Result", "noOfTests": "No. of tests", "weeklyTrend": "Weekly Trend"}, "body": {"warning": "Warning", "tooHigh": "too high"}}}}, "cpe": {"title": "CPE", "cpeStats": {"title": "CPE statistics"}, "cpeHistory": {"title": "CPE History", "metric": {"title": "Select Metric", "qoe": {"title": "QoE", "yLabel": "AP Health QoE"}, "cpuAndMemory": {"title": "CPU and Memory Usage", "cpuUsage": "CPU Usage (%)", "cpuLoad": "CPU Load", "freeMemory": "Free Memory (MB)", "cpuUsageTooltip": "CPU Usage", "cpuLoadTooltip": "CPU Load", "freeMemoryTooltip": "Free Memory"}, "chipsetTemp": {"title": "Temperature", "yLabel": "Temperature ({{unit}})"}, "powerCycle": "Power cycle", "rssi": "Wi-Fi backhaul RSSI", "disconnectionEvent": "Wi-Fi backhaul disconnections hourly"}}}, "lanWlan": {"title": "LAN/WLAN", "wifiStats": {"title": "Wi-Fi statistics", "selectBand": "Select Band", "runSpeedTest": {"button": "Run speed test", "modal": {"title": "Wi-Fi Speed test", "description": "Select the parameters you want to test.", "speed": "Speed", "latency": "Latency", "congestion": "Congestion"}}, "speed": "Speed", "latency": "Latency", "jitter": "Jitter (Up/Dn)", "congestion": "Congestion", "traffic": "Traffic (Up/Dn)", "transmission": "Transmission errors (Tx/Rx)", "interferenceAndNoise": "Ch. interference/noise", "temperature": "Chipset temperature", "trafficUp": "Traffic (Up)", "trafficDown": "Traffic (Down)", "interferenceScore": "Interference score", "noise": "Noise", "txStatus": "Tx status", "rxStatus": "Rx status"}, "lanPorts": "LAN Ports", "wifiBands": "Wi-Fi bands"}, "lanWLANHistory": {"title": "LAN/WLAN history", "selectInterface": "Select Interface", "wifiQoE": {"wifiQoE": "Wi-Fi QoE", "channel": "Channel", "connectivity": "Connectivity", "rssi": "RSSI", "speed": "Wi-Fi speed", "latency": "Wi-Fi latency", "jitter": "Wi-Fi jitter", "transmissionErrors": "Wi-Fi transmission errors", "channelNoise": "Wi-Fi channel noise", "chipsetTemperature": "Wi-Fi chipset temperature", "wifiTraffic": "Wi-Fi Traffic", "ethernetTraffic": "Ethernet Traffic", "channelInterference": "Wi-Fi Channel Interference"}}, "wanHistory": {"options": {"qoe": "Broadband QoE", "status": "Status", "packetLost": "Packet lost", "stability": "Stability", "speed": "Speed", "latency": "Latency", "jitter": "Jitter", "traffic": "Traffic", "transmissionError": "Transmission error", "lteCellId": "LTE Cell Id latest value", "lteBand": "LTE band latest value", "5gBand": "5G band latest value", "voiceRedundancy": "Voice redundancy latest", "5gRsrp": "5G RSRP min/max/average", "5gRsrq": "5G RSRQ min/max/average", "lteRsrp": "LTE RSRP min/max/average", "lteRsrq": "LTE RSRQ min/max/average"}, "chart": {"qoe": "Broadband QoE", "status": "Status", "linkStatus": "Link status", "upSpeed": "Upload speed", "downSpeed": "Download speed", "upTraffic": "Upload traffic", "downTraffic": "Download traffic", "latency": "Latency", "transmissionError": "Transmission error", "packetLost": "Packet loss", "jitter": "Jitter", "cellId": "Cell Id", "band": "Band", "voiceRedundancy": "Voice redundancy", "minRsrp": "Min", "maxRsrp": "Max", "avgRsrp": "Average", "minRsrq": "Min", "maxRsrq": "Max", "avgRsrq": "Average", "eventType": "Event type", "down": "Down", "up": "Up"}}, "serviceHistory": {"speed": "Internet speed", "latency": "Internet latency", "jitter": "Internet jitter", "traffic": "Internet traffic", "upSpeed": "Internet Upload speed", "downSpeed": "Internet Download speed", "upTraffic": "Internet Upload traffic", "downTraffic": "Internet Download traffic", "jitterFirst": "Internet Jitter0", "jitterSecond": "Internet Jitter1"}, "clientHistory": {"title": "Client History", "selectBandPlacehoder": "Select a band", "selectBand": "Select Band", "selectInterface": "Select Interface", "selectCpe": "Select CPE", "selectClient": "Select Client", "selectAll": "Select All", "wifiThroughput": {"selectMetric": "Select Metric", "qoe": "QoE", "wifiPhyRate": "Wi-Fi phy rate", "wifiThroughput": "Wi-Fi throughput", "latency": "Latency", "trafficDown": "Traffic Downlink", "trafficUp": "Traffic Uplink", "signalToNoiseRatio": "Signal to Noise Ratio", "rssiLevel": "RSSI Level"}, "chart": {"up": "Upload traffic", "down": "Download traffic", "latency": "Latency", "jitterFirst": "Jitter0", "jitterSecond": "Jitter1"}}, "cellularModem": {"title": "Cellular modem", "settings": {"title": "Settings", "general": "General", "roaming": "Roaming", "autoSelectNetwork": "Auto-select Network", "requestedNetwork": "Requested Network", "tmobile": "T-mobile", "vodafone": "Vodafone", "gigaclear": "GigaClear", "simPinCheckMethod": "SIM PIN Check Method", "onNetworkAccess": "On Network Access", "changeSimPin": "Change SIM PIN"}, "moreDetails": {"title": "More Details", "details": "Details", "interface": "Interface"}, "helpDrawer": {"title": "What is cellular model?", "description": "Cellular modem widget shows the current operational channel, along with the current operational bandwidth. A higher bandwidth, in the absence of strong external channel utilization, is typically desirable.", "descriptionRSSI": "Received Signal Strength Indicator:The carrier RSSI (Receive Strength Signal Indicator) measures the average total received power observed only in OFDM symbols containing reference symbols for antenna port 0 (i.e., OFDM symbol 0 & 4 in a slot) in the measurement bandwidth over N resource blocks. The total received power of the carrier RSSI includes the power from co-channel serving & non-serving cells, adjacent channel interference, thermal noise, etc. Total measured over 12-subcarriers including RS from Serving Cell, Traffic in the Serving Cell.", "descriptionRSRP": "Reference Signal Received Power: RSRP is a RSSI type of measurement, as follows there are some definition of it and some details as well.It is the power of the LTE Reference Signals spread over the full bandwidth and narrowband.A minimum of -20 dB SINR (of the S-Synch channel) is needed to detect RSRP/RSRQ.", "descriptionRSRQ": "Reference Signal Received Quality: Quality considering also RSSI and the number of used Resource Blocks (N) RSRQ = (N * RSRP) / RSSI measured over the same bandwidth. RSRQ is a C/I type of measurement and it indicates the quality of the received reference signal. The RSRQ measurement provides additional information when RSRP is not sufficient to make a reliable handover or cell reselection decision."}}, "wanStatistics": {"searchInputPlaceholder": "Search by type", "table": {"header": {"type": "Type", "minMax": "Min-<PERSON>", "average": "Average", "latestResult": "Latest Result", "noOfTests": "No. of tests"}}, "wanSpeedTest": {"title": "WAN speed test", "confirmButton": "Confirm", "description": "The following items are tested:", "desc1": "Speed", "desc2": "Latency", "desc3": "Jitter", "runManual": "Run manual latency test", "manualLatencyTest": {"title": "Run manual latency test", "description": "Set the destination server you want to run the ping test with.", "urlLabel": "Server address", "urlPlaceholder": "http://test.telekom.de", "testing": "Testing", "testFailed": "Test failed", "testSuccess": "Test success", "connectedTime": "Connected time", "startTransferTime": "Start transfer time"}}}, "downlink": "Downlink Traffic", "uplink": "Uplink Traffic", "broadbandConnectivity": "Broadband Connectivity", "wifiQoe": "Wi-Fi QoE", "broadbandQoe": "Broadband QoE", "Band2G": "Band 2G", "Band5G": "Band 5G", "temperature": "Temperature", "client": {"action": {"moreInfo": "More info", "changeConfigs": "Change configs", "resetParentalControl": "Reset Parental Control", "runSpeedTest": "Run speed test", "runPingTest": "Run ping test"}}, "Ethernet": "Ethernet", "EthernetPort1": "Ethernet Port 1", "EthernetPort2": "Ethernet Port 2", "EthernetPort3": "Ethernet Port 3", "EthernetPort4": "Ethernet Port 4", "EthernetPort5": "Ethernet Port 5", "EthernetPort6": "Ethernet Port 6", "EthernetPort7": "Ethernet Port 7", "EthernetPort8": "Ethernet Port 8"}