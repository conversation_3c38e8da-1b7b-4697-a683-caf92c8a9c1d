{"customerInfo": {"customerNetwork": "Customer Network", "ispInfo": {"isp": "ISP", "city/province": "City/Province", "timezone": "Timezone", "wanType": "WAN type", "connectedExtenders": "Connected Extenders", "connectedClients": "Connected Clients", "official": "Official", "thirdParty": "Third-party", "ethernet": "Ethernet", "wifi": "Wi-Fi"}}, "healthCheck": {"healthCheck": "Health Check", "latency": "Latency", "jitter": "Jitter", "wheel": {"sliceText": {"wan": "WAN", "lanWlan": "LAN/WLAN", "clients": "CLIENTS", "services": "SERVICES", "cpe": "CPE", "unknown": "Unknown"}, "footer": {"eth": "Eth", "wifi": "Wi-Fi", "unknown": "Unknown"}, "client": {"drawer": {"summary": {"clients": "Clients", "switchToStats": "Switch to statistics", "switchToSummary": "Switch to summary", "searchByDeviceName": "Search by device name", "cpe": "CPE", "allCpe": "All Cpe", "type": "Type", "allType": "All Device Type", "connection": "Connection", "allConnection": "All Connections", "parental": "Parental", "enable": "Enable", "disable": "Disable", "all": "All", "connected": "Connected", "disconnected": "Disconnected", "header": {"cpeId": "CPE ID", "device": "<PERSON><PERSON>", "ipMac": "IP / MAC", "dataUsage": "Data Usage", "insights": "Insights", "rssi": "RSSI"}, "body": {"vsLastWeek": "vs last week", "vsLast24h": "vs 1 day before", "latest": "latest", "rssiValue": "RSSI Value", "online": "Online", "offline": "Offline"}}, "statistics": {"clientStats": "Client statistics - Wayne-iPhone14Pro-391422", "switchToSummary": "Switch to summary", "searchByType": "Search by type", "runSpeedTest": "Run speed test", "vsLastPeriod": "vs. last period", "header": {"label": "Type", "minMax": "Min / Max", "average": "Average", "latestResult": "Latest Result", "noOfTests": "No. of tests", "weeklyTrends": "Weekly trend"}, "body": {"openGraph": "Open graph", "status": "Status", "wifiPhyRate": "Wi-Fi phy rate", "wifiThroughput": "Wi-Fi throughput", "speedDn": "Speed (Dn)", "wifiLatencyDn": "Wi-Fi latency (Dn)", "wifiLatencyUp": "Wi-Fi latency (Up)", "trafficDnHourly": "Traffic (Dn, hourly)", "trafficUpHourly": "Traffic (Up, hourly)", "rssi": "RSSI", "snr": "SNR"}}}}, "services": {"speedDnUp": "Speed (Dn/Up)", "trafficDnUp": "Traffic (Dn/Up)", "types": {"broadband": "Broadband", "streaming": "Streaming", "cybersecurity": "Cybersecurity", "voip": "VoIP", "homeSecurity": "Home Security"}, "drawer": {"title": "Services", "table": {"switchToSummary": "Switch to summary", "selectType": "Service Type", "searchPlaceholder": "Search by service name", "clearAll": "Clear all", "header": {"serviceName": "Service Name", "type": "Type", "activeSince": "Active since", "interface": "Interface", "speedTest": "Speed test (Dn/Up/Latency/Jitter)"}}, "serviceStats": {"headerSelect": "Service statistics - Internet", "table": {"header": {"type": "Type", "minMax": "Min-<PERSON>", "average": "Average", "latestResult": "Latest Result", "60thPercentile": "60th percentile", "weeklyTrends": "Weekly trend"}}}}}, "cpe": {"installed": "Installed", "firmware": "Firmware", "rebootCountDescription": "reboots", "cpuStatusDescription": "CPU", "freeMemoryStatus": "memory", "lastReboot": "Last Reboot", "router": "Router", "extender": "Extender"}, "lanWlan": {"fullDuplex": "Full Duplex", "halfDuplex": "Half Duplex", "autoDuplex": "Auto Duplex", "unknownDuplex": "Unknown Duplex", "inUse": "In use", "notInUse": "Not in use"}, "wan": {"speedTest": "Speed test (Dn/Up/Latency)", "broadbandCongestion": "Broadband congestion", "linkQuality": "Link quality (Type)", "errorRate": "Error rate", "downCount": "Internet down", "activeSince": "Active since", "internetUsage": "Internet usage (Dn/Up)"}}}, "realtime": {"success": "Real-time operation finished successfully", "error": "Real-time operation failed", "speedTest": {"success": "Speed test finished successfully", "error": "Speed test failed"}}}