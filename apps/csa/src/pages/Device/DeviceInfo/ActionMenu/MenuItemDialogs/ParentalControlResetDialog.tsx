import { useTranslation } from 'react-i18next';
import { useResetLineParentalControl } from 'services/Actions';
import { AxonAlertDialogWrapper, AxonToast } from 'ui/UIComponents';

type Props = {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  deviceId: string;
  lineId: string;
};
const ParentalControlResetDialog = ({ lineId, deviceId, onOpenChange, open }: Props) => {
  const { t } = useTranslation();

  const { mutateAsync: resetLineParentalControl, isPending } = useResetLineParentalControl();

  const handleConfirm = async () => {
    try {
      await resetLineParentalControl(lineId);
      AxonToast.success(t('operationSuccess'));
    } catch (error) {
      AxonToast.error(t('operationFailed'));
    }
  };

  return (
    <AxonAlertDialogWrapper
      open={open}
      onOpenChange={onOpenChange}
      title={t('device:deviceInfo.action.cpe.parentalControlReset')}
      description={t('device:deviceInfo.action.dialogDescription', { deviceId })}
      confirmText={t('device:deviceInfo.action.confirmText')}
      confirmLoading={isPending}
      cancelText={t('device:deviceInfo.action.cancelText')}
      onConfirm={handleConfirm}
      data-testid='device-info-parental-control-reset-dialog'
    />
  );
};

export default ParentalControlResetDialog;
