import { useDrawerStore } from '@/stores/drawer.store';
import { useTabDeviceId } from '@/stores/tab.store';
import { cn } from '@/utils';
import { Info, Maximize, X } from 'lucide-react';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useGetCpeDetailInfo } from 'services/CPEService';
import { getRelativeTimeFromNow } from 'services/Utils';
import {
  AxonButton,
  AxonDialog,
  AxonDialogClose,
  AxonDialogContent,
  AxonDialogTrigger,
  AxonSeparator,
} from 'ui/UIComponents';
import { useShallow } from 'zustand/react/shallow';

const DeviceDetailDialog = () => {
  const [open, setOpen] = useState(false);
  const { t } = useTranslation();
  const deviceId = useTabDeviceId() || '';
  const { data } = useGetCpeDetailInfo(deviceId, { enabled: !!deviceId && !!open });
  const deviceDetailsInfo = data?.data;

  const { openDrawer } = useDrawerStore(useShallow((state) => ({ openDrawer: state.openDrawer })));

  const bootupTime = getRelativeTimeFromNow(Number(deviceDetailsInfo?.lastRebootedAt));

  const uploadTime = getRelativeTimeFromNow(Number(deviceDetailsInfo?.lastUploadedAt));

  return (
    <>
      <AxonDialog open={open} onOpenChange={setOpen}>
        <AxonDialogTrigger asChild>
          <AxonButton
            variant='pure'
            data-testid='device-info-device-detail-dialog-trigger'
            className='flex cursor-pointer flex-row items-center gap-x-2'>
            <Info className='size-4 text-purple-600' />
            <p className='font-book text-purple-600'>{t('device:deviceInfo.detail.deviceInfo.details.moreDetails')}</p>
          </AxonButton>
        </AxonDialogTrigger>
        <AxonDialogContent
          hideCloseButton
          className={cn('bg-surface-popover flex h-5/6 max-w-md flex-row gap-x-0 overflow-auto p-4')}
          data-testid='device-info-device-detail-dialog-content'>
          <div className='flex w-full flex-col gap-y-3'>
            <div className='flex flex-row items-center justify-between'>
              <p className='text-lg font-medium' data-testid='device-info-device-detail-dialog-title'>
                {t('device:deviceInfo.detail.deviceInfo.details.deviceDetails')}
              </p>
              <AxonButton
                variant='default'
                className='mr-8'
                onClick={() => openDrawer('cpeStatistics')}
                data-testid='device-info-device-statistics-button'>
                <Maximize size={16} />
                {t('device:deviceInfo.detail.deviceInfo.details.seeStatistics')}
              </AxonButton>
            </div>
            <div
              className='scrollbar-lg flex w-full flex-col gap-y-4 overflow-auto'
              data-testid='device-info-device-details-content'>
              {/* <p className='font-book text-content-primary text-md'>
                {t('device:deviceInfo.detail.deviceInfo.details.interface')}
              </p> */}
              <div className='flex flex-col gap-y-2'>
                <p className='text-content-primary flex items-center gap-x-2' data-testid='device-info-device-type'>
                  <span className='text-content-secondary font-book min-w-28 text-sm'>
                    {t('device:deviceInfo.detail.deviceInfo.details.type')}
                  </span>
                  <span
                    className='font-book text-content-primary flex-1 text-sm'
                    data-testid='device-info-device-type-value'>
                    {deviceDetailsInfo?.deviceType}
                  </span>
                </p>
                <AxonSeparator />
              </div>
              <div className='flex flex-col gap-y-2'>
                <p className='text-content-primary flex items-center gap-x-2' data-testid='device-info-device-id'>
                  <span className='text-content-secondary font-book min-w-28 text-sm'>
                    {t('device:deviceInfo.detail.deviceInfo.details.id')}
                  </span>
                  <span
                    className='font-book text-content-primary flex-1 text-sm'
                    data-testid='device-info-device-id-value'>
                    {deviceDetailsInfo?.deviceId}
                  </span>
                </p>
                <AxonSeparator />
              </div>
              <div className='flex flex-col gap-y-2'>
                <p className='text-content-primary flex items-center gap-x-2' data-testid='device-info-secondary-id'>
                  <span className='text-content-secondary font-book min-w-28 text-sm'>
                    {t('device:deviceInfo.detail.deviceInfo.details.id2')}
                  </span>
                  <span
                    className='font-book text-content-primary flex-1 text-sm'
                    data-testid='device-info-secondary-id-value'>
                    {deviceDetailsInfo?.secondaryId}
                  </span>
                </p>
                <AxonSeparator />
              </div>
              <div className='flex flex-col gap-y-2'>
                <p className='text-content-primary flex items-center gap-x-2' data-testid='device-info-device-model'>
                  <span className='text-content-secondary font-book min-w-28 text-sm'>
                    {t('device:deviceInfo.detail.deviceInfo.details.model')}
                  </span>
                  <span
                    className='font-book text-content-primary flex-1 text-sm'
                    data-testid='device-info-device-model-value'>
                    {deviceDetailsInfo?.deviceModel}
                  </span>
                </p>
                <AxonSeparator />
              </div>
              <div className='flex flex-col gap-y-2'>
                <p
                  className='text-content-primary flex items-center gap-x-2'
                  data-testid='device-info-firmware-version'>
                  <span className='text-content-secondary font-book min-w-28 text-sm'>
                    {t('device:deviceInfo.detail.deviceInfo.details.firmware')}
                  </span>
                  <span
                    className='font-book text-content-primary flex-1 text-sm'
                    data-testid='device-info-firmware-version-value'>
                    {deviceDetailsInfo?.firmwareVersion}
                  </span>
                </p>
                <AxonSeparator />
              </div>
              <div className='flex flex-col gap-y-2'>
                <p className='text-content-primary flex items-center gap-x-2' data-testid='device-info-agent-version'>
                  <span className='text-content-secondary font-book min-w-28 text-sm'>
                    {t('device:deviceInfo.detail.deviceInfo.details.agentVer')}
                  </span>
                  <span
                    className='font-book text-content-primary flex-1 text-sm'
                    data-testid='device-info-agent-version-value'>
                    {deviceDetailsInfo?.agentVersion}
                  </span>
                </p>
                <AxonSeparator />
              </div>
              <div className='flex flex-col gap-y-2'>
                <p className='text-content-primary flex items-center gap-x-2' data-testid='device-info-public-ip'>
                  <span className='text-content-secondary font-book min-w-28 text-sm'>
                    {t('device:deviceInfo.detail.deviceInfo.details.publicIp')}
                  </span>
                  <span
                    className='font-book text-content-primary flex-1 text-sm'
                    data-testid='device-info-public-ip-value'>
                    {deviceDetailsInfo?.publicIpAddress}
                  </span>
                </p>
                <AxonSeparator />
              </div>
              <div className='flex flex-col gap-y-2'>
                <p className='text-content-primary flex items-center gap-x-2' data-testid='device-info-lan-ip'>
                  <span className='text-content-secondary font-book min-w-28 text-sm'>
                    {t('device:deviceInfo.detail.deviceInfo.details.lanIp')}
                  </span>
                  <span
                    className='font-book text-content-primary flex-1 text-sm'
                    data-testid='device-info-lan-ip-value'>
                    {deviceDetailsInfo?.lanIpAddress}
                  </span>
                </p>
                <AxonSeparator />
              </div>
              <div className='flex flex-col gap-y-2'>
                <p className='text-content-primary flex items-center gap-x-2' data-testid='device-info-management-ip'>
                  <span className='text-content-secondary font-book min-w-28 text-sm'>
                    {t('device:deviceInfo.detail.deviceInfo.details.mgmtIp')}
                  </span>
                  <span
                    className='font-book text-content-primary flex-1 text-sm'
                    data-testid='device-info-management-ip-value'>
                    {deviceDetailsInfo?.managementIpAddress || 'N/A'}
                  </span>
                </p>
                <AxonSeparator />
              </div>
              <div className='flex flex-col gap-y-2'>
                <p className='text-content-primary flex items-center gap-x-2' data-testid='device-info-ipv6-address'>
                  <span className='text-content-secondary font-book min-w-28 text-sm'>
                    {t('device:deviceInfo.detail.deviceInfo.details.ipv6')}
                  </span>
                  <span
                    className='font-book text-content-primary flex-1 text-sm'
                    data-testid='device-info-ipv6-address-value'>
                    {deviceDetailsInfo?.ipv6Address}
                  </span>
                </p>
                <AxonSeparator />
              </div>
              <div className='flex flex-col gap-y-2'>
                <p className='text-content-primary flex items-center gap-x-2' data-testid='device-info-mac-address'>
                  <span className='text-content-secondary font-book min-w-28 text-sm'>
                    {t('device:deviceInfo.detail.deviceInfo.details.macAddress')}
                  </span>
                  <span
                    className='font-book text-content-primary flex-1 text-sm'
                    data-testid='device-info-mac-address-value'>
                    {deviceDetailsInfo?.macAddress}
                  </span>
                </p>
                <AxonSeparator />
              </div>
              <div className='flex flex-col gap-y-2'>
                <p className='text-content-primary flex items-center gap-x-2' data-testid='device-info-last-reboot'>
                  <span className='text-content-secondary font-book min-w-28 text-sm'>
                    {t('device:deviceInfo.detail.deviceInfo.details.lastReboot')}
                  </span>
                  <span
                    className='font-book text-content-primary flex-1 text-sm'
                    data-testid='device-info-last-reboot-value'>
                    {bootupTime || 'N/A'}
                  </span>
                </p>
                <AxonSeparator />
              </div>
              <div className='flex flex-col'>
                <p className='text-content-primary flex items-center gap-x-2' data-testid='device-info-last-upload'>
                  <span className='text-content-secondary font-book min-w-28 text-sm'>
                    {t('device:deviceInfo.detail.deviceInfo.details.lastUpload')}
                  </span>
                  <span
                    className='font-book text-content-primary flex-1 text-sm'
                    data-testid='device-info-last-upload-value'>
                    {uploadTime || 'N/A'}
                  </span>
                </p>
              </div>
              <p
                className='text-md font-book text-content-primary mt-4'
                data-testid='device-info-configuration-groups-title'>
                {t('device:deviceInfo.detail.deviceInfo.details.configurationGroups')}
              </p>
              <div
                className='flex flex-wrap gap-x-2 gap-y-1 p-2 pt-0'
                data-testid='device-info-configuration-groups-container'>
                {deviceDetailsInfo?.configurationGroups
                  ?.filter((tag) => tag)
                  .map((tag) => (
                    <span
                      key={tag}
                      data-testid={`device-info-configuration-group-tag-${tag?.toLowerCase().replace(/\s+/g, '-')}`}
                      className={cn(
                        'bg-surface-action text-content-primary font-book rounded-full border px-2.5 py-1 text-sm',
                      )}>
                      {tag}
                    </span>
                  ))}
              </div>
            </div>
          </div>
          <AxonDialogClose
            data-testid='device-info-device-detail-dialog-close'
            className={cn(
              `ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground hover:bg-surface-action--hover absolute right-2 top-4 flex size-8 items-center justify-center rounded-sm opacity-70 transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:pointer-events-none`,
            )}>
            <X className={cn('size-4')} />
          </AxonDialogClose>
        </AxonDialogContent>
      </AxonDialog>
    </>
  );
};

export default DeviceDetailDialog;
