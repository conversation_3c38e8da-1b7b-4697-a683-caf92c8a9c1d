import { useDrawerStore } from '@/stores/drawer.store';
import { useTabDeviceId, useTabLineId } from '@/stores/tab.store';
import { useGetTimeRangeByLine } from '@/stores/timeRange.store';
import get from 'lodash/get';
import { ChevronDown, Ellipsis, Logs, Wifi } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useGetCpeInfo, useGetWanConfig } from 'services/CPEService';
import { getUnixTime } from 'services/Utils';
import { getDeviceImage, Signifier } from 'ui/UIAssets';
import {
  AxonBadge,
  AxonButton,
  AxonCard,
  AxonDropdownMenu,
  AxonDropdownMenuContent,
  AxonDropdownMenuItem,
  AxonDropdownMenuTrigger,
  AxonImage,
  AxonPopover,
  AxonPopoverContent,
  AxonPopoverTrigger,
} from 'ui/UIComponents';
import { useShallow } from 'zustand/react/shallow';
import ActionMenu from './ActionMenu';
import DeviceDetailDialog from './DeviceDetailDialog';
import Error from './Error';
import Skeleton from './Skeleton';

const DeviceInfo = () => {
  const { t } = useTranslation();
  const deviceId = useTabDeviceId() || '';
  const lineId = useTabLineId() || '';
  const timeRangeSelected = useGetTimeRangeByLine(lineId);
  const startDate = get(timeRangeSelected, 'startDate', null);
  const endDate = get(timeRangeSelected, 'endDate', null);

  const { openDrawer } = useDrawerStore(useShallow((state) => ({ openDrawer: state.openDrawer })));

  const { data, isLoading, isError } = useGetCpeInfo(deviceId ?? '', { enabled: !!deviceId });
  const { data: wanConfig } = useGetWanConfig(
    {
      customerId: lineId ?? '',
      deviceId: deviceId ?? '',
      startDate: getUnixTime(startDate) ?? 0,
      endDate: getUnixTime(endDate) ?? undefined,
    },
    { enabled: !!lineId && !!deviceId && !!startDate },
  );
  const deviceInfo = data?.data;
  const hasCellular =
    wanConfig?.data?.data.physical.wanType?.toLowerCase() === 'cellular' ||
    wanConfig?.data?.data.physical.wanType?.toLowerCase() === 'bonding';

  return (
    <AxonCard className='bg-surface-tile border-gradient-border scrollbar-lg min-h-[135px] overflow-auto p-5'>
      {isLoading ? (
        <Skeleton />
      ) : isError ? (
        <Error />
      ) : (
        <div className='flex flex-row items-center gap-x-6'>
          <AxonImage
            src={getDeviceImage(deviceInfo?.deviceType.split(' ')[0] ?? '')}
            size={'lg'}
            alt={t('device:deviceImageAlt')}
            className='p-2'
            data-testid={`device-info-device-image`}
          />
          <div>
            <p className='text-content-tertiary text-xs uppercase' data-testid={`device-info-device-type`}>
              {deviceInfo?.deviceType}
            </p>
            <div className='mt-1 flex gap-x-2'>
              <p className='text-2xl font-medium' data-testid={`device-info-device-serial-number`}>
                {deviceInfo?.serialNumber}
              </p>
              <AxonBadge variant='outline' className='rounded-sm' data-testid={`device-info-device-badge`}>
                <svg
                  width='29'
                  height='10'
                  viewBox='0 0 29 10'
                  fill='none'
                  xmlns='http://www.w3.org/2000/svg'
                  className='text-content-primary'>
                  <path
                    d='M11.7607 0.750485H12.0127V9.5H14.4109V0.750485H14.6411L23.457 9.5H28.8V0.5H26.4018V9.25054H26.1498L17.3339 0.5H9.06792L0 9.5H2.94481L11.7607 0.750485Z'
                    fill='currentColor'
                  />
                </svg>
                <span className='text-content-secondary ml-1.5 text-xs' data-testid={`device-info-device-badge-type`}>
                  {deviceInfo?.badgeType}
                </span>
              </AxonBadge>
            </div>
            <div className='mt-3 flex flex-row gap-x-5'>
              <div className='flex flex-row items-center gap-x-1' data-testid={`device-info-device-status`}>
                {deviceInfo?.status === 'Online' ? (
                  <Signifier className='text-content-meta-green' />
                ) : (
                  <Signifier className='text-content-disabled' />
                )}
                <p className='text-muted-foreground text-sm' data-testid={`device-info-device-status-label`}>
                  {deviceInfo?.status}
                </p>
              </div>
              {/* <div className='flex flex-row items-center gap-x-2'>
                <Radio size={16} />
                <p className='text-muted-foreground text-sm'>WIFI SSID</p>
              </div> */}
              <AxonPopover>
                <AxonPopoverTrigger asChild>
                  <AxonButton
                    variant='pure'
                    data-testid='device-info-location-trigger'
                    className='flex cursor-pointer flex-row items-center gap-x-2'>
                    <p className='font-book overflow-hidden text-ellipsis text-nowrap text-purple-600'>
                      {get(deviceInfo, 'bands[0].ssid', '')}
                    </p>
                    <ChevronDown size={16} className='text-purple-600' />
                  </AxonButton>
                </AxonPopoverTrigger>
                <AxonPopoverContent
                  align='start'
                  className='bg-surface-tile border-gradient-border flex w-[250px] flex-col gap-2'
                  onOpenAutoFocus={(e) => e.preventDefault()}
                  data-testid={`device-info-device-ssids`}>
                  {deviceInfo?.bands?.map((data, index) => <div key={index}>{data.ssid}</div>)}
                </AxonPopoverContent>
              </AxonPopover>

              <DeviceDetailDialog />
            </div>
          </div>
          <div className='ml-auto flex flex-row items-center gap-x-3'>
            <ActionMenu />

            {/* <AxonButton variant='outline' className='bg-surface-action border-gradient-border'>
              <Terminal className='mr-2' size={16} />
              vCPE
            </AxonButton> */}
            <AxonDropdownMenu>
              <AxonDropdownMenuTrigger asChild>
                <AxonButton
                  variant='outline'
                  size='icon'
                  aria-label={t('ariaLabel.moreActions')}
                  className='bg-surface-action border-gradient-border'
                  data-testid={`device-info-more-actions-button`}>
                  <Ellipsis size={16} />
                </AxonButton>
              </AxonDropdownMenuTrigger>
              <AxonDropdownMenuContent data-testid='device-info-more-actions-menu'>
                <AxonDropdownMenuItem
                  onClick={() => openDrawer('cpeStatistics')}
                  data-testid='device-info-see-cpe-stats'>
                  <Logs size={16} />
                  {t('device:deviceInfo.seeCPEStats')}
                </AxonDropdownMenuItem>
                <AxonDropdownMenuItem
                  disabled={!hasCellular}
                  className='hidden'
                  data-testid='device-info-cellular-modem'>
                  <Wifi size={16} />
                  {t('device:deviceInfo.cellularModem')}
                </AxonDropdownMenuItem>
              </AxonDropdownMenuContent>
            </AxonDropdownMenu>
          </div>
        </div>
      )}
    </AxonCard>
  );
};

export default DeviceInfo;
