import get from 'lodash/get';
import useRealtimeConfigStore, { useLineIsBusy, useGetSelectedRequestInfo } from '@/stores/realtime.store';
import { useTranslation } from 'react-i18next';
import { METRIC } from '@/constants';
import { useStartRealtimeProcess } from 'services/Realtime';
import { RealtimeRequestStatus, RequestInfo } from '@/stores/realtime.type';

export const useWanSpeedtestAction = (lineId: string | null) => {
  const updateRealtimeState = useRealtimeConfigStore((state) => state.updateRealtimeState);
  const { t } = useTranslation();
  const { startRealtimeProcess } = useStartRealtimeProcess();

  const selectedRequest = useGetSelectedRequestInfo(lineId || '', 'WAN_STATISTIC') as RequestInfo | undefined;
  const isLineBusy = useLineIsBusy(lineId || '');
  const isRealtimeRequesting = get(selectedRequest, 'isRealtimeRequesting', false);
  const currentStepText = isRealtimeRequesting
    ? get(selectedRequest, 'currentStepText') || t('processing...')
    : t('device:wanStatistics.wanSpeedTest.confirmButton');

  const startWanSpeedtest = () => {
    return new Promise<{ status: RealtimeRequestStatus; request: RequestInfo | undefined }>((resolve) => {
      if (lineId) {
        startRealtimeProcess(
          {
            lineId,
            metric: METRIC.WAN_STATISTIC,
          },
          updateRealtimeState,
          (status: RealtimeRequestStatus) => {
            resolve({ status, request: selectedRequest });
          },
        );
      }
    });
  };

  return {
    startWanSpeedtest,
    isLoading: isRealtimeRequesting || isLineBusy,
    currentStepText: currentStepText,
  };
};
