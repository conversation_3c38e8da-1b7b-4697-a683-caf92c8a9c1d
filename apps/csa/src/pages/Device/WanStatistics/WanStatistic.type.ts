import { EQoeStatus } from '@/utils/QOE.util';
import { WeeklyTrends } from '@/pages/Device/device.type';

export interface IWanStatistics {
  wanId: string;
  wanType: string;
  wanDown: string | number | null;
  wanDownIssue: string | null;
  wanUp: string | number | null;
  wanUpIssue: string | null;
  wanDescription: string | null;
  wanUnit: string;
  trendingData?: Array<WeeklyTrends[]>;
}

export interface IWanDetail {
  wanId: string;
  wanType: string;
  wanMinValue: string | number | null;
  wanMaxValue: string | number | null;
  wanAverage: string | number | null;
  wanValue: string | number | null;
  noOfTest: number | null;
  wanUnit: string;
  weeklyTrends: WeeklyTrends[];
}

export interface IWanStatisticResponseData {
  results: IWanStatistics[];
  status?: EQoeStatus;
  lastUpdated?: number;
}

export interface IWanStatisticDrawerResponseData {
  results: IWanDetail[];
}

export interface IWanStatisticsQuery {
  deviceId: string;
  lineId: string;
  startDate: number;
  endDate?: number;
}
