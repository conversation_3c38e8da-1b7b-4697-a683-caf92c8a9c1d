import get from 'lodash/get';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useRunManualLatencyTest } from 'services/Actions';
import { FailedGradient, SuccessGradient } from 'ui/UIAssets';
import { AxonButton, AxonDialog, AxonDialogContent, AxonInput, AxonLabel, AxonSpinner } from 'ui/UIComponents';

interface TestResult {
  connectTime: number | null;
  startTransferTime: number | null;
  error: string | null;
}

function ResultContent({ testResult, onClose }: { testResult: TestResult; onClose: () => void }) {
  const { t } = useTranslation();

  return (
    <div className='flex flex-col items-center gap-6'>
      {testResult.error ? <FailedGradient /> : <SuccessGradient />}
      <div className='flex flex-col items-center gap-2'>
        <span className='text-content-primary text-lg font-semibold'>
          {testResult.error
            ? t('device:wanStatistics.wanSpeedTest.manualLatencyTest.testFailed')
            : t('device:wanStatistics.wanSpeedTest.manualLatencyTest.testSuccess')}
        </span>
        {testResult.error ? (
          <span className='text-content-secondary text-md'>{testResult.error}</span>
        ) : (
          <>
            <span className='text-content-secondary text-md'>
              {t('device:wanStatistics.wanSpeedTest.manualLatencyTest.connectedTime')}: {testResult.connectTime}ms
              <br />
              {t('device:wanStatistics.wanSpeedTest.manualLatencyTest.startTransferTime')}:{' '}
              {testResult.startTransferTime}
              ms
            </span>
          </>
        )}
      </div>
      <AxonButton className='mt-4 w-full' variant='outline' onClick={onClose}>
        {t('close')}
      </AxonButton>
    </div>
  );
}

export default function ManualLatencyTestModal({
  deviceId,
  open,
  onOpenChange,
}: {
  deviceId: string;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}) {
  const { t } = useTranslation();
  const { mutateAsync: runManualLatencyTest, isPending } = useRunManualLatencyTest();

  const [url, setUrl] = useState('');
  const [testResult, setTestResult] = useState<TestResult | undefined>(undefined);

  useEffect(() => {
    if (!open) {
      setUrl('');
      setTestResult(undefined);
    }
  }, [open]);

  const onConfirm = async () => {
    try {
      const res = await runManualLatencyTest({
        deviceId,
        url,
      });
      setTestResult({
        connectTime: get(res, 'data.connectTimeMs', null),
        startTransferTime: get(res, 'data.startTransferTimeMs', null),
        error: get(res, 'data.error', null),
      });
    } catch (error) {
      setTestResult({
        connectTime: null,
        startTransferTime: null,
        error: get(error, 'data.message', t('operationFailed')),
      });
    }
  };

  return (
    <AxonDialog open={open} onOpenChange={onOpenChange}>
      <AxonDialogContent
        className='bg-surface-popover max-w-xs'
        data-testid='device-wan-statistics-manual-latency-test-modal-content'>
        {testResult ? (
          <ResultContent testResult={testResult} onClose={() => onOpenChange(false)} />
        ) : (
          <>
            <div className='flex flex-col gap-y-6'>
              <p className='text-content-primary text-lg font-semibold'>
                {t('device:wanStatistics.wanSpeedTest.manualLatencyTest.title')}
              </p>
              <p className='text-content-primary text-md'>
                {t('device:wanStatistics.wanSpeedTest.manualLatencyTest.description')}
              </p>
              <AxonLabel htmlFor='manual-latency-test-url'>
                {t('device:wanStatistics.wanSpeedTest.manualLatencyTest.urlLabel')}
              </AxonLabel>
              <AxonInput
                id='manual-latency-test-url'
                value={url}
                onChange={(e) => setUrl(e.target.value)}
                placeholder={t('device:wanStatistics.wanSpeedTest.manualLatencyTest.urlPlaceholder')}
                data-testid='device-wan-statistics-manual-latency-test-url-input'
              />
            </div>
            <div className='mt-4 grid grid-cols-2 gap-4'>
              <AxonButton
                variant='outline'
                onClick={() => onOpenChange(false)}
                data-testid='device-wan-statistics-manual-latency-test-cancel-button'>
                {t('cancel')}
              </AxonButton>
              <AxonButton
                disabled={isPending || !url}
                variant='accent'
                type='submit'
                onClick={onConfirm}
                data-testid='device-wan-statistics-manual-latency-test-confirm-button'>
                {isPending ? (
                  <>
                    <AxonSpinner size={18} />
                    {t('device:wanStatistics.wanSpeedTest.manualLatencyTest.testing')}
                  </>
                ) : (
                  t('confirm')
                )}
              </AxonButton>
            </div>
          </>
        )}
      </AxonDialogContent>
    </AxonDialog>
  );
}
