import { useGetCapabilities } from '@/hooks/useGetCapabilities';
import { cn } from '@/utils';
import get from 'lodash/get';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Loading, RefreshCcw } from 'ui/UIAssets';
import {
  AxonButton,
  AxonDialog,
  AxonDialogContent,
  AxonDialogFooter,
  AxonDialogTrigger,
  AxonToast,
} from 'ui/UIComponents';
import { useWanSpeedtestAction } from '../useWanSpeedtestAction';
import ManualLatencyTestModal from './ManualLatencyTest';

export default function WanSpeedTestModal({ deviceId, disabled }: { deviceId: string; disabled?: boolean }) {
  const { t } = useTranslation();

  const {
    startWanSpeedtest,
    currentStepText: realtimeStatus,
    isLoading: isLoadingRealtime,
  } = useWanSpeedtestAction(deviceId);
  const capabilities = useGetCapabilities();
  const canRunManualLatencyTest = get(capabilities, 'isRunManualLatencyTestEnabled', false);

  const [open, setOpen] = useState(false);
  const [isManualOpen, setIsManualOpen] = useState(false);

  const handleRunWANSpeedTest = async () => {
    const result = await startWanSpeedtest();
    if (result.status === 'SUCCESS') {
      AxonToast.success(t('customer:realtime.speedTest.success'));
      setOpen(false);
    } else if (result.status === 'FAILED') {
      AxonToast.error(t('customer:realtime.speedTest.error'));
    }
  };

  return (
    <AxonDialog open={open} onOpenChange={setOpen}>
      <AxonDialogTrigger asChild data-testid='device-wan-statistics-speed-test-button'>
        <AxonButton
          disabled={disabled}
          variant='outline'
          className='bg-surface-action text-component-hyperlink hover:text-component-hyperlink w-fit border-none shadow-none'>
          <RefreshCcw className='mr-2 size-4' />
          {t('runSpeedTest')}
        </AxonButton>
      </AxonDialogTrigger>
      <AxonDialogContent
        className='bg-surface-popover max-w-xs'
        data-testid='device-wan-statistics-speed-test-modal-content'>
        <div className='flex flex-col gap-y-6'>
          <p className='text-content-primary text-lg font-semibold'>{t('device:wanStatistics.wanSpeedTest.title')}</p>
          <p className='text-content-primary text-md'>{t('device:wanStatistics.wanSpeedTest.description')}</p>
          <ul className='list-disc space-y-2 ps-4'>
            <li>{t('device:wanStatistics.wanSpeedTest.desc1')}</li>
            <li>{t('device:wanStatistics.wanSpeedTest.desc2')}</li>
            <li>{t('device:wanStatistics.wanSpeedTest.desc3')}</li>
          </ul>
          <a
            aria-disabled={!canRunManualLatencyTest}
            href='#'
            className={cn(
              'text-component-hyperlink font-medium',
              !canRunManualLatencyTest && 'cursor-not-allowed opacity-50',
            )}
            onClick={() => canRunManualLatencyTest && setIsManualOpen(true)}
            data-testid='device-wan-statistics-speed-test-manual-link'>
            {t('device:wanStatistics.wanSpeedTest.runManual')}
          </a>
        </div>
        <div className='mt-4 grid grid-cols-2 gap-4'>
          <AxonButton
            variant='outline'
            onClick={() => setOpen(false)}
            data-testid='device-wan-statistics-speed-test-cancel'>
            {t('cancel')}
          </AxonButton>
          <AxonButton
            startDecorator={isLoadingRealtime ? <Loading className={cn('size-4 animate-spin')} /> : undefined}
            disabled={isLoadingRealtime}
            variant='accent'
            type='submit'
            onClick={handleRunWANSpeedTest}
            data-testid='device-wan-statistics-speed-test-start'>
            {t('start')}
          </AxonButton>
        </div>
        {isLoadingRealtime && (
          <AxonDialogFooter>
            <p className='text-right text-sm' data-testid='device-wan-statistics-speed-test-real-time-status'>
              {realtimeStatus}...
            </p>
          </AxonDialogFooter>
        )}
      </AxonDialogContent>
      <ManualLatencyTestModal open={isManualOpen} onOpenChange={setIsManualOpen} deviceId={deviceId} />
    </AxonDialog>
  );
}
