import { AxonSeparator, AxonSkeletonLoader } from 'ui/UIComponents';

const Skeleton = () => {
  return (
    <>
      <div className='h-[167px]'>
        <div className='grid grid-cols-4 divide-x-2'>
          <div className='flex flex-col gap-y-2 p-4 pb-8 pl-0 pt-0'>
            <AxonSkeletonLoader className='h-5 w-16' />
            <AxonSkeletonLoader className='h-5 w-32' />
          </div>
          <div className='flex flex-col gap-y-2 p-4 pb-8 pt-0'>
            <AxonSkeletonLoader className='h-5 w-16' />
            <AxonSkeletonLoader className='h-5 w-32' />
          </div>
          <div className='flex flex-col gap-y-2 p-4 pb-8 pt-0'>
            <AxonSkeletonLoader className='h-5 w-16' />
            <AxonSkeletonLoader className='h-5 w-32' />
          </div>
          <div className='flex flex-col gap-y-2 p-4 pb-8 pt-0'>
            <AxonSkeletonLoader className='h-5 w-16' />
            <AxonSkeletonLoader className='h-5 w-32' />
          </div>
        </div>
        <div className='border-gradient-border grid grid-cols-4 divide-x-2 border-t pb-4'>
          <div className='flex flex-col gap-y-2 p-4 pl-0'>
            <AxonSkeletonLoader className='h-5 w-16' />
            <AxonSkeletonLoader className='h-5 w-32' />
          </div>
          <div className='flex flex-col gap-y-2 p-4'>
            <AxonSkeletonLoader className='h-5 w-16' />
            <AxonSkeletonLoader className='h-5 w-32' />
          </div>
          <div className='flex flex-col gap-y-2 p-4'>
            <AxonSkeletonLoader className='h-5 w-16' />
            <AxonSkeletonLoader className='h-5 w-32' />
          </div>
          <div className='flex flex-col gap-y-2 p-4'>
            <AxonSkeletonLoader className='h-5 w-16' />
            <AxonSkeletonLoader className='h-5 w-32' />
          </div>
        </div>
      </div>
      <AxonSeparator />

      <div>
        <AxonSkeletonLoader className='h-5 w-64' />
      </div>
    </>
  );
};

export default Skeleton;
