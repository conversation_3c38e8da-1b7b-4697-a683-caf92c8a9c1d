import { useTranslation } from 'react-i18next';
import { List, MoreHorizontal } from 'ui/UIAssets';
import {
  AxonButton,
  AxonDropdownMenu,
  AxonDropdownMenuContent,
  AxonDropdownMenuGroup,
  AxonDropdownMenuItem,
  AxonDropdownMenuTrigger,
} from 'ui/UIComponents';
import { LANPortType } from '../types';

interface LANPortMoreActionProps {
  lanPort: LANPortType;
  onOpenClientsStatsDrawer: (stationMac: string | null) => void;
}

const LANPortMoreAction = ({ lanPort, onOpenClientsStatsDrawer }: LANPortMoreActionProps) => {
  const { t } = useTranslation();

  return (
    <>
      <AxonDropdownMenu>
        <AxonDropdownMenuTrigger asChild>
          <AxonButton aria-label={t('ariaLabel.moreActions')} size='icon' data-testid='device-lan-ports-more-button'>
            <MoreHorizontal />
          </AxonButton>
        </AxonDropdownMenuTrigger>
        <AxonDropdownMenuContent align='end' className='w-2xs' data-testid={`device-lan-port-more-menu-client-infor`}>
          <AxonDropdownMenuGroup>
            <AxonDropdownMenuItem
              disabled={!lanPort.stationMac}
              aria-disabled={!lanPort.stationMac}
              aria-label={t('ariaLabel.moreInfoClient')}
              data-testid={`device-lan-port-more-info-${lanPort.port}`}
              onClick={() => onOpenClientsStatsDrawer(lanPort.stationMac)}>
              <List className='size-4' /> {t('ariaLabel.moreInfoClient')}
            </AxonDropdownMenuItem>
          </AxonDropdownMenuGroup>
        </AxonDropdownMenuContent>
      </AxonDropdownMenu>
    </>
  );
};

export default LANPortMoreAction;
