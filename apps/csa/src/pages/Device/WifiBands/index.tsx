import useTabStore, { useConfigWidgetWifiBands, useTabDeviceId, useTabLineId } from '@/stores/tab.store';
import { useGetTimeRangeByLine } from '@/stores/timeRange.store';
import { ColumnDef, getCoreRowModel, getSortedRowModel, SortingState, useReactTable } from '@tanstack/react-table';
import { kebabCase } from 'lodash';
import get from 'lodash/get';
import { useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { getUnixTime } from 'services/Utils';
import { AxonCard, AxonCardContent, AxonTableData } from 'ui/UIComponents';
import { GetWifiBandsResponse, WifiBandType } from './types';
import WifiBandMoreAction from './WifiBandMoreAction';
import { useGetData, EDataSection } from 'services/GetData';

const WifiBands = () => {
  const { t } = useTranslation();
  const addConfigEnhance = useTabStore((state) => state.addConfigEnhance);
  const lineId = useTabLineId() || '';
  const timeRangeSelected = useGetTimeRangeByLine(lineId);
  const startDate = get(timeRangeSelected, 'startDate', null);
  const endDate = get(timeRangeSelected, 'endDate', null);
  const deviceId = useTabDeviceId() || '';

  const {
    data: wifiBandsData,
    isLoading,
    isError,
    // @ts-ignore generic type cannot be resolved at run time
  } = useGetData<GetWifiBandsResponse>(
    {
      data: EDataSection.WIFI_BAND,
      deviceId,
      startDate: Number(getUnixTime(startDate)),
      endDate: Number(getUnixTime(endDate)),
    },
    {
      enabled: !!deviceId && !!startDate && !!endDate,
      staleTime: Infinity,
    },
  );

  const rows = useMemo(() => wifiBandsData?.data?.results ?? [], [wifiBandsData]);
  const columns = useMemo<ColumnDef<WifiBandType, any>[]>(
    () => [
      {
        header: t('frequency'),
        accessorKey: 'frequency',
        cell: ({
          row: {
            original: { frequency, mode },
            id,
          },
        }) => (
          <div>
            <p className='text-content-primary text-md' data-testid={`device-wifi-bands-frequency-${kebabCase(id)}`}>
              {frequency}
            </p>
            <p
              className='text-content-primary font-book text-xs opacity-60'
              data-testid={`device-wifi-bands-mode-${kebabCase(id)}`}>
              {mode}
            </p>
          </div>
        ),
      },
      {
        header: t('ssidInterface'),
        accessorKey: 'ssid',
        meta: { size: 200 },
        cell: ({
          row: {
            original: { ssid, interface: bandInterface, status },
            id,
          },
        }) => (
          <div>
            <p
              className='text-content-primary text-md font-medium'
              data-testid={`device-wifi-bands-ssid-${kebabCase(id)}`}>
              {ssid}
            </p>
            <p className='text-content-tertiary font-book inline-flex items-center gap-1 text-xs'>
              <div aria-hidden={true} className='bg-content-meta-green size-2 rounded-full' />
              <span data-testid={`device-wifi-bands-status-interface-${kebabCase(id)}`}>
                {`${status}, ${bandInterface ? bandInterface : 'N/A'}`}
              </span>
            </p>
          </div>
        ),
      },
      {
        header: t('security'),
        accessorKey: 'securityType',
        cell: ({
          row: {
            original: { securityType, encryption },
            id,
          },
        }) => (
          <div>
            <p data-testid={`device-wifi-bands-security-type-${kebabCase(id)}`}>{securityType}</p>
            <p className='text-content-tertiary font-book text-xs'>{encryption ? encryption.toUpperCase() : 'N/A'}</p>
          </div>
        ),
      },
      {
        header: t('ch.'),
        accessorKey: 'channel',
        cell: ({
          row: {
            original: { channel, channelBandwidth },
            id,
          },
        }) => (
          <div>
            <p data-testid={`device-wifi-bands-channel-${kebabCase(id)}`}>{channel}</p>
            <p
              className='text-content-tertiary font-book text-xs'
              data-testid={`device-wifi-bands-channel-bandwidth-${kebabCase(id)}`}>
              {channelBandwidth ? `${channelBandwidth} MHz` : 'N/A'}
            </p>
          </div>
        ),
      },
      {
        header: t('interferenceCongestion'),
        accessorKey: 'interference',
        cell: ({
          row: {
            original: { interference, congestion },
          },
        }) => (
          <div>
            <p>
              {interference ? `${interference}%` : '-'}
              {' / '}
              {congestion ? `${congestion}%` : '-'}
            </p>
          </div>
        ),
      },
      {
        id: 'more',
        cell: ({ row: { original } }) => <WifiBandMoreAction wifiBand={original} />,
      },
    ],
    [t],
  );
  const sorting = useConfigWidgetWifiBands()!.sorting as SortingState;
  const handleChangeSorting = useCallback(
    (updaterOrValue: SortingState | ((old: SortingState) => SortingState)) => {
      const newSorting = typeof updaterOrValue === 'function' ? updaterOrValue(sorting) : updaterOrValue;
      addConfigEnhance('widgets.wifiBands.sorting', newSorting);
    },
    [sorting, addConfigEnhance],
  );

  const table = useReactTable({
    columns,
    data: rows,
    state: {
      sorting,
    },
    onSortingChange: handleChangeSorting,

    getSortedRowModel: getSortedRowModel(),
    getCoreRowModel: getCoreRowModel(),
    getRowId: useCallback((row: WifiBandType) => row.frequency, []),
  });
  return (
    <AxonCard id='wifiBands' data-testid='device-wifi-bands-card'>
      <AxonCardContent className='space-y-6 pt-6' data-testid='device-wifi-bands-card-content'>
        <h2 className='text-content-primary text-md font-medium'>{t('wifiBands')}</h2>

        <div className='scrollbar-lg h-[250px] overflow-auto'>
          <AxonTableData
            tabIndex={0}
            showFooter={false}
            table={table}
            isLoading={isLoading}
            isError={isError}
            classes={{ table: '[&_td_div]:space-y-1 [&_th:first-child]:pl-0 [&_td:first-child]:pl-0' }}
            data-testid='device-wifi-bands-table'
          />
        </div>
      </AxonCardContent>
    </AxonCard>
  );
};

export default WifiBands;
