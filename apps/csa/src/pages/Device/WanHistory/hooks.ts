import { useGetWanHistory } from 'services/CPEService';
import { getUnixTime } from 'services/Utils';

export const useWanHistoryChart = ({
  customerId,
  deviceId,
  startDate,
  endDate,
  metric,
}: {
  customerId: string;
  deviceId: string;
  startDate: Date | null;
  endDate: Date | null;
  metric: string;
}) => {
  const { data, isLoading, isError } = useGetWanHistory(
    {
      customerId,
      deviceId,
      startDate: getUnixTime(startDate) || 0,
      endDate: getUnixTime(endDate) || 0,
    },
    { enabled: Boolean(customerId) && Boolean(deviceId) && Boolean(startDate) && Boolean(metric) },
  );

  const chartData = data?.data?.[metric] || [];
  const lineProps =
    chartData && Array.isArray(chartData) && chartData.length > 0
      ? Object.keys(chartData[0])
          .filter((key) => ['date', 'hideTooltip'].includes(key) === false)
          .map((item, index) => {
            return item === 'latency'
              ? { dataKey: item, color: `rgb(var(--chart-${(index % 30) + 1}))`, yAxisId: item }
              : { dataKey: item, color: `rgb(var(--chart-${(index % 30) + 1}))` };
          })
      : [];

  return { chartData, lineProps, isLoading, isError };
};
