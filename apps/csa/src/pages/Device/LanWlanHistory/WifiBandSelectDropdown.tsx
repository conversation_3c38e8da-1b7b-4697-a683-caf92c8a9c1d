import { Filter } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import {
  AxonButton,
  AxonCheckbox,
  AxonDropdownMenu,
  AxonDropdownMenuContent,
  AxonDropdownMenuLabel,
  AxonDropdownMenuTrigger,
} from 'ui/UIComponents';

interface WifiBandSelectDropdownProps {
  wifiBands: Array<{ id: string; title: string }>;
  selectedBandIds: readonly string[];
  isDisabled: boolean;
  setFilter: (path: string, value: any, customerId?: string) => void;
}

export const WifiBandSelectDropdown = ({
  wifiBands,
  selectedBandIds = [],
  setFilter,
  isDisabled,
}: WifiBandSelectDropdownProps) => {
  const { t } = useTranslation();

  const onCheckAll = (checked: boolean) => {
    if (checked) {
      setFilter(
        'widgets.lanWlanHistory.filterState.bandIds',
        wifiBands.map((b) => b.id),
      );
    } else {
      setFilter('widgets.lanWlanHistory.filterState.bandIds', []);
    }
  };

  const onCheckBand = (checked: boolean, bandId: string) => {
    if (checked) {
      setFilter('widgets.lanWlanHistory.filterState.bandIds', [...selectedBandIds, bandId]);
    } else {
      setFilter(
        'widgets.lanWlanHistory.filterState.bandIds',
        selectedBandIds.filter((b) => b !== bandId),
      );
    }
  };

  return (
    <AxonDropdownMenu data-testid='device-wifi-band-select-dropdown'>
      <AxonDropdownMenuTrigger
        asChild
        aria-label={t('ariaLabel.bandFilter')}
        data-testid='device-wifi-band-select-dropdown-trigger'>
        <AxonButton disabled={isDisabled} endDecorator={<Filter size={16} />} variant='ghost'>
          {t('device:lanWLANHistory.selectInterface')}
        </AxonButton>
      </AxonDropdownMenuTrigger>
      <AxonDropdownMenuContent className='scrollbar-lg min-w-[200px] overflow-auto'>
        {wifiBands.length > 0 && (
          <AxonDropdownMenuLabel>
            <div className='flex w-full flex-row items-center'>
              <p className='text-md text-content-primary'>{t('device:clientHistory.selectAll')}</p>
              <div className='ml-auto'>
                <AxonCheckbox
                  checked={wifiBands.every((b) => selectedBandIds.includes(b.id))}
                  onCheckedChange={onCheckAll}
                />
              </div>
            </div>
          </AxonDropdownMenuLabel>
        )}
        {wifiBands.map((band) => {
          const isChecked = selectedBandIds.some((id) => id === band.id);
          return (
            <AxonDropdownMenuLabel key={band.id}>
              <div className='flex w-full flex-row items-center'>
                <div className='flex flex-col gap-y-2'>
                  <p className={'text-md text-content-primary'}>{band.title}</p>
                </div>
                <div className='ml-auto'>
                  <AxonCheckbox
                    checked={isChecked}
                    onCheckedChange={(checked: boolean) => onCheckBand(checked, band.id)}
                  />
                </div>
              </div>
            </AxonDropdownMenuLabel>
          );
        })}
      </AxonDropdownMenuContent>
    </AxonDropdownMenu>
  );
};
