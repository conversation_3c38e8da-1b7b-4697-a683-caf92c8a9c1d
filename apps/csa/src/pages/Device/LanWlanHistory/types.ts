export interface LanWlanHistoryRequest {
  customerId: string;
  lanWlanType: string;
  deviceId: string;
  startDate: number;
  endDate?: number;
}
export interface MetaData {
  unit: string;
  useBand: boolean;
}
export interface LanWlanHistoryResponse {
  data: { [key: string]: LanWlanHistoryChartData[] };
  meta: { [key: string]: MetaData };
}

export interface LanWlanHistoryChartData {
  date: number;
  [key: string]: number;
}
