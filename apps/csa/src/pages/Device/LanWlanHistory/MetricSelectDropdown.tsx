import { LanWlanHistoryType } from '@/stores/widgets.config';
import { useTranslation } from 'react-i18next';
import {
  AxonSelect,
  AxonSelectContent,
  AxonSelectGroup,
  AxonSelectItem,
  AxonSelectLabel,
  AxonSelectTrigger,
  AxonSelectValue,
} from 'ui/UIComponents';

interface MetricSelectDropdownProps {
  lanWlanType: LanWlanHistoryType;
  handleChangeMetric: (metric: string) => void;
}

const MetricSelectDropdown = ({ lanWlanType, handleChangeMetric }: MetricSelectDropdownProps) => {
  const { t } = useTranslation();

  const ETHERNET_METRICS = [
    { value: LanWlanHistoryType.QOE, label: t('device:lanWLANHistory.wifiQoE.wifiQoE') },
    { value: LanWlanHistoryType.CHANNEL, label: t('device:lanWLANHistory.wifiQoE.channel') },
    { value: LanWlanHistoryType.CONNECTIVITY, label: t('device:lanWLANHistory.wifiQoE.connectivity') },
    { value: LanWlanHistoryType.WIFI_SPEED, label: t('device:lanWLANHistory.wifiQoE.speed') },
    { value: LanWlanHistoryType.WIFI_TRANMISSION, label: t('device:lanWLANHistory.wifiQoE.transmissionErrors') },
    { value: LanWlanHistoryType.WIFI_CHANNEL_NOISE, label: t('device:lanWLANHistory.wifiQoE.channelNoise') },
    {
      value: LanWlanHistoryType.WIFI_CHIPSET_TEMPERATURE,
      label: t('device:lanWLANHistory.wifiQoE.chipsetTemperature'),
    },
    {
      value: LanWlanHistoryType.WIFI_CHANNEL_INTERFERENCE,
      label: t('device:lanWLANHistory.wifiQoE.channelInterference'),
    },
    {
      value: LanWlanHistoryType.WIFI_TRAFFIC,
      label: t('device:lanWLANHistory.wifiQoE.wifiTraffic'),
    },
    {
      value: LanWlanHistoryType.ETHERNET_TRAFFIC,
      label: t('device:lanWLANHistory.wifiQoE.ethernetTraffic'),
    },
  ];

  return (
    <AxonSelect value={lanWlanType} onValueChange={handleChangeMetric} data-testid='device-lan-wlan-metric-select'>
      <AxonSelectTrigger
        className='w-fit gap-x-2 border-none bg-transparent'
        aria-label={t('selectMetric')}
        data-testid='device-lan-wlan-metric-select-trigger'>
        <AxonSelectValue placeholder='' />
      </AxonSelectTrigger>
      <AxonSelectContent data-testid='device-lan-wlan-metric-select-content'>
        <AxonSelectGroup>
          <AxonSelectLabel>{t('selectMetric')}</AxonSelectLabel>
          {ETHERNET_METRICS.map((metric) => (
            <AxonSelectItem
              key={metric.value}
              value={metric.value}
              data-testid={`device-lan-wlan-metric-select-option-${metric.value}`}>
              {metric.label}
            </AxonSelectItem>
          ))}
        </AxonSelectGroup>
      </AxonSelectContent>
    </AxonSelect>
  );
};

export default MetricSelectDropdown;
