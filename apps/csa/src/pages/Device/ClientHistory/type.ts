import { ConnectionType } from '@/pages/Device/device.type';
export interface CpeClient {
  name: string;
  mac: string;
  type: string;
}

export interface CpeBand {
  bandId: string;
  band: string;
  label: string;
  networkType: string;
  connectionType: ConnectionType;
}

export interface CpeClientStation {
  rssiPercentile: number | null;
  rssiAvg: number | null;
  rssiMin: number | null;
  snr: number | null;
  throughput: number | null;
  latency: number | null;
  txPhyRate: number | null;
  rxPhyRate: number | null;
  trafficUp: number | null;
  trafficDown: number | null;
}

export interface CpeClientHistoryDatapoint {
  date: number;
  stations: Record<string, Record<string, CpeClientStation> | { qoe: number }>;
}

export interface ClientHistoryResponse {
  results: {
    [cpeId: string]: CpeClientHistoryDatapoint[];
  };
  clients: CpeClient[];
  bands: CpeBand[];
  cpeIds: string[];
}

export type ClientHistoryParams = {
  customerId: string;
  deviceId: string;
  startDate: number;
  endDate?: number;
};
