export interface IPayload {
  id?: string;
  value: string;
  color?: string;
  type?: string;
}

interface IProps {
  payload: IPayload[];
}

export function ClientHistoryLegend(props: IProps) {
  const { payload } = props;
  const mergePayload: IPayload[] = [];
  const otherPayload: IPayload[] = [];
  payload.forEach((entry) => {
    if (entry.id === 'line-solid' || entry.id === 'line-dashed') {
      mergePayload.push(entry);
    } else {
      otherPayload.push(entry);
    }
  });

  function getIcon(entry: IPayload) {
    if (entry.type === 'dashed') {
      return (
        <div className='flex justify-between gap-1'>
          <div className='bg-error-500 size-1' />
          <div className='bg-error-500 h-1 w-2' />
          <div className='bg-error-500 size-1' />
        </div>
      );
    } else if (entry.type === 'solid') {
      return <div className='bg-error-500 h-1 w-6' />;
    }
    return <div className='size-2 rounded-full' style={{ backgroundColor: entry.color }} />;
  }

  return (
    <div className='flex justify-between'>
      <ul className='flex basis-1/4 gap-2'>
        {mergePayload.map((entry, index) => (
          <li key={`item-${index}`} className='flex items-center gap-1'>
            {getIcon(entry)}
            <span className='text-nowrap'>{entry.value}</span>
          </li>
        ))}
      </ul>
      <ul className='flex flex-wrap gap-4'>
        {otherPayload.map((entry, index) => (
          <li key={`item-${index}`} className='flex items-center gap-2'>
            <div className='size-3 rounded-full' style={{ backgroundColor: entry.color }} />
            <span>{entry.value}</span>
          </li>
        ))}
      </ul>
    </div>
  );
}
