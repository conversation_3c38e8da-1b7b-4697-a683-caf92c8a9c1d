import { useTabLineId } from '@/stores/tab.store';
import { DATETIME_FORMAT, formatDate, getRelativeTimeFromNow } from 'services/Utils';
import { AxonSelect, AxonSelectContent, AxonSelectItem, AxonSelectTrigger, AxonSelectValue } from 'ui/UIComponents';
import useRealtimeConfigStore, {
  useGetListRequestInfo,
  useGetSelectedRequestInfo,
  useIsOnDemand,
} from '@/stores/realtime.store';
import get from 'lodash/get';
import { useShallow } from 'zustand/react/shallow';
import { useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useGetTimeRangeByLine } from '@/stores/timeRange.store';

export default function SelectRealtimeRequest() {
  const { t } = useTranslation();
  const lineId = useTabLineId() || '';
  const listRequestInfo = useGetListRequestInfo(lineId, 'WIFI_STATISTIC');
  const selectedRequest = useGetSelectedRequestInfo(lineId, 'WIFI_STATISTIC');
  const realtimeRequestId = get(selectedRequest, 'realtimeRequestId') || '';
  const timeRangeSelected = useGetTimeRangeByLine(lineId);
  const endDate = get(timeRangeSelected, 'endDate', 0);
  const isOnDemand = useIsOnDemand(lineId);

  const { updateRequestIdSelection } = useRealtimeConfigStore(
    useShallow((state) => ({ updateRequestIdSelection: state.updateRequestIdSelection })),
  );

  const sortedList = useMemo(() => {
    return listRequestInfo
      .filter((request) => request.realtimeRequestId)
      .sort((a, b) => (b.lastUpdated || 0) - (a.lastUpdated || 0));
  }, [listRequestInfo]);

  useEffect(() => {
    if (isOnDemand && sortedList.length > 0) {
      updateRequestIdSelection(lineId, 'WIFI_STATISTIC', sortedList[0].realtimeRequestId!);
    } else {
      updateRequestIdSelection(lineId, 'WIFI_STATISTIC', '');
    }
    // Intentionally, only update selection state when on-demand changed
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isOnDemand]);

  return (
    <AxonSelect
      value={realtimeRequestId || 'historical'}
      onValueChange={(value) => {
        if (value === 'historical') {
          updateRequestIdSelection(lineId, 'WIFI_STATISTIC', '');
        } else {
          updateRequestIdSelection(lineId, 'WIFI_STATISTIC', value);
        }
      }}>
      <AxonSelectTrigger className='h-9 min-w-[150px] border-none bg-transparent p-0'>
        <AxonSelectValue />
      </AxonSelectTrigger>
      <AxonSelectContent>
        {sortedList.map((request, i) => (
          <AxonSelectItem key={request.realtimeRequestId} value={request.realtimeRequestId!}>
            {i === 0
              ? `${t('latest')} (${getRelativeTimeFromNow(request.lastUpdated!)})`
              : formatDate(request.lastUpdated!, DATETIME_FORMAT.DATE_TIME)}
          </AxonSelectItem>
        ))}
        {/* need to provide non-empty value to avoid ts error */}
        <AxonSelectItem value={'historical'}>{formatDate(endDate, DATETIME_FORMAT.DATE)}</AxonSelectItem>
      </AxonSelectContent>
    </AxonSelect>
  );
}
