import { StatisticWidgetStatus } from '@/components/SharedStatisticComponents/StatisticWidgetStatus';
import { WifiStatsDrawer } from '@/components/WifiStatsDrawer';
import { WifiBandSelect } from '@/components/WifiStatsDrawer/WifiBandSelect';
import { useGetCapabilities } from '@/hooks/useGetCapabilities';
import { useDrawerStore } from '@/stores/drawer.store';
import useRealtimeConfigStore, { useGetSelectedRequestInfo, useLineIsBusy } from '@/stores/realtime.store';
import useTabStore, { useConfigWidgetWifiStatistic, useTabDeviceId } from '@/stores/tab.store';
import { cn } from '@/utils';
import { EQoeStatus } from '@/utils/QOE.util';
import get from 'lodash/get';
import { useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useGetCpeInfo } from 'services/CPEService';
import { useGetRealtimeWifiSpeedtest, useStartRealtimeProcess } from 'services/Realtime';
import { getUnixTime } from 'services/Utils';
import { Loading, Maximize, RefreshCcw } from 'ui/UIAssets';
import { AxonButton, AxonCard, AxonCardContent, AxonSeparator, AxonToast } from 'ui/UIComponents';
import Error from './Error';
import RunSpeedTestModal from './Modals/runSpeedTest';
import Skeleton from './Skeleton';
import { useGetTimeRangeByLine } from '@/stores/timeRange.store';
import { METRIC } from '@/constants';
import { Calendar } from 'lucide-react';
import ContentItem from './ContentItem';
import SelectRealtimeRequest from './SelectRealtimeRequest';
import { useGetData, EDataSection } from 'services/GetData';
import { WifiStatisticsResponse } from './WifiStatistic.type';

const EMPTY_STATS = {
  value: null,
  status: EQoeStatus.UNKNOWN,
  trend: null,
};

const WifiStatistic = () => {
  const { t } = useTranslation();
  const addConfigEnhance = useTabStore((state) => state.addConfigEnhance);
  const deviceId = useTabDeviceId() || '';
  const { openDrawer } = useDrawerStore((state) => ({
    openDrawer: state.openDrawer,
  }));

  const [openRunSpeedTestModal, setOpenRunSpeedTestModal] = useState(false);
  const [isTrend, setIsTrend] = useState(false);

  const selectedBandId = useConfigWidgetWifiStatistic()!.selectedBandId;

  const { data: cpeInfo, isLoading: isLoadingCpeInfo } = useGetCpeInfo(deviceId ?? '');
  const lineId = get(cpeInfo, 'data.lineId') || '';
  const selectedRequest = useGetSelectedRequestInfo(lineId, 'WIFI_STATISTIC');
  const realtimeRequestId = get(selectedRequest, 'realtimeRequestId') || '';
  const isRealtimeDataReady = get(selectedRequest, 'isDataReady');
  const isRealtimeInProcessing = get(selectedRequest, 'isRealtimeRequesting');
  const isLineBusy = useLineIsBusy(lineId);
  const updateRealtimeState = useRealtimeConfigStore((state) => state.updateRealtimeState);

  const timeRangeSelected = useGetTimeRangeByLine(lineId);
  const startDate = get(timeRangeSelected, 'startDate', null);
  const endDate = get(timeRangeSelected, 'endDate', null);

  const isViewingHistory = realtimeRequestId === '';

  const { startRealtimeProcess } = useStartRealtimeProcess();

  const handleGetRealtime = () => {
    if (lineId) {
      startRealtimeProcess(
        {
          lineId,
          metric: METRIC.WIFI_STATISTIC,
        },
        updateRealtimeState,
        (status) => {
          if (status === 'SUCCESS') {
            AxonToast.success(t('customer:realtime.speedTest.success'));
          } else if (status === 'FAILED') {
            AxonToast.error(t('customer:realtime.speedTest.error'));
          }
        },
      );
    }
  };

  const capabilities = useGetCapabilities();

  const {
    data: wifiStatisticsData,
    isLoading: isLoadingWifiStats,
    isError: isErrorWifiStats,
    // @ts-ignore generic type cannot be resolved at run time
  } = useGetData<WifiStatisticsResponse>(
    {
      data: EDataSection.WIFI_STATISTICS,
      deviceId,
      startDate: Number(getUnixTime(startDate)),
      endDate: Number(getUnixTime(endDate)),
    },
    {
      enabled: !isLineBusy && !!deviceId && !!startDate && !!endDate,
      staleTime: Infinity,
    },
  );

  const { data: realtimeWifiSpeedtestData, isFetching: isFetchingWifiSpeedtestRealtime } = useGetRealtimeWifiSpeedtest(
    {
      realtimeRequestId,
      deviceId,
      startDate: getUnixTime(startDate) || 0,
      endDate: getUnixTime(endDate) || 0,
    },
    {
      enabled: Boolean(realtimeRequestId) && Boolean(isRealtimeDataReady) && Boolean(deviceId) && Boolean(startDate),
      staleTime: Infinity,
    },
  );

  const isGettingRealtimeData = useMemo(
    () => isFetchingWifiSpeedtestRealtime || isRealtimeInProcessing,
    [isRealtimeInProcessing, isFetchingWifiSpeedtestRealtime],
  );

  const canRunSpeedTest = get(capabilities, 'isSpeedTestEnabled', false);

  const isCollectRealtimeDataDisabled = useMemo(
    () => isLoadingCpeInfo || !canRunSpeedTest,
    [isLoadingCpeInfo, canRunSpeedTest],
  );

  const wifiStatParameters = isGettingRealtimeData
    ? undefined
    : isViewingHistory
      ? wifiStatisticsData
      : realtimeWifiSpeedtestData;

  function handleChangeOpenDialog(status: boolean) {
    setOpenRunSpeedTestModal(status);
  }

  const handleSubmitDataCheck = () => {
    setOpenRunSpeedTestModal(false);

    handleGetRealtime();
  };

  const isLoading = useMemo(
    () => isLoadingWifiStats || isGettingRealtimeData || isFetchingWifiSpeedtestRealtime,
    [isFetchingWifiSpeedtestRealtime, isLoadingWifiStats, isGettingRealtimeData],
  );

  const handleChangeBandId = (bandId: string) => {
    addConfigEnhance('widgets.wifiStatistic.selectedBandId', bandId);
  };

  const bands = get(wifiStatParameters, 'data.bands');

  useEffect(() => {
    if (!selectedBandId && bands) {
      addConfigEnhance('widgets.wifiStatistic.selectedBandId', bands[0]?.bandId);
    }
  }, [selectedBandId, bands, addConfigEnhance]);

  const lastUpdated = isViewingHistory
    ? get(wifiStatParameters, 'data.lastUpdated', 0)
    : get(selectedRequest, 'lastUpdated');

  const currentStepText = isRealtimeInProcessing
    ? get(selectedRequest, 'currentStepText') || t('processing...')
    : t('device:lanWlan.wifiStats.runSpeedTest.button');

  const statistics = get(wifiStatParameters, `data.result.${selectedBandId}`, {});

  const throughputStats = get(statistics, `throughput`, EMPTY_STATS);
  const latencyStats = get(statistics, `latency`, EMPTY_STATS);
  const congestionStats = get(statistics, `congestion`, EMPTY_STATS);
  const trafficUpStats = get(statistics, `trafficUp`, EMPTY_STATS);
  const trafficDownStats = get(statistics, `trafficDown`, EMPTY_STATS);
  const transmissionUpStats = get(statistics, `txError`, {});
  const transmissionDownStats = get(statistics, `rxError`, {});
  const interfaceScoreStats = get(statistics, `interferenceScore`, EMPTY_STATS);
  const noiseStats = get(statistics, `noise`, EMPTY_STATS);
  const temperatureStats = get(statistics, `temperature`, EMPTY_STATS);
  const status = get(statistics, 'status') || EQoeStatus.UNKNOWN;

  return (
    <AxonCard
      className='bg-surface-tile border-gradient-border'
      radialGradientTypes={[status]}
      radialGradientPosition='left bottom'
      data-testid='device-wifi-statistic-card'>
      <AxonCardContent className='p-0' data-testid='device-wifi-statistic-card-content'>
        <div className='flex flex-col p-6' data-testid='device-wifi-statistic-container'>
          <div className='flex flex-row items-center' data-testid='device-wifi-statistic-header'>
            <h2 className='text-md text-content-primary font-semibold' data-testid='device-wifi-statistic-title'>
              {t('device:lanWlan.wifiStats.title')}
            </h2>
            <div className='ml-auto flex flex-row items-center gap-x-1' data-testid='device-wifi-statistic-controls'>
              <AxonButton
                startDecorator={
                  isRealtimeInProcessing ? (
                    <Loading
                      className={cn('size-4 animate-spin')}
                      data-testid='device-wifi-statistic-refresh-spinner'
                    />
                  ) : (
                    <RefreshCcw className='size-4' />
                  )
                }
                variant='outline'
                disabled={isCollectRealtimeDataDisabled || isRealtimeInProcessing || isLineBusy}
                className='bg-surface-action text-component-hyperlink hover:text-component-hyperlink w-fit border-none shadow-none'
                data-testid='device-wifi-statistic-run-speedtest-button'
                onClick={() => setOpenRunSpeedTestModal(true)}>
                {currentStepText}
              </AxonButton>

              <RunSpeedTestModal
                open={openRunSpeedTestModal}
                onSubmitData={handleSubmitDataCheck}
                onOpenChange={handleChangeOpenDialog}
              />

              <WifiBandSelect
                disabled={isLoadingWifiStats}
                wifiBands={bands}
                selectedBandId={selectedBandId}
                setSelectedBandId={handleChangeBandId}
                data-testid='device-wifi-statistic-band-select'
              />
              <SelectRealtimeRequest />
              <AxonButton
                data-testid='wifi-stats-trend-button'
                variant={isTrend ? 'accent' : 'ghost'}
                onClick={() => setIsTrend((prev) => !prev)}
                aria-label={t('ariaLabel.switchTrendMode')}>
                <Calendar className='size-4' />
              </AxonButton>
              <AxonButton
                variant='ghost'
                aria-label={t('ariaLabel.openWifiStats')}
                onClick={() => openDrawer('wifiStatistics')}
                data-testid='device-wifi-statistic-drawer-button'>
                <Maximize className='size-4 cursor-pointer' />
              </AxonButton>
              <WifiStatsDrawer bands={bands} />
            </div>
          </div>
          {isLoading ? (
            <Skeleton data-testid='device-wifi-statistic-skeleton' />
          ) : isErrorWifiStats ? (
            <Error data-testid='device-wifi-statistic-error' />
          ) : (
            <>
              <div className='grid grid-cols-4'>
                {/* Row 1 */}
                <div
                  className={`flex flex-col gap-y-1 border-b-2 border-r-2 px-4 py-3 pl-0`}
                  data-testid='device-wifi-statistic-speed-cell'>
                  <p
                    className='text-content-secondary line-clamp-1 overflow-hidden text-xs font-medium'
                    data-testid='device-wifi-statistic-speed-label'>
                    {t('device:lanWlan.wifiStats.speed')}
                  </p>
                  <ContentItem
                    label={t('device:lanWlan.wifiStats.speed')}
                    value={throughputStats.value}
                    unit='Mbps'
                    status={throughputStats.status}
                    trend={throughputStats.trend}
                    isTrendView={isTrend}
                    data-testid='device-wifi-statistic-speed-content'
                  />
                </div>
                <div
                  className={`flex flex-col gap-y-1 border-b-2 border-r-2 px-4 py-3`}
                  data-testid='device-wifi-statistic-latency-cell'>
                  <p
                    className='text-content-secondary line-clamp-1 overflow-hidden text-xs font-medium'
                    data-testid='device-wifi-statistic-latency-label'>
                    {t('device:lanWlan.wifiStats.latency')}
                  </p>
                  <ContentItem
                    label={t('device:lanWlan.wifiStats.latency')}
                    value={latencyStats.value}
                    unit='ms'
                    status={latencyStats.status}
                    trend={latencyStats.trend}
                    isTrendView={isTrend}
                    data-testid='device-wifi-statistic-latency-content'
                  />
                </div>
                <div
                  className={`flex flex-col gap-y-1 border-b-2 border-r-2 px-4 py-3`}
                  data-testid='device-wifi-statistic-congestion-cell'>
                  <p
                    className='text-content-secondary line-clamp-1 overflow-hidden text-xs font-medium'
                    data-testid='device-wifi-statistic-congestion-label'>
                    {t('device:lanWlan.wifiStats.congestion')}
                  </p>
                  <ContentItem
                    label={t('device:lanWlan.wifiStats.congestion')}
                    value={congestionStats.value}
                    unit='%'
                    status={congestionStats.status}
                    trend={congestionStats.trend}
                    isTrendView={isTrend}
                    data-testid='device-wifi-statistic-congestion-content'
                  />
                </div>
                <div
                  className={`flex flex-col gap-y-1 border-b-2 px-4 py-3`}
                  data-testid='device-wifi-statistic-traffic-cell'>
                  <p
                    className='text-content-secondary line-clamp-1 overflow-hidden text-xs font-medium'
                    data-testid='device-wifi-statistic-traffic-label'>
                    {t('device:lanWlan.wifiStats.traffic')}
                  </p>
                  <div
                    className='flex flex-wrap items-center gap-x-1'
                    data-testid='device-wifi-statistic-traffic-content'>
                    <ContentItem
                      label={t('device:lanWlan.wifiStats.trafficUp')}
                      data-testid='device-wifi-statistic-traffic-up'
                      value={trafficUpStats.value}
                      unit='GB'
                      status={trafficUpStats.status}
                      trend={trafficUpStats.trend}
                      isTrendView={isTrend}
                    />
                    {(isTrend || trafficDownStats.value != null) && (
                      <span className={cn('text-content-tertiary h-5')}>
                        {!isTrend && trafficUpStats.value === null && 'N/A'} /
                      </span>
                    )}
                    <ContentItem
                      label={t('device:lanWlan.wifiStats.trafficDown')}
                      value={trafficDownStats.value}
                      unit='GB'
                      status={trafficDownStats.status}
                      trend={trafficDownStats.trend}
                      isTrendView={isTrend}
                      data-testid='device-wifi-statistic-traffic-down'
                    />
                  </div>
                </div>
                {/* Row 2 */}
                <div
                  className={`flex flex-col gap-y-1 border-r-2 px-4 py-3 pl-0`}
                  data-testid='device-wifi-statistic-transmission-cell'>
                  <p
                    className='text-content-secondary line-clamp-1 overflow-hidden text-xs font-medium'
                    data-testid='device-wifi-statistic-transmission-label'>
                    {t('device:lanWlan.wifiStats.transmission')}
                  </p>
                  <div
                    className={`flex flex-wrap items-center gap-x-1`}
                    data-testid='device-wifi-statistic-transmission-content'>
                    <ContentItem
                      label={t('device:lanWlan.wifiStats.txStatus')}
                      value={transmissionUpStats.value}
                      status={transmissionUpStats.status}
                      trend={transmissionUpStats.trend}
                      isTrendView={isTrend}
                    />
                    {(isTrend || transmissionDownStats.value != null) && (
                      <span
                        className={cn('text-content-tertiary h-5')}
                        data-testid='device-wifi-statistic-transmission-separator'>
                        {!isTrend && transmissionUpStats.value === null && 'N/A'} /
                      </span>
                    )}
                    <ContentItem
                      label={t('device:lanWlan.wifiStats.rxStatus')}
                      value={transmissionDownStats.value}
                      status={transmissionDownStats.status}
                      trend={transmissionDownStats.trend}
                      isTrendView={isTrend}
                      data-testid='device-wifi-statistic-transmission-down'
                    />
                  </div>
                </div>
                <div
                  className={`flex flex-col gap-y-1 border-r-2 px-4 py-3`}
                  data-testid='device-wifi-statistic-interference-cell'>
                  <p
                    className='text-content-secondary line-clamp-1 overflow-hidden text-xs font-medium'
                    data-testid='device-wifi-statistic-interference-label'>
                    {t('device:lanWlan.wifiStats.interferenceAndNoise')}
                  </p>
                  <div
                    className={`flex flex-wrap items-center gap-x-1`}
                    data-testid='device-wifi-statistic-interference-content'>
                    <ContentItem
                      label={t('device:lanWlan.wifiStats.interferenceScore')}
                      value={interfaceScoreStats.value}
                      unit='%'
                      status={interfaceScoreStats.status}
                      trend={interfaceScoreStats.trend}
                      isTrendView={isTrend}
                      data-testid='device-wifi-statistic-interference-score'
                    />
                    {(isTrend || noiseStats.value != null) && (
                      <span
                        className={cn('text-content-tertiary h-5')}
                        data-testid='device-wifi-statistic-interference-separator'>
                        {!isTrend && interfaceScoreStats.value === null && 'N/A'} /
                      </span>
                    )}
                    <ContentItem
                      label={t('device:lanWlan.wifiStats.noise')}
                      value={noiseStats.value}
                      unit='dBm'
                      status={noiseStats.status}
                      trend={noiseStats.trend}
                      isTrendView={isTrend}
                      data-testid='device-wifi-statistic-noise'
                    />
                  </div>
                </div>
                <div
                  className={`flex flex-col gap-y-1 border-r-2 px-4 py-3`}
                  data-testid='device-wifi-statistic-temperature-cell'>
                  <p
                    className='text-content-secondary line-clamp-1 overflow-hidden text-xs font-medium'
                    data-testid='device-wifi-statistic-temperature-label'>
                    {t('device:lanWlan.wifiStats.temperature')}
                  </p>
                  <ContentItem
                    label={t('device:lanWlan.wifiStats.temperature')}
                    value={temperatureStats.value}
                    unit='°C'
                    status={temperatureStats.status}
                    trend={temperatureStats.trend}
                    isTrendView={isTrend}
                    data-testid='device-wifi-statistic-temperature-content'
                  />
                </div>
              </div>
              <AxonSeparator className='my-6' data-testid='device-wifi-statistic-separator' />
              <StatisticWidgetStatus
                status={status}
                lastUpdated={lastUpdated}
                data-testid='device-wifi-statistic-status'
              />
            </>
          )}
        </div>
      </AxonCardContent>
    </AxonCard>
  );
};

export default WifiStatistic;
