import { EQoeStatus } from '@/utils/QOE.util';
import { WeeklyTrends } from '@/pages/Device/device.type';

export interface WifiStatisticsParams {
  deviceId: string;
  startDate: number;
  endDate?: number;
}

export interface WifiStatsDrawerParams {
  deviceId: string;
  startDate: number;
  endDate?: number;
}

type BaseWifiStatisticItem = {
  value: number | null;
  date: number | null;
  status: EQoeStatus;
  trend: WeeklyTrends[] | null;
};

export interface WifiStatisticsResponse {
  result: {
    [bandId: string]: {
      throughput: BaseWifiStatisticItem;
      latency: BaseWifiStatisticItem;
      congestion: BaseWifiStatisticItem;
      trafficUp: BaseWifiStatisticItem;
      trafficDown: BaseWifiStatisticItem;
      txError: BaseWifiStatisticItem;
      rxError: BaseWifiStatisticItem;
      interferenceScore: BaseWifiStatisticItem;
      noise: BaseWifiStatisticItem;
      temperature: BaseWifiStatisticItem;
      status: EQoeStatus;
    };
  };
  bands: {
    bandId: string;
    bandName: string;
  }[];
  lastUpdated: number;
}

export interface WifiStatsDrawerResponse {
  result: {
    [bandId: string]: {
      type: string;
      minMax: string | null;
      average: string | null;
      latestResult: string | null;
      noOfTest: string | null;
    }[];
  };
  bands: {
    bandId: string;
    bandName: string;
  }[];
  status: EQoeStatus;
  lastUpdated: number;
}
