import { cn } from '@/utils';
import { useTranslation } from 'react-i18next';
import { WarnTooltip } from './WarnTooltip';
import { EQoeStatus } from '@/utils/QOE.util';
import { useMemo } from 'react';
import { WeeklyTrends } from '@/pages/Device/device.type';
import { StatusTrend } from '@/components/StatusTrend';

/**
 * Check if the value is not null or undefined
 * @param value - The value to check
 * @returns true if the value is not null or undefined, false otherwise
 */
function getUnit(value: number | null | undefined, unit?: string): string {
  if (value != null) {
    return String(`${value} ${unit ?? ''}`).trim();
  }

  return '';
}

export default function ContentItem({
  label,
  value,
  unit,
  status,
  trend = null,
  isTrendView = false,
}: {
  label: string;
  value?: number | null;
  unit?: string;
  status?: EQoeStatus;
  trend?: WeeklyTrends[] | null;
  isTrendView?: boolean;
}) {
  const { t } = useTranslation();

  const isWarning = useMemo(() => {
    if (!status) {
      return false;
    }

    return status !== EQoeStatus.UNKNOWN && status !== EQoeStatus.STABLE;
  }, [status]);

  return (
    <div className='flex h-5 items-center gap-x-1 text-lg font-semibold'>
      {isTrendView ? (
        <>
          <StatusTrend data={trend || []} hideDate hideSeparator compact />
        </>
      ) : (
        <>
          <span
            className={cn({
              'text-content-meta-orange': status === EQoeStatus.UNSTABLE,
              'text-content-meta-red': status === EQoeStatus.VERY_UNSTABLE,
            })}>
            {getUnit(value, unit)}
          </span>
          {isWarning && <WarnTooltip label={label} message={t(status || '')} status={status} />}
        </>
      )}
    </div>
  );
}
