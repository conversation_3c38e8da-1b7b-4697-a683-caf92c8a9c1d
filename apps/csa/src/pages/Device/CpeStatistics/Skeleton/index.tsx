import { AxonSeparator, AxonSkeletonLoader } from 'ui/UIComponents';

const Skeleton = () => {
  return (
    <div className='flex flex-col gap-y-6'>
      {/* Stats grid section */}
      <div className='h-[133px] w-full'>
        {/* Upper row */}
        <div className='divide-border-flat grid w-full grid-cols-4 divide-x-2'>
          <div className='flex flex-col gap-y-1 p-3'>
            <AxonSkeletonLoader className='h-4 w-16' />
            <AxonSkeletonLoader className='h-6 w-32' />
          </div>
          <div className='flex flex-col gap-y-1 p-3'>
            <AxonSkeletonLoader className='h-4 w-16' />
            <AxonSkeletonLoader className='h-6 w-32' />
          </div>
          <div className='flex flex-col gap-y-1 p-3'>
            <AxonSkeletonLoader className='h-4 w-16' />
            <AxonSkeletonLoader className='h-6 w-32' />
          </div>
          <div className='flex flex-col gap-y-1 p-3'>
            <AxonSkeletonLoader className='h-4 w-16' />
            <AxonSkeletonLoader className='h-6 w-32' />
          </div>
        </div>

        {/* Lower row */}
        <div className='divide-border-flat border-t-border-flat grid w-full grid-cols-4 divide-x-2 border-t'>
          <div className='flex flex-col gap-y-1 p-3'>
            <AxonSkeletonLoader className='h-4 w-16' />
            <AxonSkeletonLoader className='h-6 w-32' />
          </div>
          <div className='flex flex-col gap-y-1 p-3'>
            <AxonSkeletonLoader className='h-4 w-16' />
            <AxonSkeletonLoader className='h-6 w-32' />
          </div>
          <div className='flex flex-col gap-y-1 p-3'>
            <AxonSkeletonLoader className='h-4 w-16' />
            <AxonSkeletonLoader className='h-6 w-32' />
          </div>
          <div className='flex flex-col gap-y-1 p-3'>
            <AxonSkeletonLoader className='h-4 w-16' />
            <AxonSkeletonLoader className='h-6 w-32' />
          </div>
        </div>
      </div>

      <AxonSeparator />

      {/* Status section */}
      <div className='flex items-center justify-between'>
        <AxonSkeletonLoader className='h-4 w-80' />
      </div>
    </div>
  );
};

export default Skeleton;
