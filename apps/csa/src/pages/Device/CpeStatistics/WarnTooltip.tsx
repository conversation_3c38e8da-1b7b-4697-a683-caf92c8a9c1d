import { BoldDangerCircle } from 'ui/UIAssets';

import { cn } from '@/utils';
import { DetectionStatusEnum } from '@/utils/detection.util';
import { useTranslation } from 'react-i18next';
import { AxonButton, AxonTooltip, AxonTooltipContent, AxonTooltipProvider, AxonTooltipTrigger } from 'ui/UIComponents';

type WarnTooltipProps = {
  detectionStatus: DetectionStatusEnum;
};

export function CpeStatWarnTooltip({ detectionStatus }: WarnTooltipProps) {
  const { t } = useTranslation();
  return (
    <AxonTooltipProvider>
      <AxonTooltip>
        <AxonTooltipTrigger asChild>
          <AxonButton variant='pure' data-testid='device-cpe-statistics-warn-tooltip-trigger' aria-label={t('status')}>
            <BoldDangerCircle
              className={cn('', {
                'text-content-meta-orange': DetectionStatusEnum.UNSTABLE === detectionStatus,
                'text-content-meta-red': DetectionStatusEnum.VERY_UNSTABLE === detectionStatus,
              })}
            />
          </AxonButton>
        </AxonTooltipTrigger>
        <AxonTooltipContent
          side='bottom'
          className={cn('w-auto p-0')}
          align='start'
          data-testid='device-cpe-statistics-warn-tooltip-content'>
          <div
            className={cn('flex items-center gap-x-2 px-2 py-1', {
              'text-content-meta-yellow': DetectionStatusEnum.UNSTABLE === detectionStatus,
              'text-content-meta-red': DetectionStatusEnum.VERY_UNSTABLE === detectionStatus,
            })}
            data-testid='device-cpe-statistics-warn-tooltip-status'>
            <div
              className={cn('rounded-full p-0.5', {
                'bg-content-meta-yellow': DetectionStatusEnum.UNSTABLE === detectionStatus,
                'bg-content-meta-red': DetectionStatusEnum.VERY_UNSTABLE === detectionStatus,
              })}
            />
            <span className='font-semibold' data-testid='device-cpe-statistics-warn-tooltip-status-text'>
              {t(detectionStatus)}
            </span>
          </div>
        </AxonTooltipContent>
      </AxonTooltip>
    </AxonTooltipProvider>
  );
}
