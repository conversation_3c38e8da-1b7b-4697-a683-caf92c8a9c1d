import { StatisticWidgetStatus } from '@/components/SharedStatisticComponents/StatisticWidgetStatus';
import { StatusTrend } from '@/components/StatusTrend';
import { CpeStatWarnTooltip } from '@/pages/Device/CpeStatistics/WarnTooltip';
import { useDrawerStore } from '@/stores/drawer.store';
import { useTabDeviceId, useTabLineId } from '@/stores/tab.store';
import { useGetTimeRangeByLine } from '@/stores/timeRange.store';
import { cn } from '@/utils';
import { convertDetectionStatus, DetectionStatusEnum } from '@/utils/detection.util';
import { EQoeStatus } from '@/utils/QOE.util';
import get from 'lodash/get';
import { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useGetCpeInfo } from 'services/CPEService';
import { EDataSection, useGetData } from 'services/GetData';
import { getUnixTime } from 'services/Utils';
import { LinearCalendarMinimalistic, Maximize } from 'ui/UIAssets';
import { AxonButton, AxonCard, AxonCardContent, AxonSeparator } from 'ui/UIComponents';
import { useShallow } from 'zustand/react/shallow';
import { DeviceType } from '../device.type';
import Error from './Error';
import Skeleton from './Skeleton';
import { CpeStatsDetailResponse } from './types';

const CpeStatistics = () => {
  const { t } = useTranslation();

  const [isTrend, setIsTrend] = useState(false);

  const lineId = useTabLineId() || '';
  const deviceId = useTabDeviceId() || '';
  const timeRangeSelected = useGetTimeRangeByLine(lineId);
  const startDate = get(timeRangeSelected, 'startDate', null);
  const endDate = get(timeRangeSelected, 'endDate', null);

  const openDrawer = useDrawerStore(useShallow((state) => state.openDrawer));
  const { data: cpeInfo } = useGetCpeInfo(deviceId);

  const isExtenderDevice = cpeInfo?.data?.deviceType === DeviceType.EXTENDER;

  const {
    data: queryData,
    isLoading,
    isError,
    // @ts-ignore remote import can't resolve generic type at runtime
  } = useGetData<CpeStatsDetailResponse>(
    {
      data: EDataSection.CPE_STATISTICS,
      deviceId,
      startDate: getUnixTime(startDate) || 0,
      endDate: getUnixTime(endDate) || 0,
    },
    { enabled: !!lineId && !!deviceId && !!startDate, staleTime: Infinity },
  );

  const data = queryData?.data?.data;
  const { status, lastUpdated, lastBootupTime } = queryData?.data || {};

  const upperRow = data?.slice(0, 4);
  const lowerRow = isExtenderDevice ? data?.slice(4, 7) : [...(data?.slice(4, 5) || [])];

  return (
    <AxonCard
      radialGradientTypes={[status || EQoeStatus.UNKNOWN]}
      className='border-gradient-border bg-surface-tile'
      data-testid='device-cpe-statistics-card'>
      <AxonCardContent className='p-0'>
        <div className='gap-xl flex h-[308px] flex-col justify-between p-6'>
          <div className='flex w-full items-center justify-between'>
            <h2 className={'text-md text-content-primary font-medium'}>{t('device:cpe.cpeStats.title')}</h2>
            <div className='flex items-center justify-between gap-x-1'>
              <AxonButton
                data-testid='cpe-stats-trend-button'
                variant={isTrend ? 'accent' : 'ghost'}
                onClick={() => setIsTrend((prev) => !prev)}
                aria-label={t('ariaLabel.switchTrendMode')}>
                <LinearCalendarMinimalistic className='size-4' />
              </AxonButton>
              <AxonButton
                data-testid='cpe-stats-open-drawer-button'
                variant={'ghost'}
                onClick={() => openDrawer('cpeStatistics')}
                aria-label={t('ariaLabel.openCpeStats')}>
                <Maximize className='size-4' />
              </AxonButton>
            </div>
          </div>

          {isLoading ? (
            <Skeleton />
          ) : isError ? (
            <Error />
          ) : (
            <>
              <div className='h-[133px] w-full'>
                <div className='divide-border-flat h- grid w-full grid-cols-4 divide-x-2'>
                  {upperRow?.map((cpeStats, i) => <StatItem key={i} statItem={cpeStats} isTrend={isTrend} />)}
                </div>

                <div className='divide-border-flat border-t-border-flat grid w-full grid-cols-4 divide-x-2 border-t'>
                  {lowerRow?.map((cpeStats, i) => <StatItem key={i} statItem={cpeStats} isTrend={isTrend} />)}
                </div>
              </div>

              <AxonSeparator />

              <StatisticWidgetStatus
                status={status || EQoeStatus.UNKNOWN}
                lastUpdated={lastUpdated}
                lastBootupTime={lastBootupTime}
                data-testid='device-cpe-statistics-status'
              />
            </>
          )}
        </div>
      </AxonCardContent>
    </AxonCard>
  );
};

interface CpeStatsDetail {
  title: string;
  value: number | string | null;
  unit: string;
  lastUpdated: null;
  description: string;
  detection: number | null;
  trend: { status: string; date: number }[] | null;
}

const StatItem = ({ statItem, isTrend }: { statItem: CpeStatsDetail; isTrend: boolean }) => {
  const detectionStatus = useMemo(() => convertDetectionStatus(statItem.detection), [statItem.detection]);

  const key = statItem.title.toLowerCase().replace(/\s+/g, '-');

  return (
    <div className='flex flex-col gap-y-1 p-3'>
      <div className='text-content-secondary font-book text-xs' data-testid={`device-cpe-statistics-${key}-title`}>
        {statItem.title}
      </div>
      <div className='flex h-6 items-center gap-1'>
        {isTrend && statItem.trend && <StatusTrend data={statItem.trend} hideDate hideSeparator compact />}
        {!isTrend && typeof statItem.value === 'number' && (
          <span
            className={cn('text-content-primary text-lg font-medium', {
              'text-content-meta-orange': DetectionStatusEnum.UNSTABLE === detectionStatus,
              'text-content-meta-red': DetectionStatusEnum.VERY_UNSTABLE === detectionStatus,
            })}
            data-testid={`device-cpe-statistics-${key}-value`}>
            {`${statItem.value} ${statItem.unit}`}
          </span>
        )}
        {[DetectionStatusEnum.UNSTABLE, DetectionStatusEnum.VERY_UNSTABLE].includes(detectionStatus) && (
          <CpeStatWarnTooltip detectionStatus={detectionStatus} />
        )}
      </div>

      {statItem.description && (
        <div
          className='font-book text-content-secondary text-xs'
          data-testid={`device-cpe-statistics-${key}-description`}>
          {statItem.description}
        </div>
      )}
    </div>
  );
};

export default CpeStatistics;
