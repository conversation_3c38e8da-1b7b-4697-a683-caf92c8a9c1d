import { AxonSeparator, AxonSkeletonLoader } from 'ui/UIComponents';

const Skeleton = () => {
  return (
    <>
      <div className='flex items-center gap-4'>
        <p className='text-content-tertiary text-xs font-medium uppercase tracking-[.04em] [writing-mode:sideways-lr]'>
          <AxonSkeletonLoader className='h-20 w-4' />
        </p>
        <div className='flex w-full flex-col'>
          <div className='flex w-full items-center divide-x-2'>
            <div className='flex flex-1 flex-col gap-y-2 p-2 pl-0'>
              <AxonSkeletonLoader className='h-5 w-12' />
              <AxonSkeletonLoader className='h-5 w-24' />
            </div>
            <div className='flex flex-1 flex-col gap-y-2 p-2'>
              <AxonSkeletonLoader className='h-5 w-12' />
              <AxonSkeletonLoader className='h-5 w-24' />
            </div>
            <div className='flex flex-1 flex-col gap-y-2 p-2'>
              <AxonSkeletonLoader className='h-5 w-12' />
              <AxonSkeletonLoader className='h-5 w-24' />
            </div>
            <div className='flex flex-1 flex-col gap-y-2 p-2'>
              <AxonSkeletonLoader className='h-5 w-12' />
              <AxonSkeletonLoader className='h-5 w-24' />
            </div>
          </div>
          <div className='border-t-gradient-border flex w-full items-center divide-x-2 border-t'>
            <div className='flex flex-1 flex-col gap-y-2 p-2 pl-0'>
              <AxonSkeletonLoader className='h-5 w-12' />
              <AxonSkeletonLoader className='h-5 w-24' />
            </div>
            <div className='flex flex-1 flex-col gap-y-2 p-2'>
              <AxonSkeletonLoader className='h-5 w-12' />
              <AxonSkeletonLoader className='h-5 w-24' />
            </div>
            <div className='flex flex-1 flex-col gap-y-2 p-2'>
              <AxonSkeletonLoader className='h-5 w-12' />
              <AxonSkeletonLoader className='h-5 w-24' />
            </div>
            <div className='flex flex-1 flex-col gap-y-2 p-2'>
              <AxonSkeletonLoader className='h-5 w-12' />
              <AxonSkeletonLoader className='h-5 w-24' />
            </div>
          </div>
        </div>
      </div>

      <div className='mt-[3px] flex items-center gap-4'>
        <p className='text-content-tertiary text-xs font-medium uppercase tracking-[.04em] [writing-mode:sideways-lr]'>
          <AxonSkeletonLoader className='h-20 w-4' />
        </p>
        <div className='flex w-full flex-col'>
          <div className='flex w-full items-center divide-x-2'>
            <div className='flex flex-1 flex-col gap-y-2 p-2 pl-0'>
              <AxonSkeletonLoader className='h-5 w-12' />
              <AxonSkeletonLoader className='h-5 w-24' />
            </div>
            <div className='flex flex-1 flex-col gap-y-2 p-2'>
              <AxonSkeletonLoader className='h-5 w-12' />
              <AxonSkeletonLoader className='h-5 w-24' />
            </div>
            <div className='flex flex-1 flex-col gap-y-2 p-2'>
              <AxonSkeletonLoader className='h-5 w-12' />
              <AxonSkeletonLoader className='h-5 w-24' />
            </div>
            <div className='flex flex-1 flex-col gap-y-2 p-2'>
              <AxonSkeletonLoader className='h-5 w-12' />
              <AxonSkeletonLoader className='h-5 w-24' />
            </div>
          </div>
          <div className='border-t-gradient-border flex w-full items-center divide-x-2 border-t'>
            <div className='flex flex-1 flex-col gap-y-2 p-2 pl-0'>
              <AxonSkeletonLoader className='h-5 w-12' />
              <AxonSkeletonLoader className='h-5 w-24' />
            </div>
            <div className='flex flex-1 flex-col gap-y-2 p-2'>
              <AxonSkeletonLoader className='h-5 w-12' />
              <AxonSkeletonLoader className='h-5 w-24' />
            </div>
            <div className='flex flex-1 flex-col gap-y-2 p-2'>
              <AxonSkeletonLoader className='h-5 w-12' />
              <AxonSkeletonLoader className='h-5 w-24' />
            </div>
            <div className='flex flex-1 flex-col gap-y-2 p-2'>
              <AxonSkeletonLoader className='h-5 w-12' />
              <AxonSkeletonLoader className='h-5 w-24' />
            </div>
          </div>
        </div>
      </div>

      <AxonSeparator />
      <div>
        <AxonSkeletonLoader className='h-5 w-80' />
      </div>
    </>
  );
};

export default Skeleton;
