import { ComponentProps } from 'react';

const StableStatusIcon = (props: ComponentProps<'svg'>) => {
  return (
    <svg
      xmlns='http://www.w3.org/2000/svg'
      width='16'
      height='16'
      viewBox='0 0 16 16'
      fill='none'
      color='currentColor'
      {...props}>
      <path
        d='M8.00033 9.33464C8.73671 9.33464 9.33366 8.73768 9.33366 8.0013C9.33366 7.26492 8.73671 6.66797 8.00033 6.66797C7.26395 6.66797 6.66699 7.26492 6.66699 8.0013C6.66699 8.73768 7.26395 9.33464 8.00033 9.33464Z'
        fill='currentColor'
      />

      <g>
        <path
          d='M3.64427 2.92805C3.83933 3.12351 3.83902 3.44009 3.64356 3.63516C2.52449 4.752 1.83301 6.29478 1.83301 8.00002C1.83301 9.72519 2.54076 11.2841 3.68303 12.4039C3.88022 12.5972 3.88337 12.9138 3.69006 13.111C3.49675 13.3082 3.18018 13.3113 2.98299 13.118C1.65677 11.8179 0.833008 10.0047 0.833008 8.00002C0.833008 6.01855 1.63781 4.2241 2.93716 2.92734C3.13262 2.73228 3.4492 2.73259 3.64427 2.92805Z'
          fill='currentColor'
        />
        <path
          d='M12.4106 2.97631C12.6078 2.78299 12.9244 2.78614 13.1177 2.98333C14.3844 4.27551 15.1663 6.04695 15.1663 8.00002C15.1663 9.97631 14.3657 11.7666 13.0724 13.0625C12.8773 13.258 12.5607 13.2583 12.3652 13.0632C12.1698 12.8682 12.1695 12.5516 12.3645 12.3561C13.4785 11.24 14.1663 9.70082 14.1663 8.00002C14.1663 6.31915 13.4945 4.7962 12.4036 3.68338C12.2103 3.48618 12.2134 3.16962 12.4106 2.97631Z'
          fill='currentColor'
        />
      </g>

      <g>
        <path
          d='M5.53949 4.99039C5.72817 5.19202 5.71768 5.50843 5.51605 5.69711C4.88345 6.2891 4.5 7.09884 4.5 7.98683C4.5 8.88516 4.89246 9.70352 5.53837 10.2973C5.74167 10.4841 5.75499 10.8004 5.56811 11.0037C5.38123 11.207 5.06493 11.2204 4.86163 11.0335C4.02645 10.2658 3.5 9.18694 3.5 7.98683C3.5 6.80067 4.01432 5.73286 4.83277 4.96696C5.0344 4.77827 5.35081 4.78877 5.53949 4.99039Z'
          fill='currentColor'
        />
        <path
          d='M10.4952 5.01574C10.6861 4.81619 11.0026 4.80915 11.2022 5.00002C12.0003 5.76344 12.5 6.81773 12.5 7.98683C12.5 9.16988 11.9884 10.2352 11.1736 11.0007C10.9724 11.1898 10.656 11.1799 10.4669 10.9787C10.2778 10.7774 10.2876 10.461 10.4889 10.2719C11.1185 9.68033 11.5 8.87253 11.5 7.98683C11.5 7.11146 11.1274 6.31232 10.511 5.72267C10.3114 5.5318 10.3044 5.21529 10.4952 5.01574Z'
          fill='currentColor'
        />
      </g>
    </svg>
  );
};

export default StableStatusIcon;
