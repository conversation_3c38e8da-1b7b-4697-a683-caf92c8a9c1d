import {
  AxonAreaChart,
  AxonCard,
  AxonCardContent,
  AxonSeparator,
  AxonTooltip,
  AxonTooltipTrigger,
  AxonTooltipContent,
  AxonTooltipProvider,
} from 'ui/UIComponents';
import { toRoundedDecimalNumber } from '@/utils/number';
import { cn } from '@/utils';
import { ArrowDown, ArrowUp, Minus } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useMemo } from 'react';
import { DATETIME_FORMAT, formatDate } from 'services/Utils';

interface Props {
  name: string;
  unit: string;
  points: Array<{ value: number; pct: number; timestamp: number }>;
}

export const InitialScreenReport = ({ name, unit, points }: Props) => {
  const { t } = useTranslation();

  const sortedResults = useMemo(() => {
    const uniqueTimestamps = Array.from(new Set(points.map((p) => p.timestamp)));

    return uniqueTimestamps.sort().map((timestamp) => {
      const filteredPoints = points.filter((p) => p.timestamp === timestamp);
      // Kaan: some of the value will have the same timestamp, we need to unify them
      const sumValues = filteredPoints.reduce((acc, curr) => acc + curr.value, 0);
      const sumPcts = filteredPoints.reduce((acc, curr) => acc + curr.pct, 0);

      return {
        timestamp,
        date: formatDate(new Date(timestamp)),
        value: unit === '%' ? sumPcts : sumValues,
        number: sumValues,
        pct: sumPcts,
      };
    });
  }, [points, unit]);

  const latestValue = useMemo(() => sortedResults[sortedResults.length - 1]?.value, [sortedResults]);

  const changedInPercentage = useMemo(() => {
    const latestValue = sortedResults[sortedResults.length - 1]?.value;

    if (isNaN(latestValue)) return 0;

    const allExceptLatest = sortedResults.slice(0, sortedResults.length - 1);
    const average = allExceptLatest.reduce((acc, curr) => acc + curr.value, 0) / allExceptLatest.length;

    return ((latestValue - average) * 100) / average;
  }, [sortedResults]);

  const changeStatus: 'good' | 'bad' | 'none' = useMemo(() => {
    if (unit === '%') {
      if (changedInPercentage > 0) return 'bad';
      if (changedInPercentage < 0) return 'good';
    } else {
      if (changedInPercentage > 0) return 'good';
      if (changedInPercentage < 0) return 'bad';
    }
    return 'none';
  }, [changedInPercentage, unit]);

  // no change or infinite change will display in gray text
  const isChangeNeutral =
    isNaN(changedInPercentage) || changedInPercentage === 0 || Math.abs(changedInPercentage) === Infinity;

  return (
    <AxonCard data-testid={`report-analysis-${name}`} radialGradientTypes={['success']}>
      <AxonCardContent className='flex h-full flex-col justify-end p-5'>
        <div className='flex flex-1 flex-row items-start gap-x-2'>
          <div className='flex h-full w-1/2 flex-col justify-between gap-y-1'>
            <AxonTooltipProvider>
              <AxonTooltip>
                <AxonTooltipTrigger asChild>
                  <p
                    data-testid={`report-analysis-${name}-title`}
                    className='text-content-primary font-book overflow-hidden text-ellipsis text-sm opacity-60'>
                    {t(`welcomeScreen:${name}`)}
                  </p>
                </AxonTooltipTrigger>
                <AxonTooltipContent>{t(`welcomeScreen:${name}`)}</AxonTooltipContent>
              </AxonTooltip>
            </AxonTooltipProvider>
            <p data-testid={`report-analysis-${name}-value`} className='w-full text-2xl font-medium'>
              {toRoundedDecimalNumber(latestValue, 5)}
              {unit}
            </p>
          </div>
          <div data-testid={`report-analysis-${name}-chart`} className='flex h-full flex-1 items-center'>
            <AxonAreaChart
              className='aspect-auto h-16 w-full'
              chartData={sortedResults}
              xAxisDataKey='timestamp'
              xAxisType='time'
              color={changeStatus === 'bad' ? 'red' : 'green'}
              valueDataKey='value'
              tooltip={{
                formatter: (_a, _b, item) => {
                  return (
                    <div>
                      <p>
                        {item.payload.number} {t('APs')} - {toRoundedDecimalNumber(item.payload.pct, 5)}%
                      </p>
                      <p>{formatDate(item.payload.timestamp, DATETIME_FORMAT.DATE)}</p>
                    </div>
                  );
                },
              }}
            />
          </div>
        </div>
        <AxonSeparator className='my-3' />
        <div className='flex flex-row flex-wrap items-center gap-x-2 overflow-auto'>
          <div
            className={cn(
              'bg-content-primary/20 text-content-primary rounded-xs p-1',
              !isChangeNeutral && changeStatus === 'good' && 'bg-content-meta-green/20 text-content-meta-green',
              !isChangeNeutral && changeStatus === 'bad' && 'bg-content-meta-red/20 text-content-meta-red',
            )}>
            {isChangeNeutral ? (
              <Minus size={12} />
            ) : changedInPercentage > 0 ? (
              <ArrowUp size={12} />
            ) : (
              <ArrowDown size={12} />
            )}
          </div>
          <p
            data-testid={`report-analysis-${name}-change-text`}
            className={cn(
              'text-content-primary text-xs font-medium',
              !isChangeNeutral && changeStatus === 'good' && 'text-content-meta-green',
              !isChangeNeutral && changeStatus === 'bad' && 'text-content-meta-red',
            )}>
            {changedInPercentage !== 0 ? toRoundedDecimalNumber(changedInPercentage, 2) : null}
            {!isChangeNeutral && '%'} {changedInPercentage === 0 && t('translation:home.noChange')}
          </p>
          <p className='font-book text-xs opacity-60'>{t('translation:home.vsLastPeriod')}</p>
        </div>
      </AxonCardContent>
    </AxonCard>
  );
};
