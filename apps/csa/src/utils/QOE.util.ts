export enum EQoeStatus {
  STABLE = 'stable',
  UNSTABLE = 'unstable',
  VERY_UNSTABLE = 'veryUnstable',
  UNKNOWN = 'unknown',
}

export const convertQOEStatus = (status: number | null | undefined): EQoeStatus => {
  if (typeof status === 'number') {
    if (status >= 4) {
      return EQoeStatus.STABLE;
    }

    if (status >= 2) {
      return EQoeStatus.UNSTABLE;
    }

    if (status >= 0) {
      return EQoeStatus.VERY_UNSTABLE;
    }
  }

  return EQoeStatus.UNKNOWN;
};
