export const toRoundedDecimalNumber = (inputNumber: unknown, roundedDecimalLevel = 2): number | null => {
  if (!isNumber(inputNumber)) {
    return null;
  }

  const pow = Math.pow(10, roundedDecimalLevel);
  return Math.round(Number(inputNumber) * pow) / pow;
};

/**
 * Check if the input is a number. String input will be coerced to a number.
 * @param inputNumber - The input to check
 * @returns true if the input is a number, false otherwise
 */
export const isNumber = (inputNumber: any): boolean => {
  if (typeof inputNumber === 'string') {
    return isFinite(Number(inputNumber));
  }
  return Number.isFinite(inputNumber);
};
