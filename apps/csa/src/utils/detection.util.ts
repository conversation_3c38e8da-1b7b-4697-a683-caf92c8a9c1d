export enum DetectionStatusEnum {
  STABLE = 'stable',
  UNSTABLE = 'unstable',
  VERY_UNSTABLE = 'veryUnstable',
  UNKNOWN = 'unknown',
}

export const convertDetectionStatus = (status: number | null): DetectionStatusEnum => {
  if (typeof status === 'number') {
    if (status >= 4) {
      return DetectionStatusEnum.VERY_UNSTABLE;
    }

    if (status >= 2) {
      return DetectionStatusEnum.UNSTABLE;
    }

    if (status >= 0) {
      return DetectionStatusEnum.STABLE;
    }
  }

  return DetectionStatusEnum.UNKNOWN;
};
