import { useTranslation } from 'react-i18next';

import {
  AxonButton,
  AxonCard,
  AxonDropdownMenu,
  AxonDropdownMenuContent,
  AxonDropdownMenuItem,
  AxonDropdownMenuTrigger,
  AxonSeparator,
} from 'ui/UIComponents';
import { useDrawerStore } from '@/stores/drawer.store';
import { useTabLineId } from '@/stores/tab.store';
import { useGetLineInfo } from 'services/LineInfo';
import { BoldCard, BoldHome, Book, DTLogo, MoreHorizontal } from 'ui/UIAssets';
import { useShallow } from 'zustand/react/shallow';
import CustomerBadges from './CustomerBadges';
import CustomerDetails from './CustomerDetails';
import CustomerError from './CustomerError';
import { CustomerInfoSkeleton } from './CustomerInfoSkeleton';
import CustomerNoData from './CustomerNoData';
import CustomerViewNumber from './CustomerViewNumber';
import ISPInfo from './ISPInfo';

const CustomerInfo = () => {
  const { t } = useTranslation();
  const lineId = useTabLineId();
  const { data, isLoading, isError } = useGetLineInfo(lineId ?? '');

  const openDrawer = useDrawerStore(useShallow((state) => state.openDrawer));

  if (isLoading) return <CustomerInfoSkeleton />;
  if (isError) return <CustomerError />;
  if (!data?.data) return <CustomerNoData />;

  const lineInfo = data.data.network ?? {};

  return (
    <AxonCard className='border-gradient-border'>
      <div className='flex min-h-[135px] flex-row items-center gap-x-5 p-5 font-medium'>
        <div className='border-gradient-border rounded-md border p-2'>
          <BoldHome className='text-content-tertiary size-16' />
        </div>

        <div className='scrollbar-md flex flex-col gap-y-1 overflow-auto'>
          <p className='text-content-tertiary text-xs uppercase leading-tight'>
            {t('customer:customerInfo.customerNetwork')}
          </p>
          <div className='mb-2 flex items-center gap-2'>
            <p data-testid={`customer-info-network-id`} className='text-2xl leading-tight'>
              {lineInfo?.lineId}
            </p>
            <CustomerBadges />
          </div>
          <div className='flex items-center gap-3'>
            <CustomerDetails />
            <ISPInfo />
            {(lineInfo.servicePlanName || lineInfo.phoneNumber) && (
              <AxonSeparator orientation='vertical' className='h-3' />
            )}
            <div className='flex items-center gap-1 *:text-sm'>
              {lineInfo.servicePlanName && (
                <p
                  data-testid={`customer-info-service-plan`}
                  className='text-component-hyperlink flex items-center gap-1'>
                  <BoldCard className='size-4' aria-hidden={true} />
                  {lineInfo.servicePlanName}
                </p>
              )}
              {lineInfo.phoneNumber && <CustomerViewNumber phoneNumber={lineInfo.phoneNumber} />}
            </div>
          </div>
        </div>
        <div className='ml-auto flex items-center gap-2'>
          <p className='text-content-tertiary text-sm'>1 service</p>
          <AxonButton
            aria-label={t('ariaLabel.moreActions')}
            data-testid={`customer-info-more-button`}
            variant={'outline'}
            size='icon'
            onClick={() => openDrawer('serviceSummary')}>
            <img src={DTLogo} alt={t('dtLogoAlt')} className='size-4 object-contain' />
          </AxonButton>
          <AxonDropdownMenu>
            <AxonDropdownMenuTrigger asChild>
              <AxonButton
                aria-label={t('ariaLabel.moreActions')}
                data-testid={`customer-info-more-button`}
                variant={'outline'}
                size='icon'>
                <MoreHorizontal />
              </AxonButton>
            </AxonDropdownMenuTrigger>
            <AxonDropdownMenuContent>
              <AxonDropdownMenuItem
                data-testid={`customer-info-more-button-log`}
                className='cursor-pointer'
                onClick={() => openDrawer('event')}>
                <Book className='mr-2 size-4' />
                {t('logList')}
              </AxonDropdownMenuItem>
            </AxonDropdownMenuContent>
          </AxonDropdownMenu>
        </div>
      </div>
    </AxonCard>
  );
};

export default CustomerInfo;
