import React from 'react';
import { useTranslation } from 'react-i18next';
import { AxonCard } from 'ui/UIComponents';

const CustomerNoData = () => {
  const { t } = useTranslation();
  return (
    <AxonCard>
      <p className='text-content-tertiary flex h-[135px] flex-row items-center justify-center gap-x-5 p-5'>
        {t('noData')}
      </p>
    </AxonCard>
  );
};

export default CustomerNoData;
