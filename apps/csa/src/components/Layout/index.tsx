import useSyncTimeRangeStoreAndTabStore from '@/hooks/useSyncTimeRangeStoreAndTabStore';
import { useTabViewSync } from '@/hooks/useTabViewSync';
import { useUpdateTimeZone } from '@/hooks/useTimeZone';
import { useTabId, useTabType } from '@/stores/tab.store';
import { lazy, useMemo } from 'react';
import { useIdleLogout } from 'services/Hooks';
import { AxonSeparator } from 'ui/UIComponents';
import CpeStatsDrawer from '../CpeStatsDrawer';
import ServicesDrawer from '../drawers/ServicesDrawer';

const Chatbot = lazy(() => import('../Chatbot'));
const NetworkTopologyPopover = lazy(() => import('../NetworkTopology'));
const Sidebar = lazy(() => import('./Sidebar'));
const Header = lazy(() => import('./Header'));
const Customer = lazy(() => import('@/pages/Customer'));
const InitialScreen = lazy(() => import('@/pages/InitialScreen'));
const Device = lazy(() => import('@/pages/Device'));

export const Layout = () => {
  useTabViewSync();
  useIdleLogout();
  useUpdateTimeZone();
  useSyncTimeRangeStoreAndTabStore();

  const tabId = useTabId();
  const tabType = useTabType();

  const tabContent = useMemo(() => {
    if (!tabId) return <InitialScreen />;
    switch (tabType) {
      case 'customer':
        return <Customer />;
      case 'device':
        return <Device />;
    }
  }, [tabId, tabType]);

  return (
    <div className='relative flex h-screen flex-col'>
      <div className='min-h-[56px]'>
        <Header />
      </div>
      <div className='flex h-[calc(100vh-56px)] flex-1 flex-row'>
        <div className='scrollbar-lg h-full w-[256px] overflow-auto'>
          <Sidebar />
        </div>
        <AxonSeparator orientation='vertical' />
        <div className='scrollbar-hidden bg-surface-section axon- relative flex-1 overflow-auto' id='main-content'>
          <div className='absolute size-full p-10 pb-4'>{tabContent}</div>
        </div>
      </div>
      {/* <GlobalErrorDialog /> */}
      {process.env.ENABLE_CHATBOT === 'true' && <Chatbot />}
      <NetworkTopologyPopover />
      <ServicesDrawer />
      <CpeStatsDrawer />
    </div>
  );
};
