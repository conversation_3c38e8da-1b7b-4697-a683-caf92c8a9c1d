import { useQueryClient } from '@tanstack/react-query';
import debounce from 'lodash/debounce';
import { useState } from 'react';
import { AxonAlertDialogWrapper } from 'ui/UIComponents';

const GlobalErrorDialog = () => {
  const [currentError, setCurrentError] = useState<string | null>(null);
  const [errorCount, setErrorCount] = useState(0);
  const queryClient = useQueryClient();

  const debouncedSetError = debounce((message: string) => {
    setCurrentError(message);
    setErrorCount(errorCount + 1);
  }, 0);

  queryClient.getQueryCache().subscribe((event: any) => {
    if (event?.action?.type === 'failed' && event?.action?.error) {
      const error = event?.action?.error as { data: { code: number; message: string } };
      debouncedSetError(error?.data?.message);
    }
  });
  if (currentError) {
    return (
      <AxonAlertDialogWrapper
        open
        title={'Something went wrong'}
        variant='danger'
        description={currentError + ` (${errorCount})`}
        confirmText='OK'
        onConfirm={() => {
          setCurrentError(null);
        }}
      />
    );
  }
  return null;
};

export default GlobalErrorDialog;
