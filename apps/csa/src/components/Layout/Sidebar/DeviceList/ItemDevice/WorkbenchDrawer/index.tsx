import { useDrawerStore, useWorkbenchDrawer } from '@/stores/drawer.store';
import useTabStore, { useTabLineId } from '@/stores/tab.store';
import {
  ExpandedState,
  getCoreRowModel,
  getExpandedRowModel,
  getFilteredRowModel,
  getSortedRowModel,
  Updater,
  useReactTable,
} from '@tanstack/react-table';
import { Search } from 'lucide-react';
import { useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useGetDataModel } from 'services/Customer';
import { Server2 } from 'ui/UIAssets';
import {
  AxonButton,
  AxonSheet,
  AxonSheetContent,
  AxonSheetTrigger,
  AxonTableData,
  AxonTableInputSearch,
} from 'ui/UIComponents';
import { useShallow } from 'zustand/react/shallow';
import { useColumn } from './columns';
import FilterDropdown from './FilterDrodown';
import { useFilterState } from './filterState';

const WorkbenchDrawer = () => {
  const { t } = useTranslation();
  const lineId = useTabLineId();

  const { setActiveTab, addTab } = useTabStore(
    useShallow((state) => ({
      setActiveTab: state.setActiveTab,
      addTab: state.addTab,
    })),
  );

  const { searchText, expanded, showActiveClient, showInactiveClient, changeFilter } = useFilterState();
  const columns = useColumn({ lineId, addTab, setActiveTab });

  const { open } = useWorkbenchDrawer();
  const { openDrawer, closeDrawer } = useDrawerStore(
    useShallow((state) => ({
      openDrawer: state.openDrawer,
      closeDrawer: state.closeDrawer,
    })),
  );

  const { data: dataModel, isLoading } = useGetDataModel(lineId || '', { enabled: !!lineId && open });

  const handleExpandedChange = useCallback(
    (updaterOrValue: Updater<ExpandedState>) => {
      const expandedState = typeof updaterOrValue === 'function' ? updaterOrValue(expanded) : updaterOrValue;
      changeFilter({ expanded: expandedState });
    },
    [changeFilter, expanded],
  );

  const handleFilterChange = useCallback(
    (value: string) => {
      changeFilter({ searchText: String(value) });
    },
    [changeFilter],
  );

  const allData = useMemo(() => dataModel?.data?.result || [], [dataModel]);
  const visibleData = useMemo(() => {
    return allData.filter((client) => {
      const isStation = client.device.name !== 'WAN' && !client.device.deviceId;
      if (isStation) {
        // Apply filter on station only
        if (!showActiveClient && client.device.statuses.online.isAlive) {
          return false;
        }
        if (!showInactiveClient && !client.device.statuses.online.isAlive) {
          return false;
        }
      }
      return true;
    });
  }, [allData, showActiveClient, showInactiveClient]);
  const tableData = useMemo(() => visibleData.filter((device) => !device.parentDeviceName), [visibleData]);

  const table = useReactTable({
    data: tableData,
    columns,
    state: {
      globalFilter: searchText,
      expanded,
    },
    onExpandedChange: handleExpandedChange,
    getSubRows: (row) => visibleData.filter((device) => device.parentDeviceName === row.device.name),
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getExpandedRowModel: getExpandedRowModel(),
    getSortedRowModel: getSortedRowModel(),
    filterFromLeafRows: true,
    getColumnCanGlobalFilter: () => true,
    getRowId: (row) => row.device.deviceId ?? row.device.name,
  });

  return (
    <AxonSheet open={open} onOpenChange={(e) => (e ? openDrawer('workbench') : closeDrawer('workbench'))}>
      <AxonSheetTrigger asChild>
        <AxonButton
          variant='pure'
          aria-label={t('ariaLabel.openWorkbench')}
          data-testid={`sidebar-device-workbench-button`}
          className='flex items-center gap-1 px-0 py-2'>
          <Server2 className='text-component-hyperlink size-5' />
          <p className='text-component-hyperlink text-xs font-medium'>{t('workbench')}</p>
        </AxonButton>
      </AxonSheetTrigger>
      <AxonSheetContent className='max-w-[80%] p-0' onClick={(e) => e.stopPropagation()}>
        <div className='flex h-full flex-col'>
          <h1
            className='border-b-border-flat h-16 border-b px-6 py-5 text-xl font-medium'
            data-testid='workbench-drawer-title'>
            {t('workbench')}
            <span className='text-content-tertiary ml-2 font-normal' data-testid='workbench-drawer-device-count'>
              {Math.max(allData.length - 1, 0)} {t('devices')}
            </span>
          </h1>
          <div className='border-b-border-flat insight-search-block flex items-center justify-between border-b px-6 py-4'>
            <div className='relative h-fit'>
              <Search className='text-content-tertiary absolute left-3 top-1/2 size-4 -translate-y-1/2' />
              <AxonTableInputSearch
                value={searchText}
                onChange={handleFilterChange}
                placeholder={t('searchByDeviceInfo')}
                className='w-80'
                data-testid={`workbench-drawer-search-input`}
              />
            </div>
            <FilterDropdown
              filterView={{
                activeClient: showActiveClient,
                inactiveClient: showInactiveClient,
              }}
              onChangeFilter={(e) => {
                changeFilter({
                  showActiveClient: e.activeClient,
                  showInactiveClient: e.inactiveClient,
                });
              }}
              data-testid={`workbench-drawer-filter-dropdown`}
            />
          </div>
          <AxonTableData
            table={table}
            showFooter={false}
            isLoading={isLoading}
            classes={{
              wrapper: 'bg-surface-section text-content-primary/75',
            }}
            data-testid={`workbench-drawer-table`}
          />
        </div>
      </AxonSheetContent>
    </AxonSheet>
  );
};

export default WorkbenchDrawer;
