import get from 'lodash/get';
import { useDrawerStore, useWorkbenchDrawer } from '@/stores/drawer.store';
import { useEffect } from 'react';
import { useShallow } from 'zustand/react/shallow';
import { useGetTimeRangeByLine } from '@/stores/timeRange.store';
import { useTabLineId } from '@/stores/tab.store';

export function useFilterState() {
  const lineId = useTabLineId();
  const timeRangeSelected = useGetTimeRangeByLine(lineId || '');
  const defaultStartDate = get(timeRangeSelected, 'startDate', null);
  const defaultEndDate = get(timeRangeSelected, 'endDate', null);

  const updateDrawer = useDrawerStore(useShallow((state) => state.updateDrawerWorkbench));

  const { searchText, startDate, endDate, showActiveClient, showInactiveClient, expanded, open } = useWorkbenchDrawer();

  useEffect(() => {
    if (!open) {
      updateDrawer({
        searchText: '',
        startDate: defaultStartDate,
        endDate: defaultEndDate,
        showActiveClient: true,
        showInactiveClient: true,
        expanded: true,
      });
    }
  }, [open, defaultEndDate, defaultStartDate, updateDrawer]);

  const changeFilter = ({
    startDate,
    endDate,
    searchText,
    showActiveClient,
    showInactiveClient,
    expanded,
  }: {
    startDate?: Date | null;
    endDate?: Date | null;
    searchText?: string;
    showActiveClient?: boolean;
    showInactiveClient?: boolean;
    expanded?: true | Record<string, boolean>;
  }) => {
    updateDrawer({
      ...(startDate !== undefined ? { startDate } : {}),
      ...(endDate !== undefined ? { endDate } : {}),
      ...(searchText !== undefined ? { searchText } : {}),
      ...(showActiveClient !== undefined ? { showActiveClient } : {}),
      ...(showInactiveClient !== undefined ? { showInactiveClient } : {}),
      ...(expanded !== undefined ? { expanded } : {}),
    });
  };

  return {
    searchText,
    startDate,
    endDate,
    showActiveClient,
    showInactiveClient,
    expanded,
    changeFilter,
  };
}
