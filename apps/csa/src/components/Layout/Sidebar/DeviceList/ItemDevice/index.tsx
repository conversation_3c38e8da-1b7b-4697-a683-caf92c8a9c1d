import { cn } from '@/utils';
import { useTranslation } from 'react-i18next';
import { getDeviceImage, Layers, Signifier } from 'ui/UIAssets';
import {
  AxonButton,
  AxonImage,
  AxonSeparator,
  AxonTooltip,
  AxonTooltipContent,
  AxonTooltipProvider,
  AxonTooltipTrigger,
} from 'ui/UIComponents';
import WorkbenchDrawer from './WorkbenchDrawer';

type ItemDeviceProps = {
  selected: boolean;
  deviceId: string;
  type: string;
  status: 'online' | 'offline';
  onClick: () => void;
  onViewTopology?: () => void;
};

export default function ItemDevice({ selected, deviceId, type, status, onClick, onViewTopology }: ItemDeviceProps) {
  const { t } = useTranslation();

  return (
    <div
      data-testid={`sidebar-device-${deviceId}`}
      className={cn(
        'hover:bg-surface-section flex cursor-pointer flex-col gap-2.5 border-b p-4 transition-all',
        selected && 'bg-surface-section pt-4',
      )}
      onClick={onClick}>
      <div className='flex items-center gap-2'>
        <AxonImage src={getDeviceImage(type)} alt='access point' size={'xs'} label='axon' className='p-1' />
        <div className='min-w-0 flex-1'>
          <AxonTooltipProvider delayDuration={100}>
            <AxonTooltip>
              <AxonTooltipTrigger>
                <p
                  data-testid={`sidebar-device-${deviceId}-title`}
                  className='text-content-primary w-[130px] truncate text-left text-sm font-semibold'>
                  {deviceId}
                </p>
              </AxonTooltipTrigger>
              <AxonTooltipContent>
                <p>{deviceId}</p>
              </AxonTooltipContent>
            </AxonTooltip>
          </AxonTooltipProvider>
          <div className='flex items-center gap-1'>
            {status === 'online' ? (
              <Signifier className='text-content-meta-green' />
            ) : (
              <Signifier className='text-content-disabled' />
            )}
            <span
              data-testid={`sidebar-device-${deviceId}-status`}
              className='text-2xs text-content-tertiary capitalize'>
              {type ? `${type}, ` : ''} {t(status)}
            </span>
          </div>
        </div>
      </div>
      {selected && (
        <div className='flex flex-col pl-[50px]'>
          <AxonSeparator />
          <AxonButton
            variant='pure'
            aria-label={t('ariaLabel.openTopology')}
            data-testid={`sidebar-device-topology-button`}
            className='flex items-center gap-1 px-0 py-2'
            onClick={onViewTopology}>
            <Layers className='text-component-hyperlink size-5' />
            <p className='text-component-hyperlink text-nowrap text-xs font-medium'>
              {t('sidebar.deviceList.viewTopology')}
            </p>
          </AxonButton>
          <AxonSeparator />
          <WorkbenchDrawer />
        </div>
      )}
    </div>
  );
}
