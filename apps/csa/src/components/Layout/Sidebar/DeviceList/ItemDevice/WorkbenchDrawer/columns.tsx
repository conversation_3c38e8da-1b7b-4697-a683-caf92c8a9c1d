import { default as LogIcon } from '@/assets/code.svg';
import { default as EventIcon } from '@/assets/info.svg';
import { EHealthCheckStatus } from '@/components/HealthCheck/enum';
import { CPESections } from '@/constants/cpeElements';
import { useDrawerStore } from '@/stores/drawer.store';
import { LogDrawerTabs } from '@/stores/widgets.config';
import { cn } from '@/utils';
import { convertQOEStatus, EQoeStatus } from '@/utils/QOE.util';
import { ColumnDef } from '@tanstack/react-table';
import {
  BookOpen,
  ChevronDown,
  ChevronRight,
  Clock4,
  Ellipsis,
  GitMerge,
  Globe,
  Wifi,
  Zap,
  ZapOff,
} from 'lucide-react';
import { useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { getDayjsFromToday, getRelativeTimeFromNow } from 'services/Utils';
import { getDeviceImage, List, TrendingUp } from 'ui/UIAssets';
import {
  AxonButton,
  AxonDropdownMenu,
  AxonDropdownMenuContent,
  AxonDropdownMenuItem,
  AxonDropdownMenuTrigger,
  AxonImage,
  AxonSeparator,
  AxonTooltip,
  AxonTooltipContent,
  AxonTooltipPortal,
  AxonTooltipProvider,
  AxonTooltipTrigger,
  AxonTooltipWrapper,
} from 'ui/UIComponents';
import { useShallow } from 'zustand/react/shallow';
import { Client } from './type';
import { ALL_VALUE_OPTION } from '@/components/Insights/InsightsDrawer';

export const useColumn = ({ lineId, addTab, setActiveTab }) => {
  const { t } = useTranslation();

  const { openDrawer, closeDrawer, updateDrawerInsight, updateDrawerEvent } = useDrawerStore(
    useShallow((state) => ({
      openDrawer: state.openDrawer,
      closeDrawer: state.closeDrawer,
      updateDrawerInsight: state.updateDrawerInsight,
      updateDrawerEvent: state.updateDrawerEvent,
    })),
  );

  const goToCpePage = useCallback(
    (deviceId: string) => {
      if (!lineId) return;
      addTab({
        id: deviceId,
        name: deviceId,
        type: 'device',
        deviceId: deviceId,
        customerId: lineId,
      });
      closeDrawer('workbench');
      setActiveTab({
        tabId: deviceId,
        tabType: 'device',
      });
    },
    [lineId, addTab, setActiveTab, closeDrawer],
  );

  const handleOpenInsightDrawer = useCallback(
    (stationInfo: Client) => {
      let filterDeviceId = '';
      let stationMac = ALL_VALUE_OPTION;
      if (stationInfo.device.deviceType === 'Extender' || stationInfo.device.deviceType === 'Router') {
        // set filter insight by its id.
        filterDeviceId = stationInfo.device.deviceId ?? '';
      } else {
        stationMac = stationInfo.device.stationMac ?? ALL_VALUE_OPTION;
        filterDeviceId = stationInfo.parentDeviceId ?? '';
      }
      openDrawer('insight');
      updateDrawerInsight({
        selectedDeviceId: filterDeviceId,
        stationMac,
        startDate: getDayjsFromToday(6)?.toDate() || null,
        endDate: getDayjsFromToday(0)?.toDate() || null,
      });
    },
    [updateDrawerInsight, openDrawer],
  );

  const handleOpenLogDrawer = useCallback(
    (stationInfo: Client, viewMode: LogDrawerTabs = LogDrawerTabs.Logs) => {
      openDrawer('event');
      if (stationInfo.device.deviceType === 'Extender' || stationInfo.device.deviceType === 'Router') {
        updateDrawerEvent({
          cpeIdFilter: stationInfo.device.deviceId || '',
          sourceFilter: 'all',
          startDate: getDayjsFromToday(6)?.toDate() || null,
          endDate: getDayjsFromToday(0)?.toDate() || null,
          viewMode,
        });
      } else {
        updateDrawerEvent({
          cpeIdFilter: 'all',
          sourceFilter: stationInfo.device.stationMac || '',
          startDate: getDayjsFromToday(6)?.toDate() || null,
          endDate: getDayjsFromToday(0)?.toDate() || null,
          viewMode,
        });
      }
    },
    [openDrawer, updateDrawerEvent],
  );

  const handleGoToRssiHistory = useCallback(
    (deviceId: string) => {
      if (!lineId) return;
      addTab({
        id: deviceId,
        name: deviceId,
        type: 'device',
        deviceId: deviceId,
        customerId: lineId,
        config: {
          scrollTo: {
            section: CPESections.CLIENTS,
            widget: 'clientHistory',
          },
        },
      });
      closeDrawer('workbench');
      setActiveTab({
        tabId: deviceId,
        tabType: 'device',
      });
    },
    [lineId, addTab, setActiveTab, closeDrawer],
  );

  const handleGoToEventTimeline = useCallback(
    (stationInfo: Client) => {
      if (!lineId) return;
      handleOpenLogDrawer(stationInfo, LogDrawerTabs.LogChart);
    },
    [lineId, handleOpenLogDrawer],
  );

  const columns = useMemo<ColumnDef<Client, any>[]>(
    () => [
      {
        accessorFn: (row) => row.device.name,
        header: t('deviceId'),
        cell: ({ row }) => {
          const { deviceType, name, statuses } = row.original.device;
          const isRoot = name === 'WAN';
          const canExpand = row.getCanExpand();

          const qoeStatus = convertQOEStatus(statuses.qoe.status);
          const lastTestedQOE = statuses.qoe.lastTested;

          return (
            <div
              style={{
                paddingLeft: `${(row.depth - 1) * 3}rem`,
              }}>
              <div className={cn('flex items-center gap-x-2')}>
                {canExpand && !isRoot ? (
                  <AxonButton
                    aria-label={row.getIsExpanded() ? t('ariaLabel.collapse') : t('ariaLabel.expand')}
                    onClick={row.getToggleExpandedHandler()}
                    variant='ghost'
                    size='default'
                    data-testid={`workbench-drawer-expand-button-${row.id}`}>
                    {row.getIsExpanded() ? <ChevronDown size={16} /> : <ChevronRight size={16} />}
                  </AxonButton>
                ) : null}
                {!canExpand && !isRoot && <div className='w-10'></div>}
                <AxonImage
                  src={getDeviceImage(deviceType || '')}
                  size={'xs'}
                  className='p-1'
                  label={deviceType && ['Router', 'Extender'].includes(deviceType) ? 'axon' : undefined}
                  alt={t('ariaLabel.dataModelImageAlt')}
                  data-testid={`workbench-drawer-device-image-${row.id}`}
                />
                <div className='flex flex-col'>
                  <p
                    className='text-content-primary font-medium'
                    data-testid={`workbench-drawer-device-name-${row.id}`}>
                    {name}
                  </p>

                  <span className='flex items-center gap-1 text-xs'>
                    <div
                      className={cn('bg-content-disabled size-2 rounded-full', {
                        'bg-content-meta-green': qoeStatus === EQoeStatus.STABLE,
                        'bg-content-meta-yellow': qoeStatus === EQoeStatus.UNSTABLE,
                        'bg-content-meta-red': qoeStatus === EQoeStatus.VERY_UNSTABLE,
                        'bg-content-tertiary': !statuses.online.isAlive,
                      })}
                      data-testid={`workbench-drawer-qoe-status-${row.id}`}
                    />
                    <span
                      className={cn(`text-content-disabled font-medium`, {
                        'text-content-meta-green': qoeStatus === EQoeStatus.STABLE,
                        'text-content-meta-yellow': qoeStatus === EQoeStatus.UNSTABLE,
                        'text-content-meta-red': qoeStatus === EQoeStatus.VERY_UNSTABLE,
                        'text-content-tertiary': !statuses.online.isAlive,
                      })}
                      data-testid={`workbench-drawer-qoe-status-text-${row.id}`}>
                      {t(qoeStatus)}
                    </span>
                    {lastTestedQOE && (
                      <span
                        className='font-book text-content-primary opacity-60'
                        data-testid={`workbench-drawer-qoe-last-tested-${row.id}`}>
                        {`${t('since')} ${getRelativeTimeFromNow(lastTestedQOE)}`}
                      </span>
                    )}
                  </span>

                  <div
                    className='text-content-tertiary flex flex-row items-center gap-1 text-xs'
                    data-testid={`workbench-drawer-online-status-${row.id}`}>
                    {statuses.online.isAlive ? (
                      <>
                        <Zap size={12} className='text-content-tertiary' />
                        <p>{t('online')}</p>
                      </>
                    ) : (
                      <>
                        <ZapOff size={12} className='text-content-tertiary' />
                        <p>{t('offline')}</p>
                      </>
                    )}
                    {statuses.online.lastTested && (
                      <p>{`${t('since')} ${getRelativeTimeFromNow(statuses.online.lastTested)}`}</p>
                    )}
                  </div>
                </div>
              </div>
            </div>
          );
        },
      },
      {
        accessorFn: (row) => row.deviceInfo.modelName,
        header: t('deviceInfo'),
        cell: ({ row }) => {
          const { modelName, osVersion } = row.original.deviceInfo;
          return (
            <div className='flex flex-col gap-1'>
              <p className='text-md text-content-primary' data-testid={`workbench-drawer-device-model-${row.id}`}>
                {modelName}
              </p>
              <p className='text-content-tertiary text-xs' data-testid={`workbench-drawer-device-os-${row.id}`}>
                {osVersion}
              </p>
            </div>
          );
        },
      },
      {
        id: 'connection',
        header: t('connection'),
        cell: ({ row }) => {
          const {
            connectedInterface,
            connectedBandName,
            connectedMode,
            connectionType,
            coverageDetectionStatus,
            rssi,
          } = row.original.connection;
          const parentDeviceName = row.original.parentDeviceName;
          const isRootAP = connectedInterface === 'WAN';
          const isEthernet = connectionType === 'ethernet';
          const isWifi = connectionType === 'wifi';
          const color =
            coverageDetectionStatus === EHealthCheckStatus.VERY_UNSTABLE
              ? 'rgb(var(--error-500))'
              : coverageDetectionStatus === EHealthCheckStatus.UNSTABLE
                ? 'rgb(var(--warning-500))'
                : coverageDetectionStatus === EHealthCheckStatus.STABLE
                  ? 'rgb(var(--success-500))'
                  : 'rgb(var(--content-tertiary))';
          return (
            <div className='flex flex-col gap-1'>
              <div className='flex flex-row items-center gap-2' data-testid={`workbench-drawer-connection-${row.id}`}>
                {isEthernet && <GitMerge size={16} />}
                {isWifi && <Wifi size={16} color={color} />}
                {isRootAP && <Globe size={16} />}
                {parentDeviceName && parentDeviceName !== 'WAN' && !isEthernet ? (
                  <span
                    className='text-md text-content-primary flex items-center gap-1 font-medium'
                    data-testid={`workbench-drawer-connection-text-${row.id}`}>
                    <AxonTooltipWrapper
                      label={
                        <p className='max-w-40 overflow-hidden text-ellipsis text-nowrap'>
                          {`${rssi ? `${rssi} dBm,` : ''} ${connectedInterface ? connectedInterface : t('unknown')}`}
                          <span className='text-sm tracking-wider'>{connectedMode ? ` (${connectedMode})` : ''}</span>
                        </p>
                      }
                      content={
                        <p>
                          {connectedInterface}
                          <span className='text-sm tracking-wider'>{connectedMode ? ` (${connectedMode})` : ''}</span>
                        </p>
                      }
                    />
                  </span>
                ) : (
                  <p
                    className='text-md text-content-primary font-medium'
                    data-testid={`workbench-drawer-connection-text-${row.id}`}>
                    {connectedInterface}
                  </p>
                )}
              </div>
              <p
                className='text-content-tertiary pl-6 text-xs'
                data-testid={`workbench-drawer-connection-band-${row.id}`}>
                {isEthernet && !connectedBandName ? t('notDetermined') : connectedBandName}
              </p>
            </div>
          );
        },
      },
      {
        id: 'ipMac',
        accessorFn: (row) => `${row.ipMac.ipAddress ?? ''} ${row.ipMac.stationMac ?? ''}`,
        header: t('IP/MAC'),
        cell: ({ row }) => {
          const { ipAddress, stationMac } = row.original.ipMac;
          return (
            <div className='flex flex-col gap-1'>
              <p className='text-md text-content-primary' data-testid={`workbench-drawer-ip-${row.id}`}>
                {ipAddress}
              </p>
              <p className='text-content-tertiary text-xs' data-testid={`workbench-drawer-mac-${row.id}`}>
                {stationMac}
              </p>
            </div>
          );
        },
      },
      {
        id: 'speed',
        header: t('speed'),
        cell: ({ row }) => {
          const { upload, download, unit, latency, lastTested } = row.original.speed;

          return (
            <div className='flex flex-col gap-1'>
              <div
                className='text-md text-content-primary flex flex-row gap-1 whitespace-nowrap'
                data-testid={`workbench-drawer-speed-${row.id}`}>
                <div data-testid={`workbench-drawer-speed-upload-${row.id}`}>{upload}</div>
                <div data-testid={`workbench-drawer-speed-download-${row.id}`}>
                  {upload && download ? `/ ${download}` : download}
                </div>
                {(upload || download) && <div data-testid={`workbench-drawer-speed-unit-${row.id}`}>{unit}</div>}
                {latency ? <div data-testid={`workbench-drawer-speed-latency-${row.id}`}>{`(${latency})`}</div> : null}
              </div>
              {lastTested ? (
                <p
                  className='text-content-tertiary text-xs'
                  data-testid={`workbench-drawer-last-tested-${row.id}`}>{`${t('since')} ${getRelativeTimeFromNow(1750244948000)}`}</p>
              ) : null}
            </div>
          );
        },
      },
      {
        id: 'counts',
        header: () => (
          <AxonTooltipProvider delayDuration={100}>
            <AxonTooltip>
              <AxonTooltipTrigger data-testid={`workbench-drawer-seven-days-history`}>
                {t('sevenDaysHistory')}
              </AxonTooltipTrigger>
              {/*// https://github.com/shadcn-ui/ui/issues/129*/}
              <AxonTooltipPortal>
                <AxonTooltipContent side='top'>{t('diagnoseLastWeek')}</AxonTooltipContent>
              </AxonTooltipPortal>
            </AxonTooltip>
          </AxonTooltipProvider>
        ),
        cell: ({ row }) => {
          const { insights, logs } = row.original.counts;
          return (
            <div className='flex flex-row gap-2' data-testid={`workbench-drawer-counts-${row.id}`}>
              {!insights && !logs ? (
                <p className='text-md text-content-primary' data-testid={`workbench-drawer-no-insight-${row.id}`}>
                  {t('insights.noInsight')}
                </p>
              ) : (
                <div className='flex flex-row gap-3'>
                  <AxonButton
                    data-testid={`sidebar-device-insights-button-${row.id}`}
                    variant='ghost'
                    size='icon'
                    aria-label={t('ariaLabel.openInsights')}
                    // className='flex items-center gap-1'
                    onClick={() => handleOpenInsightDrawer(row.original)}>
                    <EventIcon className='shrink-0' />
                    {insights ?? 0}
                  </AxonButton>
                  <AxonButton
                    data-testid={`sidebar-device-events-button-${row.id}`}
                    variant='ghost'
                    size='icon'
                    onClick={() => handleOpenLogDrawer(row.original)}>
                    <LogIcon className='shrink-0' />
                    {logs ?? 0}
                  </AxonButton>
                </div>
              )}
            </div>
          );
        },
      },
      {
        id: 'more',
        header: '',
        cell: ({ row }) =>
          row.original.device.deviceType && ['Router', 'Extender'].includes(row.original.device.deviceType) ? (
            <AxonDropdownMenu modal={false}>
              <AxonDropdownMenuTrigger asChild>
                <AxonButton
                  variant='outline'
                  size='icon'
                  aria-label={t('ariaLabel.moreActions')}
                  data-testid={`workbench-drawer-more-button-${row.id}`}>
                  <Ellipsis size={16} />
                </AxonButton>
              </AxonDropdownMenuTrigger>
              <AxonDropdownMenuContent data-testid={`workbench-drawer-more-menu-${row.id}`}>
                <AxonDropdownMenuItem
                  className='cursor-pointer'
                  onClick={() => goToCpePage(row.original.device.name)}
                  data-testid={`workbench-drawer-go-to-device-page-${row.id}`}>
                  <List className='size-4' />
                  {t('devicesDrawer.go_to_device_page')}
                </AxonDropdownMenuItem>
                <AxonDropdownMenuItem
                  className='pointer-events-none'
                  data-testid={`workbench-drawer-diagnose-device-${row.id}`}>
                  <AxonSeparator />
                </AxonDropdownMenuItem>
                <AxonDropdownMenuItem
                  className='cursor-pointer'
                  onClick={() => handleGoToRssiHistory(row.original.device.name)}
                  data-testid={`workbench-drawer-go-to-rssi-history-${row.id}`}>
                  <TrendingUp className='size-4' />
                  {t('devicesDrawer.go_to_rssi_history')}
                </AxonDropdownMenuItem>
                <AxonDropdownMenuItem
                  className='cursor-pointer'
                  onClick={() => handleGoToEventTimeline(row.original)}
                  data-testid={`workbench-drawer-go-to-event-timeline-${row.id}`}>
                  <Clock4 size={16} />
                  {t('devicesDrawer.go_to_event_timeline')}
                </AxonDropdownMenuItem>
                <AxonDropdownMenuItem
                  className='cursor-pointer'
                  onClick={() => handleOpenInsightDrawer(row.original)}
                  data-testid={`workbench-drawer-open-insights-${row.id}`}>
                  <BookOpen size={16} />
                  {t('devicesDrawer.open_insights_panel')}
                </AxonDropdownMenuItem>
              </AxonDropdownMenuContent>
            </AxonDropdownMenu>
          ) : null,
      },
    ],
    [goToCpePage, t, handleGoToEventTimeline, handleGoToRssiHistory, handleOpenInsightDrawer, handleOpenLogDrawer],
  );

  return columns;
};
