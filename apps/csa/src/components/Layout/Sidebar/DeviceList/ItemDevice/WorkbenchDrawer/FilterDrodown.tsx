import { useMemo, useState } from 'react';
import {
  AxonButton,
  AxonDropdownMenu,
  AxonDropdownMenuContent,
  AxonDropdownMenuGroup,
  AxonDropdownMenuItem,
  AxonDropdownMenuLabel,
  AxonDropdownMenuTrigger,
  AxonCheckbox,
  AxonFilterButton,
} from 'ui/UIComponents';
import { useTranslation } from 'react-i18next';
import { WorkbenchFilterView } from './type';

export default function FilterDropdown({
  filterView,
  onChangeFilter,
}: {
  filterView: WorkbenchFilterView;
  onChangeFilter: (filterView: WorkbenchFilterView) => void;
}) {
  const { t } = useTranslation();

  const [open, setOpen] = useState(false);

  const totalFilter = useMemo(() => {
    return Object.values(filterView).reduce((acc, value) => {
      return value ? acc + 1 : acc;
    }, 0);
  }, [filterView]);

  const onClearFilterView = () => {
    onChangeFilter({
      activeClient: true,
      inactiveClient: true,
    });
  };

  const handleOpenChange = (isOpen: boolean) => {
    setOpen(isOpen);
  };

  const toggleStatus = (value: string) => {
    onChangeFilter({
      ...filterView,
      activeClient: value === 'active' ? !filterView.activeClient : filterView.activeClient,
      inactiveClient: value === 'inactive' ? !filterView.inactiveClient : filterView.inactiveClient,
    });
  };

  return (
    <AxonDropdownMenu open={open} onOpenChange={handleOpenChange}>
      <AxonDropdownMenuTrigger asChild>
        <AxonFilterButton
          data-testid='topology-graphical-filter-button'
          text={t('networkTopology.view')}
          badgeText={totalFilter.toString()}
        />
      </AxonDropdownMenuTrigger>
      <AxonDropdownMenuContent
        className='bg-surface-popover w-64 space-y-4 p-4'
        onInteractOutside={() => setOpen(false)}
        onEscapeKeyDown={(e) => {
          e.stopPropagation();
          setOpen(false);
        }}
        tabIndex={1}
        loop={true}>
        <AxonDropdownMenuGroup>
          <AxonDropdownMenuLabel
            data-testid='topology-graphical-filter-group-clientStatus'
            className='text-content-secondary pb-0 font-normal'>
            {t('networkTopology.clientStatus')}
          </AxonDropdownMenuLabel>
          <AxonDropdownMenuItem
            className='flex items-center justify-between gap-2'
            tabIndex={0}
            role='menuitem'
            onKeyDown={(e) => {
              if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                toggleStatus('active');
              }
            }}
            onSelect={(e) => {
              e.preventDefault();
              toggleStatus('active');
            }}>
            <span data-testid={`topology-graphical-filter-activeClient-label`}>{t('networkTopology.active')}</span>
            <AxonCheckbox
              data-testid={`topology-graphical-filter-activeClient-checkbox`}
              aria-label={t('networkTopology.active')}
              checked={filterView.activeClient}
              onCheckedChange={(checked) => {
                onChangeFilter({
                  ...filterView,
                  activeClient: !!checked,
                });
              }}
            />
          </AxonDropdownMenuItem>
          <AxonDropdownMenuItem
            className='flex items-center justify-between gap-2'
            tabIndex={0}
            role='menuitem'
            onKeyDown={(e) => {
              if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                toggleStatus('inactive');
              }
            }}
            onSelect={(e) => {
              e.preventDefault();
              toggleStatus('inactive');
            }}>
            <span data-testid={`topology-graphical-filter-inactiveClient-label`}>{t('networkTopology.inactive')}</span>
            <AxonCheckbox
              data-testid={`topology-graphical-filter-inactiveClient-checkbox`}
              aria-label={t('networkTopology.inactive')}
              checked={filterView.inactiveClient}
              onCheckedChange={(checked) => {
                onChangeFilter({
                  ...filterView,
                  inactiveClient: !!checked,
                });
              }}
            />
          </AxonDropdownMenuItem>
        </AxonDropdownMenuGroup>
        <AxonDropdownMenuItem
          key='clear-button-item'
          className='p-0'
          tabIndex={0}
          role='menuitem'
          onSelect={(e) => {
            // Prevent closing the dropdown when selecting this item
            e.preventDefault();
            // Call the clear function directly
            onClearFilterView();
          }}>
          <AxonButton
            data-testid='topology-graphical-filter-clear-button'
            role='group'
            aria-label={t('networkTopology.clearfilterBtn')}
            variant='default'
            className='w-full'
            onClick={(e) => {
              // Stop propagation to prevent double-triggering
              e.stopPropagation();
              onClearFilterView();
            }}>
            {t('networkTopology.clearfilterBtn')}
          </AxonButton>
        </AxonDropdownMenuItem>
      </AxonDropdownMenuContent>
    </AxonDropdownMenu>
  );
}
