import get from 'lodash/get';
import { useDrawerStore, useWanStatsDrawer } from '@/stores/drawer.store';
import { useEffect } from 'react';
import { useShallow } from 'zustand/react/shallow';
import { useGetTimeRangeByLine } from '@/stores/timeRange.store';
import { useTabLineId } from '@/stores/tab.store';
import { useGetSelectedRequestInfo } from '@/stores/realtime.store';
import { dayjs } from 'services/Utils';

export function useFilterState() {
  const lineId = useTabLineId() || '';
  const timeRangeSelected = useGetTimeRangeByLine(lineId);
  const defaultStartDate = get(timeRangeSelected, 'startDate', null);
  const defaultEndDate = get(timeRangeSelected, 'endDate', null);
  const selectedRequest = useGetSelectedRequestInfo(lineId, 'WAN_STATISTIC');
  const realtimeRequestId = get(selectedRequest, 'realtimeRequestId') || '';

  const updateDrawer = useDrawerStore(useShallow((state) => state.updateDrawerWanStats));

  const { searchText, startDate, endDate, open } = useWanStatsDrawer();

  useEffect(() => {
    if (!open) {
      const defaultStart = realtimeRequestId ? dayjs().toDate() : defaultStartDate;
      const defaultEnd = realtimeRequestId ? dayjs().toDate() : defaultEndDate;
      updateDrawer({
        searchText: '',
        startDate: defaultStart,
        endDate: defaultEnd,
      });
    }
  }, [open, defaultEndDate, defaultStartDate, updateDrawer, realtimeRequestId]);

  const changeFilter = ({
    startDate,
    endDate,
    searchText,
  }: {
    startDate?: Date | null;
    endDate?: Date | null;
    searchText?: string;
  }) => {
    updateDrawer({
      ...(startDate !== undefined ? { startDate } : {}),
      ...(endDate !== undefined ? { endDate } : {}),
      ...(searchText !== undefined ? { searchText } : {}),
    });
  };

  return {
    searchText,
    startDate,
    endDate,
    changeFilter,
  };
}
