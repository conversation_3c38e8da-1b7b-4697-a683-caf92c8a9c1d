import { StatusTrend } from '@/components/StatusTrend';
import { ColumnDef } from '@tanstack/react-table';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { IWanDetail } from './type.remote';

export const useColumn = () => {
  const { t } = useTranslation();

  const columns = useMemo<ColumnDef<IWanDetail, any>[]>(() => {
    return [
      {
        header: () => <span className=''>{t('device:wanStatistics.table.header.type')}</span>,
        accessorKey: 'type',
        cell: ({ row }) => {
          const { wanType } = row.original;
          return <span className='text-md text-content-primary font-medium'>{wanType}</span>;
        },
      },
      {
        header: t('device:wanStatistics.table.header.minMax'),
        accessorKey: `minMax`,
        cell: ({ row }) => {
          const { wanMinValue, wanMaxValue, wanUnit } = row.original;
          return (
            <span className='text-content-primary text-md font-normal'>
              {wanUnit
                ? `${wanMinValue !== null ? wanMinValue : ''} - ${wanMaxValue !== null ? wanMaxValue : ''} ${wanUnit}`
                : ''}
            </span>
          );
        },
      },
      {
        header: t('device:wanStatistics.table.header.average'),
        accessorKey: 'averageValue',
        cell: ({ row }) => {
          const { wanAverage, wanUnit } = row.original;
          return (
            <span className='text-md text-content-primary font-normal'>
              {wanAverage !== null ? wanAverage : '-'} {wanUnit}
            </span>
          );
        },
      },
      {
        header: t('device:wanStatistics.table.header.latestResult'),
        accessorKey: 'value',
        cell: ({ row }) => {
          const { wanValue, wanUnit, wanId } = row.original;
          const isWarning = wanId.includes('speed') || wanId.includes('latency');

          return (
            <div className='flex flex-col items-start'>
              <span className='text-content-primary'>
                {wanValue !== null ? wanValue : '-'} {wanUnit}
              </span>
              {isWarning && (
                <span className='flex items-center gap-1'>
                  {/* <BoldArrowUpSquare color='rgb(var(--content-meta-green))' />{' '}
                   <span className='text-content-meta-green text-xs font-medium'>xx%</span>{' '}
                   <span className='font-book text-xs opacity-60'>vs. SLA (xxMbps)</span> */}
                </span>
              )}
            </div>
          );
        },
      },
      {
        header: t('device:wanStatistics.table.header.noOfTests'),
        accessorKey: 'noOfTest',
        cell: ({ row }) => {
          const { noOfTest } = row.original;
          return (
            <span className='text-md text-content-primary pr-5 font-normal'>{noOfTest !== null ? noOfTest : ''}</span>
          );
        },
      },
      {
        header: t('device:deviceInfo.cpeStats.table.header.weeklyTrend'),
        accessorKey: 'weeklyTrends',
        cell: ({
          row: {
            original: { weeklyTrends },
          },
        }) => (weeklyTrends ? <StatusTrend data={weeklyTrends} /> : null),
      },
    ];
  }, [t]);

  return columns;
};
