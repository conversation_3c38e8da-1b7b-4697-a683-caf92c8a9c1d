import { useDrawerStore, useWanStatsDrawer } from '@/stores/drawer.store';
import { useTabDeviceId, useTabLineId } from '@/stores/tab.store';
import { cn } from '@/utils';
import { getCoreRowModel, getSortedRowModel, useReactTable } from '@tanstack/react-table';
import { Search } from 'lucide-react';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useGetWanStatisticsDrawer } from 'services/CPEService';
import { dayjs, getDayjsFromDate, getTimezone, getUnixTime } from 'services/Utils';
import { AxonDateRangePicker, AxonInput, AxonSheet, AxonSheetContent, AxonTableData } from 'ui/UIComponents';
import { useShallow } from 'zustand/react/shallow';
import { useColumn } from './columns';
import { useFilterState } from './filterState';
import { IWanDetail } from './type.remote';
import { useGetSelectedRequestInfo } from '@/stores/realtime.store';
import get from 'lodash/get';
import { useGetRealtimeWanStatsDrawer } from 'services/Realtime';

export interface IWanStatistics {
  wanId: string;
  wanType: string;
  wanDown: string | number | null;
  wanDownIssue: string | null;
  wanUp: string | number | null;
  wanUpIssue: string | null;
  wanDescription: string | null;
  wanUnit: string;
}

type DateRange = {
  from: Date | undefined;
  to?: Date;
};

export default function WanStatsDrawer() {
  const { t } = useTranslation();

  const deviceId = useTabDeviceId() || '';
  const lineId = useTabLineId() || '';
  const selectedRequest = useGetSelectedRequestInfo(lineId, 'WAN_STATISTIC');
  const realtimeRequestId = get(selectedRequest, 'realtimeRequestId') || '';

  const { searchText, startDate, endDate, changeFilter } = useFilterState();
  const { open: isOpenDrawer } = useWanStatsDrawer();
  const closeDrawer = useDrawerStore(useShallow((state) => state.closeDrawer));

  const isUseRealtime = useMemo(() => {
    const isEndDateToday = dayjs(endDate).isSame(dayjs(), 'day');

    if (isEndDateToday && realtimeRequestId) return true; // Case realtime
    if (isEndDateToday && !realtimeRequestId) return false; // Case on-the-fly

    return false;
  }, [endDate, realtimeRequestId]);

  const {
    data: queryWanStatisticDrawerData,
    isLoading,
    isError,
  } = useGetWanStatisticsDrawer(
    {
      deviceId,
      startDate: getUnixTime(startDate) || 0,
      endDate: getUnixTime(endDate) || undefined,
    },
    { enabled: !isUseRealtime && isOpenDrawer && !!deviceId && !!startDate },
  );

  const { data: realtimeWanStatsDrawerData } = useGetRealtimeWanStatsDrawer(
    {
      realtimeRequestId,
      deviceId,
      startDate: getUnixTime(startDate) || 0,
      endDate: getUnixTime(endDate) || undefined,
    },
    {
      enabled: isUseRealtime && isOpenDrawer && !!deviceId && !!startDate && !!realtimeRequestId,
    },
  );

  const wanStatisticDrawerData = isUseRealtime ? realtimeWanStatsDrawerData?.data : queryWanStatisticDrawerData?.data;
  const { results: detail } = wanStatisticDrawerData ?? {};

  const handleSetCustomTimeRange = (value?: DateRange) => {
    const startDate = getDayjsFromDate(value?.from).startOf('day').tz(getTimezone(), true).toDate();
    const endDate = getDayjsFromDate(value?.to).startOf('day').tz(getTimezone(), true).toDate();

    changeFilter({
      startDate,
      endDate,
    });
  };

  const columns = useColumn();

  const rows = useMemo<IWanDetail[]>(() => {
    if (!searchText || !detail) return detail || [];
    return detail.filter((item) => {
      if (item.wanId.toLowerCase().includes(searchText.toLowerCase())) return true;
      if (item.wanType.toLowerCase().includes(searchText.toLowerCase())) return true;
      if ((item.wanUnit ?? '').toLowerCase().includes(searchText.toLowerCase())) return true;

      return false;
    });
  }, [detail, searchText]);

  const table = useReactTable({
    columns,
    data: rows,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getRowId: (row) => row.wanId,
  });

  return (
    <AxonSheet open={isOpenDrawer} onOpenChange={(open) => !open && closeDrawer('wanStatistics')}>
      <AxonSheetContent
        className={cn('lg w-full overflow-y-auto p-0 sm:max-w-5xl md:max-w-screen-lg 2xl:max-w-screen-xl')}
        data-testid='device-wan-statistics-drawer'>
        <div className='flex h-screen flex-col'>
          <div className={cn('h-16 border-b px-6 py-5 text-xl font-medium')}>{t('wanStatistics')}</div>
          <div className={cn('border-b-border-flat flex items-center justify-between border-b p-4')}>
            <div className='relative w-96'>
              <Search size={16} className='text-muted-foreground absolute left-3 top-3' />
              <AxonInput
                placeholder={t('device:wanStatistics.searchInputPlaceholder')}
                className='pl-10'
                value={searchText}
                onChange={(e) => changeFilter({ searchText: e.target.value })}
                data-testid='device-wan-statistics-search-input'
              />
            </div>
            <div>
              <AxonDateRangePicker
                selected={{
                  from: startDate ?? undefined,
                  to: endDate ?? undefined,
                }}
                onApply={handleSetCustomTimeRange}
                showIcon={false}
                data-testid='device-wan-statistics-date-range-picker'
              />
            </div>
          </div>

          <div className={cn('bg-surface-section scrollbar-lg h-full overflow-auto')}>
            <AxonTableData
              table={table}
              showFooter={false}
              isLoading={isLoading}
              isError={isError}
              data-testid='device-wan-statistics-table'
            />
          </div>
        </div>
      </AxonSheetContent>
    </AxonSheet>
  );
}
