import { Node } from '@xyflow/react';
import { createContext, useContext, useState } from 'react';

export const VisibleNodesContext = createContext({
  nodes: [] as Node[], // Default value
  setNodes: (_nodes: Node[]) => {}, // Default empty function
});

export const VisibleNodesContextProvider = ({ children }: { children: React.ReactNode }) => {
  const [nodes, setNodes] = useState<Node[]>([]);

  return <VisibleNodesContext.Provider value={{ nodes, setNodes }}>{children}</VisibleNodesContext.Provider>;
};

export const useVisibleNodesContext = () => {
  return useContext(VisibleNodesContext);
};
