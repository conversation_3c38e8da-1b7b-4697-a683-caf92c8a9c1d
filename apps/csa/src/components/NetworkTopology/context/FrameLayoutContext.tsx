import { createContext, useContext, useState } from 'react';

export const FrameLayoutContext = createContext({
  isExtended: true,
  setIsExtended: (_isExtended: boolean) => {},
});

export const FrameLayoutContextProvider = ({ children }: { children: React.ReactNode }) => {
  const [isExtended, setIsExtended] = useState<boolean>(true);

  return <FrameLayoutContext.Provider value={{ isExtended, setIsExtended }}>{children}</FrameLayoutContext.Provider>;
};

export const useFrameLayoutContext = () => {
  return useContext(FrameLayoutContext);
};
