import { type FC } from 'react';
import { EdgeLabelRenderer, BaseEdge, type Edge, type EdgeProps, getSmoothStepPath } from '@xyflow/react';
import { getColorByStatus } from './utils';

// this is a little helper component to render the actual edge label
function EdgeLabel({ transform, label, color }: { transform?: string; label?: string; color?: string }) {
  return (
    <div
      style={{
        position: 'absolute',
        padding: '2px',
        fontSize: 24,
        fontWeight: 500,
        backgroundColor: 'rgb(var(--surface-body))',
        color,
        transform,
      }}
      className='nodrag nopan'>
      {label}
    </div>
  );
}

const CustomEdge: FC<
  EdgeProps<
    Edge<{
      rssi: number | null;
      status: 'stable' | 'unstable' | 'veryUnstable' | 'unknown';
      connectionType: string | null;
      band: string | null;
      connectionInterface: string | null;
    }>
  >
> = ({ id, sourceX, sourceY, targetX, targetY, sourcePosition, targetPosition, data }) => {
  const [edgePath] = getSmoothStepPath({
    sourceX,
    sourceY,
    sourcePosition,
    targetX,
    targetY,
    targetPosition,
    borderRadius: 200,
  });

  const color = getColorByStatus(data ? data.status : 'unknown');

  const isWireless = data?.connectionType === 'wifi';

  return (
    <>
      <BaseEdge
        id={id}
        path={edgePath}
        markerStart={`url(#marker-${data ? data.status : 'unknown'})`}
        markerEnd={`url(#marker-${data ? data.status : 'unknown'})`}
        style={{
          strokeWidth: 6,
          stroke: color,
          strokeDasharray: isWireless ? '16 16' : undefined,
        }}
      />
      <EdgeLabelRenderer>
        {isWireless && (
          <EdgeLabel
            color={color}
            transform={`translate(-50%, -400%) translate(${targetX}px,${targetY}px)`}
            label={data?.connectionInterface || ''}
          />
        )}
      </EdgeLabelRenderer>
    </>
  );
};

export default CustomEdge;
