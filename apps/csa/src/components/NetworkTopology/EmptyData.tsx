import { useTranslation } from 'react-i18next';
import { ZapOff } from 'ui/UIAssets';
import { AxonButton } from 'ui/UIComponents';

export default function EmptyData({ onReload }: { onReload: () => void }) {
  const { t } = useTranslation();

  return (
    <div
      className='bg-surface-section/80 flex size-full flex-col items-center justify-center gap-4'
      data-testid='topology-graphical-empty-view'>
      <ZapOff className='text-warning-500 size-10' />
      <span
        className='text-content-primary font-global text-lg font-medium leading-[120%]'
        data-testid='topology-graphical-empty-view-title'>
        {t('noData')}
      </span>
      <AxonButton onClick={onReload} data-testid='topology-graphical-empty-view-refresh-button'>
        {t('refresh')}
      </AxonButton>
    </div>
  );
}
