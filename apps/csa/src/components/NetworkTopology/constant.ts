import { FilterView, ITopologyGraph } from './types';

export const DEFAULT_FILTER_VIEW: FilterView = {
  connectionTypes: { ethernet: true, wifi_2_4: true, wifi_5: true },
  deviceTypes: { cpe: true, client: true },
  statuses: { active: true, inactive: false },
};

export const CLEAR_FILTER_VIEW: FilterView = {
  connectionTypes: { ethernet: true, wifi_2_4: true, wifi_5: true },
  deviceTypes: { cpe: true, client: true },
  statuses: { active: true, inactive: true },
};

export const DEFAULT_CONFIG = {
  open: false,
  filterView: DEFAULT_FILTER_VIEW,
  selectedNode: null,
  nodeToZoomMap: null,
  estimatedCapacityOpen: false,
  channelGainsOpen: false,
};

export const EMPTY_TOPOLOGY: ITopologyGraph = {
  topology: [],
  nodeAttributes: [],
  staTopology: [],
  staNodeAttributes: [],
  score: {
    average: 0,
    minimum: 0,
    sum: 0,
  },
};
