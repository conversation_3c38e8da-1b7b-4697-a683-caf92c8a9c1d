import { StatusTrend } from '@/components/StatusTrend';
import { cn } from '@/utils';
import { ColumnDef } from '@tanstack/react-table';
import { Minus } from 'lucide-react';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { BoldSquareArrowDown, BoldSquareArrowUp } from 'ui/UIAssets';
import { ServiceStatistics } from '../type';

export const useColumn = () => {
  const { t } = useTranslation();

  const statusChanged = (changedInPercentage: number): string => {
    if (changedInPercentage > 0) return 'good';
    if (changedInPercentage < 0) return 'bad';
    return 'none';
  };

  const columns = useMemo<ColumnDef<ServiceStatistics, any>[]>(() => {
    return [
      {
        header: () => (
          <span className='pl-5'>{t('customer:healthCheck.wheel.services.drawer.serviceStats.table.header.type')}</span>
        ),
        accessorKey: 'type',
        cell: ({ row }) => {
          const { serviceType } = row.original;
          return <span className='text-md text-content-primary pl-5 font-medium'>{serviceType}</span>;
        },
      },
      {
        header: t('customer:healthCheck.wheel.services.drawer.serviceStats.table.header.minMax'),
        accessorKey: `minMax`,
        cell: ({ row }) => {
          const { serviceMinValue, serviceMaxValue, serviceUnit } = row.original;

          const showValue = serviceMinValue !== null || serviceMaxValue !== null;
          const minValue = serviceMinValue !== null ? serviceMinValue : '';
          const maxValue = serviceMaxValue !== null ? serviceMaxValue : '';

          const value = showValue ? `${minValue} - ${maxValue} ${serviceUnit}` : '-';

          return <span className='text-content-primary text-md font-normal'>{value}</span>;
        },
      },
      {
        header: t('customer:healthCheck.wheel.services.drawer.serviceStats.table.header.60thPercentile'),
        accessorKey: `60th`,
        cell: ({ row }) => {
          const { service60thPercentile, serviceUnit } = row.original;
          return (
            <span className='text-content-primary text-md font-normal'>
              {service60thPercentile !== null ? `${service60thPercentile} ${serviceUnit}` : '-'}
            </span>
          );
        },
      },
      {
        header: t('customer:healthCheck.wheel.services.drawer.serviceStats.table.header.latestResult'),
        accessorKey: 'value',
        cell: ({ row }) => {
          const { serviceLatestResult, serviceUnit, serviceLastDayValue } = row.original;
          const isStatusRow = typeof serviceLatestResult === 'string';
          let percentChangeFromLastDay = 0;
          let isShowPeriod = false;
          if (serviceLastDayValue && serviceLatestResult && typeof serviceLatestResult === 'number') {
            isShowPeriod = true;
            percentChangeFromLastDay = ((serviceLatestResult - serviceLastDayValue) / serviceLastDayValue) * 100;
          }

          const changeStatusType = statusChanged(percentChangeFromLastDay);

          return (
            <div className='flex flex-col items-start'>
              <span className='text-content-primary'>
                {!isStatusRow
                  ? `${serviceLatestResult ? `${serviceLatestResult} ${serviceUnit}` : '-'}`
                  : t(serviceLatestResult)}
              </span>
              {isShowPeriod && (
                <div className='flex items-center gap-x-2'>
                  {changeStatusType === 'good' ? (
                    <BoldSquareArrowUp className='text-content-meta-green size-4' />
                  ) : changeStatusType === 'bad' ? (
                    <BoldSquareArrowDown className='text-content-meta-red size-4' />
                  ) : (
                    <div className='rounded-[2px] bg-gray-200 p-1'>
                      <Minus size={4} />
                    </div>
                  )}
                  {changeStatusType === 'none' ? (
                    <span className='text-content-primary text-xs font-medium'>{t('home.noChange')}</span>
                  ) : (
                    <span
                      className={cn(
                        'text-xs',
                        changeStatusType === 'bad' ? 'text-content-meta-red' : 'text-content-meta-green',
                      )}>
                      {Math.abs(percentChangeFromLastDay).toFixed(2)}%
                    </span>
                  )}
                  <span className='text-xs text-gray-500'>
                    {t('customer:healthCheck.wheel.client.drawer.statistics.vsLastPeriod')} (
                    {serviceLastDayValue ? `${serviceLastDayValue} ${serviceUnit || ''}` : '-'})
                  </span>
                </div>
              )}
            </div>
          );
        },
      },
      {
        header: t('customer:healthCheck.wheel.services.drawer.serviceStats.table.header.average'),
        accessorKey: 'averageValue',
        cell: ({ row }) => {
          const { serviceAverage, serviceUnit } = row.original;
          return (
            <span className='text-md text-content-primary font-normal'>
              {serviceAverage !== null ? `${serviceAverage} ${serviceUnit}` : '-'}
            </span>
          );
        },
      },
      {
        header: t('customer:healthCheck.wheel.services.drawer.serviceStats.table.header.weeklyTrends'),
        accessorKey: 'weeklyTrends',
        cell: ({ row }) => {
          const { weeklyTrend } = row.original;
          return <StatusTrend data={weeklyTrend || []} />;
        },
      },
    ];
  }, [t]);

  return columns;
};
