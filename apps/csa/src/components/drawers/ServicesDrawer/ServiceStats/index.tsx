import { ServicesSummaryProps } from '@/components/drawers/ServicesDrawer/ServicesSummary';
import { ServiceStatistics } from '@/components/drawers/ServicesDrawer/type';
import { useTabLineId } from '@/stores/tab.store';
import { cn } from '@/utils';
import { getCoreRowModel, getSortedRowModel, useReactTable } from '@tanstack/react-table';
import { Search } from 'lucide-react';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useGetServiceStats } from 'services/Service';
import { getDayjsFromDate, getTimezone, getUnixTime } from 'services/Utils';
import { Repeat, X } from 'ui/UIAssets';
import {
  AxonButton,
  AxonDateRangePicker,
  AxonInput,
  AxonSelect,
  AxonSelectContent,
  AxonSelectGroup,
  AxonSelectLabel,
  AxonSelectTrigger,
  AxonSelectValue,
  AxonSheetClose,
  AxonTableData,
} from 'ui/UIComponents';
import { useColumn } from './columns';
import { useFilterState } from './filterState';

type DateRange = {
  from: Date | undefined;
  to?: Date;
};

const ServiceStatisticsDrawer = (props: ServicesSummaryProps) => {
  const { t } = useTranslation();
  const { onButtonClick } = props;
  const lineId = useTabLineId() || '';
  const { searchText, startDate, endDate, changeFilter } = useFilterState();

  const handleSetCustomTimeRange = (value?: DateRange) => {
    const startDate = getDayjsFromDate(value?.from).startOf('day').tz(getTimezone(), true).toDate();
    const endDate = getDayjsFromDate(value?.to).startOf('day').tz(getTimezone(), true).toDate();

    changeFilter({
      startDate,
      endDate,
    });
  };

  const {
    data: queryServiceStatisticDrawerData,
    isLoading,
    isError,
  } = useGetServiceStats(
    {
      customerId: lineId,
      startDate: getUnixTime(startDate) || 0,
      endDate: getUnixTime(endDate) || undefined,
    },
    {
      enabled: !!lineId,
      staleTime: 1 * 60 * 1000,
    },
  );
  const serviceStatisticDrawerData = queryServiceStatisticDrawerData?.data;
  const { results: detailData } = serviceStatisticDrawerData ?? {};

  const columns = useColumn();

  const rows = useMemo<ServiceStatistics[]>(() => {
    if (!searchText || !detailData) return detailData || [];
    return detailData.filter((item) => item.serviceType.toLowerCase().includes(searchText.toLowerCase()));
  }, [detailData, searchText]);

  const table = useReactTable({
    columns,
    data: rows,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
  });

  return (
    <div className='flex h-screen flex-col'>
      <div className='bg-surface-body h-d16 border-border-primary flex shrink-0 items-center justify-between border-b px-6'>
        <div>
          <AxonSelect>
            <AxonSelectTrigger
              className='text-content-primary w-fit gap-x-2 bg-transparent text-xl font-medium'
              aria-label='Service statistics - Internet'>
              <AxonSelectValue
                placeholder={t('customer:healthCheck.wheel.services.drawer.serviceStats.headerSelect')}
              />
            </AxonSelectTrigger>
            <AxonSelectContent>
              <AxonSelectGroup>
                <AxonSelectLabel>
                  {t('customer:healthCheck.wheel.services.drawer.serviceStats.headerSelect')}
                </AxonSelectLabel>
              </AxonSelectGroup>
            </AxonSelectContent>
          </AxonSelect>
        </div>
        <div>
          <AxonButton variant={'ghost'} className='ml-auto' onClick={onButtonClick}>
            <Repeat className='text-component-hyperlink mr-2 size-4' />{' '}
            <p className='text-component-hyperlink font-book text-xs'>
              {t('customer:healthCheck.wheel.services.drawer.table.switchToSummary')}
            </p>
          </AxonButton>
          <AxonSheetClose>
            <AxonButton variant={'outline'} size={'icon'}>
              <X className='size-4' />
            </AxonButton>
          </AxonSheetClose>
        </div>
      </div>
      <div className={cn('border-b-border-flat flex items-center justify-between border-b p-4')}>
        <div className='relative w-96'>
          <Search size={16} className='text-muted-foreground absolute left-3 top-3' />
          <AxonInput
            name='serviceSearchByType'
            placeholder={t('device:wanStatistics.searchInputPlaceholder')}
            className='pl-10'
            value={searchText}
            onChange={(e) => changeFilter({ searchText: e.target.value })}
          />
        </div>
        <div className='flex gap-2'>
          <AxonDateRangePicker
            selected={{
              from: startDate || undefined,
              to: endDate || undefined,
            }}
            onApply={handleSetCustomTimeRange}
            showIcon={false}
          />
          {/*<AxonButton variant='primary' size={'sm'} startDecorator={<RotateCcw className='size-4' />}>*/}
          {/*  Run test*/}
          {/*</AxonButton>*/}
        </div>
      </div>

      <div className={cn('bg-surface-section scrollbar-lg h-full overflow-auto')}>
        <AxonTableData
          table={table}
          showFooter={false}
          headerRowClassName='bg-surface-section'
          bodyRowClassName='bg-surface-section'
          isLoading={isLoading}
          isError={isError}
        />
      </div>
    </div>
  );
};
export default ServiceStatisticsDrawer;
