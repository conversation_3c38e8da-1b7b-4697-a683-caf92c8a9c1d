import get from 'lodash/get';
import { useDrawerStore, useServiceStatsDrawer } from '@/stores/drawer.store';
import { useEffect } from 'react';
import { useShallow } from 'zustand/react/shallow';
import { useGetTimeRangeByLine } from '@/stores/timeRange.store';
import { useTabLineId } from '@/stores/tab.store';

export function useFilterState() {
  const lineId = useTabLineId();
  const timeRangeSelected = useGetTimeRangeByLine(lineId || '');
  const defaultStartDate = get(timeRangeSelected, 'startDate', null);
  const defaultEndDate = get(timeRangeSelected, 'endDate', null);

  const updateDrawer = useDrawerStore(useShallow((state) => state.updateDrawerServiceStats));

  const { searchText, startDate, endDate, open } = useServiceStatsDrawer();

  useEffect(() => {
    if (open) {
      updateDrawer({
        searchText: '',
        startDate: defaultStartDate,
        endDate: defaultEndDate,
      });
    }
  }, [open, defaultEndDate, defaultStartDate, updateDrawer]);

  const changeFilter = ({
    startDate,
    endDate,
    searchText,
  }: {
    startDate?: Date | null;
    endDate?: Date | null;
    searchText?: string;
  }) => {
    updateDrawer({
      ...(startDate !== undefined ? { startDate } : {}),
      ...(endDate !== undefined ? { endDate } : {}),
      ...(searchText !== undefined ? { searchText } : {}),
    });
  };

  return {
    searchText,
    startDate,
    endDate,
    changeFilter,
  };
}
