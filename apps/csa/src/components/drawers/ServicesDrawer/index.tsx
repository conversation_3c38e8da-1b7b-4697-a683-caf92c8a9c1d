import ServicesSummary from '@/components/drawers/ServicesDrawer/ServicesSummary';
import ServiceStatistics from '@/components/drawers/ServicesDrawer/ServiceStats';
import { useDrawerStore, useServiceStatsDrawer, useServiceSummaryDrawer } from '@/stores/drawer.store';
import { AxonSheet, AxonSheetContent } from 'ui/UIComponents';
import { useShallow } from 'zustand/react/shallow';

const ServicesDrawer = () => {
  const { open: isStatsOpen } = useServiceStatsDrawer();
  const { open: isSummaryOpen } = useServiceSummaryDrawer();
  const { openDrawer, closeDrawer } = useDrawerStore(
    useShallow((state) => ({
      openDrawer: state.openDrawer,
      closeDrawer: state.closeDrawer,
    })),
  );

  const isOpen = isStatsOpen || isSummaryOpen;
  const isStatisticsView = !!isStatsOpen;

  const onButtonClick = () => {
    if (isStatisticsView) {
      openDrawer('serviceSummary');
    } else {
      openDrawer('serviceStats');
    }
  };

  const handleChangeOpen = (open: boolean) => {
    const drawerType = isStatisticsView ? 'serviceStats' : 'serviceSummary';
    if (open) openDrawer(drawerType);
    else closeDrawer(drawerType);
  };

  return (
    <AxonSheet open={isOpen} onOpenChange={handleChangeOpen}>
      <AxonSheetContent className='w-4/5 max-w-none p-0' hideCloseButton>
        {isOpen && (
          <>
            {isStatisticsView ? (
              <ServiceStatistics onButtonClick={onButtonClick} />
            ) : (
              <ServicesSummary onButtonClick={onButtonClick} />
            )}
          </>
        )}
      </AxonSheetContent>
    </AxonSheet>
  );
};

export default ServicesDrawer;
