import { useDrawerStore, useServiceSummaryDrawer } from '@/stores/drawer.store';
import { useEffect } from 'react';
import { useShallow } from 'zustand/react/shallow';
import { SortingState } from '@tanstack/react-table';
import get from 'lodash/get';
import { SERVICE_TYPE_OPTIONS } from '@/components/HealthCheck/constant';
import { useGetTimeRangeByLine } from '@/stores/timeRange.store';
import { useTabLineId } from '@/stores/tab.store';

export function useFilterState() {
  const lineId = useTabLineId() || '';
  const timeRangeSelected = useGetTimeRangeByLine(lineId);
  const defaultStartDate = get(timeRangeSelected, 'startDate', null);
  const defaultEndDate = get(timeRangeSelected, 'endDate', null);

  const updateDrawer = useDrawerStore(useShallow((state) => state.updateDrawerServiceSummary));

  const { searchText, serviceTypes, sorting, open } = useServiceSummaryDrawer();

  useEffect(() => {
    if (open) {
      updateDrawer({
        searchText: '',
        serviceTypes: SERVICE_TYPE_OPTIONS.map((st) => st.value),
        sorting: [],
      });
    }
  }, [open, updateDrawer]);

  const changeFilter = ({
    searchText,
    serviceTypes,
    sorting,
  }: {
    searchText?: string;
    serviceTypes?: string[];
    sorting?: SortingState;
  }) => {
    updateDrawer({
      ...(searchText !== undefined ? { searchText } : {}),
      ...(serviceTypes !== undefined ? { serviceTypes } : {}),
      ...(sorting !== undefined ? { sorting } : {}),
    });
  };

  return {
    startDate: defaultStartDate,
    endDate: defaultEndDate,
    searchText,
    serviceTypes,
    sorting,
    changeFilter,
  };
}
