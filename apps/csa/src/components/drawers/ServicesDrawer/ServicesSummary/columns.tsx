import { ColumnDef } from '@tanstack/react-table';
import { useTranslation } from 'react-i18next';
import { useMemo } from 'react';
import { ServiceSummary } from '../type';
import { cn } from '@/utils';
import { DTLogo } from 'ui/UIAssets';
import { getRelativeTimeFromNow } from 'services/Utils';
import { EllipsisVertical } from 'lucide-react';

const getTextColor = (status: string) => {
  let textColor = 'bg-content-meta-green';

  if (status === 'unstable') {
    textColor = 'bg-content-meta-orange';
  } else if (status === 'veryUnstable') {
    textColor = 'bg-content-meta-red';
  }
  return textColor;
};

export const useColumn = () => {
  const { t } = useTranslation();

  const columns = useMemo<ColumnDef<ServiceSummary, any>[]>(
    () => [
      {
        header: () => (
          <span className='pl-5'>{t('customer:healthCheck.wheel.services.drawer.table.header.serviceName')}</span>
        ),
        accessorKey: 'serviceName',
        cell: ({
          row: {
            original: { serviceName, status },
          },
        }) => (
          <div className='flex gap-3 pl-5'>
            <img src={DTLogo} alt={t('dtLogoAlt')} className='size-10 object-contain' />
            <div className='flex flex-col gap-1'>
              <p className='font-medium'>{serviceName ?? '-'}</p>
              <p className='text-content-tertiary flex items-center justify-center gap-2'>
                <span className={cn('inline-block size-2 rounded-full', getTextColor(status ?? ''))}></span>
                {status ? t(status) : '-'}
              </p>
            </div>
          </div>
        ),
      },
      {
        header: t('customer:healthCheck.wheel.services.drawer.table.header.type'),
        accessorKey: 'type',
        cell: ({
          row: {
            original: { type },
          },
        }) => <div className='flex gap-2'>{type ?? '-'}</div>,
      },
      {
        header: t('customer:healthCheck.wheel.services.drawer.table.header.activeSince'),
        accessorKey: 'activeSince',
        cell: ({
          row: {
            original: { activeSince },
          },
        }) => <div className='flex gap-2'>{activeSince ? getRelativeTimeFromNow(activeSince) : '-'}</div>,
      },
      {
        header: t('customer:healthCheck.wheel.services.drawer.table.header.interface'),
        accessorKey: 'interfaceName',
        cell: ({
          row: {
            original: { interfaceName, vlanId },
          },
        }) => (
          <div className='flex gap-2'>
            <div className='flex flex-col gap-1'>
              <p className='font-medium'>{interfaceName ?? '-'}</p>
              <p className='text-content-tertiary'>{vlanId ?? '-'}</p>
            </div>
          </div>
        ),
      },
      {
        header: t('customer:healthCheck.wheel.services.drawer.table.header.speedTest'),
        accessorKey: 'speedSummary',
        cell: ({
          row: {
            original: { speedSummary },
          },
        }) => (
          <div className='flex gap-2'>
            <div className='flex flex-col gap-1'>
              <p className='text-content-primary'>
                {speedSummary?.dn ? `${speedSummary.dn} Mbps` : '-'} /{' '}
                {speedSummary?.up ? `${speedSummary.up} Mbps` : '-'} /{' '}
                {speedSummary?.latency ? `${speedSummary.latency} ms` : '-'} /{' '}
                {speedSummary?.jitter ? `${speedSummary.jitter} ms` : '-'}
              </p>
              {/*<p className='text-content-primary opacity-60'>Last tested {lastTested}</p>*/}
            </div>
          </div>
        ),
      },
      {
        id: 'more',
        cell: () => <EllipsisVertical className='size-4' />,
      },
    ],
    [t],
  );

  return columns;
};
