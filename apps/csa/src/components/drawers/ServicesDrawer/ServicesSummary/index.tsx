import {
  AxonButton,
  AxonCheckbox,
  AxonInput,
  AxonPopover,
  AxonPopoverContent,
  AxonPopoverTrigger,
  AxonSeparator,
  AxonSheetClose,
  AxonTableData,
} from 'ui/UIComponents';
import { Repeat, Search, X } from 'ui/UIAssets';
import { useTranslation } from 'react-i18next';
import { getCoreRowModel, getSortedRowModel, useReactTable } from '@tanstack/react-table';
import { useColumn } from './columns';
import { useFilterState } from './filterState';
import { cn } from '@/utils';
import { ChevronDown } from 'lucide-react';
import { useGetServiceSummary } from 'services/Service';
import { useTabLineId } from '@/stores/tab.store';
import { ServiceSummary } from '../type';
import { useMemo } from 'react';
import { getUnixTime } from 'services/Utils';
import { SERVICE_TYPE_OPTIONS } from '@/components/HealthCheck/constant';

export type ServicesSummaryProps = {
  onButtonClick: () => void;
};

export default function ServicesSummary(props: ServicesSummaryProps) {
  const { onButtonClick } = props;
  const { t } = useTranslation();
  const lineId = useTabLineId();

  const { searchText, startDate, endDate, serviceTypes, sorting, changeFilter } = useFilterState();

  const { data: serviceSummaryData, isLoading } = useGetServiceSummary(
    {
      customerId: lineId ?? '',
      startDate: getUnixTime(startDate) || 0,
      endDate: getUnixTime(endDate) || undefined,
    },
    {
      enabled: !!lineId,
      staleTime: 1 * 60 * 1000,
    },
  );

  const filteredData: ServiceSummary[] = useMemo(() => {
    const { results } = serviceSummaryData?.data || {};

    if (!results) {
      return [];
    }
    return results.filter((item) => {
      const isFilterByType =
        serviceTypes.length === 0 || serviceTypes.some((s) => s.toLowerCase() === item?.type?.toLowerCase());
      const isSearchByName = searchText === '' || item?.serviceName?.toLowerCase().includes(searchText.toLowerCase());
      return isFilterByType && isSearchByName;
    });
  }, [searchText, serviceTypes, serviceSummaryData]);

  const columns = useColumn();

  const table = useReactTable({
    data: filteredData,
    columns,
    state: {
      sorting,
    },
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
  });
  const handleSelectServiceType = (key: string, checked: boolean) => {
    changeFilter({
      serviceTypes: checked ? [...serviceTypes, key] : serviceTypes.filter((x) => x !== key),
    });
  };

  const handleClear = () => {
    changeFilter({
      serviceTypes: [],
    });
  };

  return (
    <div className='flex h-full flex-col gap-3'>
      <div className='bg-surface-body h-d16 border-border-primary flex shrink-0 items-center justify-between border-b px-6'>
        <div>
          <p className='text-content-primary text-xl font-medium'>
            {t('customer:healthCheck.wheel.services.drawer.title')}{' '}
            <span className='text-content-tertiary'>{filteredData.length ?? 0}</span>
          </p>
        </div>
        <div>
          <AxonButton variant={'ghost'} className='ml-auto' onClick={onButtonClick}>
            <Repeat className='text-component-hyperlink mr-2 size-4' />{' '}
            <p className='text-component-hyperlink font-book text-xs'>
              {t('customer:healthCheck.wheel.client.drawer.summary.switchToStats')}
            </p>
          </AxonButton>
          <AxonSheetClose>
            <AxonButton variant={'outline'} size={'icon'}>
              <X className='size-4' />
            </AxonButton>
          </AxonSheetClose>
        </div>
      </div>
      <div className='bg-surface-body border-border-primary flex h-[64px] items-center justify-between gap-x-3 px-6'>
        <AxonInput
          placeholder={t('customer:healthCheck.wheel.services.drawer.table.searchPlaceholder')}
          startDecorator={<Search />}
          className='w-80'
          value={searchText}
          onChange={(e) => changeFilter({ searchText: e.target.value })}
        />
        <div>
          <AxonPopover>
            <AxonPopoverTrigger className='flex items-center justify-between'>
              <AxonButton>
                <span className={cn(serviceTypes.length > 0 && 'text-component-hyperlink')}>
                  {t('customer:healthCheck.wheel.services.drawer.table.selectType')}
                </span>
                {serviceTypes.length > 0 ? (
                  <span className='bg-surface-accent-purple rounded-xs text-neutral-0 ml-1 px-2 text-xs'>
                    {serviceTypes.length}
                  </span>
                ) : (
                  <ChevronDown className='ml-1 size-4 text-gray-500' />
                )}
              </AxonButton>
            </AxonPopoverTrigger>
            <AxonPopoverContent>
              <div className='space-y-3'>
                {SERVICE_TYPE_OPTIONS.map((st) => (
                  <label key={st.value} className='flex cursor-pointer items-center justify-between'>
                    <span className='text-content-primary text-sm'>
                      {t(`customer:healthCheck.wheel.services.types.${st.value}`)}
                    </span>
                    <AxonCheckbox
                      checked={serviceTypes.includes(st.value)}
                      onCheckedChange={(checked: boolean) => handleSelectServiceType(st.value, checked)}
                    />
                  </label>
                ))}
                <AxonSeparator />
                <AxonButton className='w-full' onClick={handleClear}>
                  {t('customer:healthCheck.wheel.services.drawer.table.clearAll')}
                </AxonButton>
              </div>
            </AxonPopoverContent>
          </AxonPopover>
        </div>
      </div>
      <div className='bg-surface-section border-border-primary flex-1 overflow-auto border-t'>
        <AxonTableData classes={{ wrapper: 'max-h-screen' }} table={table} showFooter={false} isLoading={isLoading} />
      </div>
    </div>
  );
}
