import { AxonAlertDialogWrapper, AxonToast } from 'ui/UIComponents';
import { useTranslation } from 'react-i18next';
import { useResetClientParentalControl } from 'services/Actions';

type Props = {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  lineId: string;
  macAddress: string;
};
const ParentalControlResetDialog = ({ lineId, macAddress, onOpenChange, open }: Props) => {
  const { t } = useTranslation();

  const { mutateAsync: resetClientParentalControl, isPending } = useResetClientParentalControl();

  const handleConfirm = async () => {
    try {
      await resetClientParentalControl({ lineId, stationMacAddress: macAddress });
      AxonToast.success(t('operationSuccess'));
    } catch (error) {
      AxonToast.error(t('operationFailed'));
    }
  };

  return (
    <AxonAlertDialogWrapper
      open={open}
      onOpenChange={onOpenChange}
      title={t('device:deviceInfo.action.cpe.parentalControlReset')}
      description={t('device:deviceInfo.action.dialogDescription', { deviceId: macAddress })}
      confirmText={t('device:deviceInfo.action.confirmText')}
      confirmLoading={isPending}
      cancelText={t('device:deviceInfo.action.cancelText')}
      onConfirm={handleConfirm}
    />
  );
};

export default ParentalControlResetDialog;
