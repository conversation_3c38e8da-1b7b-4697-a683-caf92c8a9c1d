import {
  AxonButton,
  AxonDropdownMenu,
  AxonDropdownMenuContent,
  AxonDropdownMenuGroup,
  AxonDropdownMenuItem,
  AxonDropdownMenuSeparator,
  AxonDropdownMenuTrigger,
} from 'ui/UIComponents';

import { useTabDeviceId, useTabLineId } from '@/stores/tab.store';
import { useMemo, useState } from 'react';
import { MoreHorizontal, RefreshCcw, Settings, List, RotateCcw } from 'ui/UIAssets';
import { useTranslation } from 'react-i18next';
import { useGetLineInfo } from 'services/LineInfo';
import get from 'lodash/get';
import ParentalControlResetDialog from './MenuItemDialogs/ParentalControlResetDialog';
import PingTestDialog from './MenuItemDialogs/PingTestDialog';
import { DeviceRecord } from '../ClientsTable/type';
import { useGetCapabilities } from '@/hooks/useGetCapabilities';
import { useDrawerStore } from '@/stores/drawer.store';

interface ClientActionMenuProps {
  clientInfo: DeviceRecord;
  onChangeDrawerType?: () => void;
}

const ClientActionMenu = ({ clientInfo, onChangeDrawerType }: ClientActionMenuProps) => {
  const stationIpAddress = clientInfo.networkAddress.ipAddress;
  const parentalControls = clientInfo.parentalControls;
  const stationMacAddress = clientInfo.networkAddress.macAddress;

  const { t } = useTranslation();
  const deviceId = useTabDeviceId() || '';
  const lineId = useTabLineId();

  const updateDrawer = useDrawerStore((state) => state.updateDrawerClientConnectionStats);

  const { data: lineInfo } = useGetLineInfo(deviceId || lineId || '');

  const [dialogsState, setDialogsState] = useState({
    parentalControlResetDialog: false,
    pingTestDialog: false,
  });
  const capabilities = useGetCapabilities();

  const hasClientParentalControlCapability = useMemo(() => {
    return parentalControls.some((e) => e.enabled);
  }, [parentalControls]);

  const isDeviceOnline = get(clientInfo, 'deviceInfo.isOnline', false);

  const goToClientDetail = () => {
    onChangeDrawerType?.();
    updateDrawer({
      cpeId: clientInfo.cpeId,
      stationMac: clientInfo.networkAddress.macAddress,
    });
  };

  return (
    <>
      <AxonDropdownMenu>
        <AxonDropdownMenuTrigger asChild>
          <AxonButton className='cursor-pointer' variant='outline' size='icon' aria-label={t('ariaLabel.moreActions')}>
            <MoreHorizontal />
          </AxonButton>
        </AxonDropdownMenuTrigger>
        <AxonDropdownMenuContent align='end' className='w-2xs'>
          <AxonDropdownMenuGroup>
            <AxonDropdownMenuItem onClick={goToClientDetail}>
              <List className='size-4' /> {t('device:client.action.moreInfo')}
            </AxonDropdownMenuItem>
          </AxonDropdownMenuGroup>
          <AxonDropdownMenuSeparator />
          <AxonDropdownMenuGroup>
            <AxonDropdownMenuItem disabled>
              <Settings className='size-4' /> {t('device:client.action.changeConfigs')}
            </AxonDropdownMenuItem>
            {capabilities?.isParentalControlEnabled && (
              <AxonDropdownMenuItem
                disabled={!hasClientParentalControlCapability}
                onClick={() => setDialogsState({ ...dialogsState, parentalControlResetDialog: true })}>
                <RefreshCcw className='size-4' />
                {t('device:client.action.resetParentalControl')}
              </AxonDropdownMenuItem>
            )}
            <AxonDropdownMenuItem
              onClick={() => setDialogsState({ ...dialogsState, pingTestDialog: true })}
              disabled={!capabilities?.isPingTestEnabled || !isDeviceOnline}>
              <RotateCcw className='size-4' />
              {t('device:client.action.runPingTest')}
            </AxonDropdownMenuItem>
          </AxonDropdownMenuGroup>
        </AxonDropdownMenuContent>
      </AxonDropdownMenu>
      {stationMacAddress && lineInfo && (
        <ParentalControlResetDialog
          open={dialogsState.parentalControlResetDialog}
          onOpenChange={(open) => setDialogsState({ ...dialogsState, parentalControlResetDialog: open })}
          macAddress={stationMacAddress}
          lineId={get(lineInfo, 'data.id', '')}
        />
      )}
      {dialogsState.pingTestDialog && (
        <PingTestDialog
          open={dialogsState.pingTestDialog}
          onClose={() => setDialogsState({ ...dialogsState, pingTestDialog: false })}
          macAddress={stationMacAddress}
          ipAddress={stationIpAddress}
          clientName={clientInfo.deviceInfo.deviceName}
        />
      )}
    </>
  );
};

export default ClientActionMenu;
