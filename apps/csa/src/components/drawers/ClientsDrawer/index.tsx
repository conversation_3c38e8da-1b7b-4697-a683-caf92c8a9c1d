import { useClientConnectionDrawer, useDrawerStore } from '@/stores/drawer.store';
import { Maximize } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { AxonButton, AxonSheet, AxonSheetContent, AxonSheetTrigger } from 'ui/UIComponents';
import { useShallow } from 'zustand/react/shallow';
import ClientsStats from './ClientsStats';
import ClientsTable from './ClientsTable';

interface ClientsDrawerProps {
  disabled?: boolean;
  isSummaryView: boolean;
  onSwitchView: () => void;
  onCloseDrawer: () => void;
}

const ClientsDrawer = ({ disabled = false, isSummaryView = true, onSwitchView, onCloseDrawer }: ClientsDrawerProps) => {
  const { t } = useTranslation();

  const { open } = useClientConnectionDrawer();
  const { openDrawer, closeDrawer, clearDrawerClientConnection } = useDrawerStore(
    useShallow((state) => ({
      openDrawer: state.openDrawer,
      closeDrawer: state.closeDrawer,
      clearDrawerClientConnection: state.clearDrawerClientConnection,
    })),
  );

  const handleDrawerOpenChange = (open: boolean) => {
    if (open) {
      openDrawer('clientConnection');
    } else {
      closeDrawer('clientConnection');
      onCloseDrawer?.();
      clearDrawerClientConnection();
    }
  };

  return (
    <AxonSheet open={open} onOpenChange={handleDrawerOpenChange}>
      <AxonSheetTrigger disabled={disabled} asChild data-testid='device-clients-drawer-trigger'>
        <AxonButton className='p-2' variant='ghost' size='icon' aria-label={t('ariaLabel.openClients')}>
          <Maximize size={1.5} className='text-content-primary size-4' />
        </AxonButton>
      </AxonSheetTrigger>
      <AxonSheetContent className='w-4/5 max-w-none p-0' hideCloseButton data-testid='device-clients-drawer-content'>
        {open && (
          <>
            {isSummaryView ? (
              <ClientsTable onSwitchView={onSwitchView} />
            ) : (
              <ClientsStats onSwitchView={onSwitchView} />
            )}
          </>
        )}
      </AxonSheetContent>
    </AxonSheet>
  );
};

export default ClientsDrawer;
