import { EQoeStatus } from '@/utils/QOE.util';
import { ConnectionType, WeeklyTrends } from '@/pages/Device/device.type';

export interface CpeClient {
  name: string;
  mac: string;
  type: string;
}

export interface CpeClientStation {
  rssiPercentile: number | null;
  rssiAvg: number | null;
  rssiMin: number | null;
  snr: number | null;
  throughput: number | null;
  latency: number | null;
  txPhyRate: number | null;
  rxPhyRate: number | null;
  trafficUp: number | null;
  trafficDown: number | null;
}

export interface CpeClientHistoryDatapoint {
  date: number;
  stations: Record<string, Record<string, CpeClientStation> | { qoe: number }>;
}

export interface CpeBand {
  bandId: string;
  band: string;
  label: string;
  networkType: string;
  connectionType: ConnectionType;
}

export interface ClientHistoryResponse {
  results: {
    [cpeId: string]: CpeClientHistoryDatapoint[];
  };
  clients: CpeClient[];
  bands: CpeBand[];
  cpeIds: string[];
}

export type ClientHistoryParams = {
  customerId: string;
  deviceId: string;
  startDate: number;
  endDate?: number;
};

export type ClientStatisticsTableDrawerParams = {
  customerId: string;
  deviceId: string;
  stationMac: string;
  startDate: number;
  endDate?: number;
};

interface DeviceInfo {
  deviceName: string;
  isOnline: boolean;
  lastSeenTimestamp: number; // UNIX timestamp
  deviceType: string; // e.g. 'iphone', 'tv', 'laptop'
}

interface NetworkConnection {
  connectionType: ConnectionType;
  band: string;
  description: string; // e.g., SSID or Ethernet port name
  connectionInterface: string | null;
}

interface NetworkAddress {
  ipAddress: string;
  macAddress: string;
}

interface DataUsageInfo {
  totalUsedGB: number; // e.g., 1.5
  percentChangeFromDayBefore: number; // e.g., 31
}

interface InsightStats {
  eventsCount: number;
  lastUpdatedTimestamp: number; // UNIX timestamp
}

interface SignalStrength {
  rssiValue: number; // e.g., -40 (strong) to -90 (weak)
  coverageDetection: number;
  status: EQoeStatus;
}

export interface DeviceRecord {
  cpeId: string;
  deviceInfo: DeviceInfo;
  networkConnection: NetworkConnection;
  networkAddress: NetworkAddress;
  dataUsage: DataUsageInfo;
  insights: InsightStats;
  signalStrength: SignalStrength;
  parentalControls: {
    enabled: boolean;
    stationMac: string;
    indefinite: boolean;
    parentalRestrictionId: string;
  }[];
}

export interface ClientConnectionResponse {
  results: DeviceRecord[];
  cpeIds: string[];
  deviceTypes: string[];
  connectionInterfaces: string[];
}

export enum ClientHistoryEMetric {
  WIFI_PHY_RATE = 'phyRate',
  WIFI_THROUGHPUT = 'throughput',
  TRAFFIC_DOWN = 'trafficDown',
  TRAFFIC_UP = 'trafficUp',
  LATENCY = 'latency',
  SNR = 'snr',
  RSSI = 'rssi',
  QOE = 'qoe',
}

export type ClientStatsDrawer = {
  type: ClientHistoryEMetric;
  label: string;
  minMax: string | null;
  average: string | null;
  latestResult: number | null;
  secondLatestResult: number | null;
  noOfTest: number | null;
  isShowPercentChange: boolean;
  unit: string | null;
  weeklyTrends: WeeklyTrends[];
};

export interface ClientStatisticsTableDrawerResponse {
  results: ClientStatsDrawer[];
}
