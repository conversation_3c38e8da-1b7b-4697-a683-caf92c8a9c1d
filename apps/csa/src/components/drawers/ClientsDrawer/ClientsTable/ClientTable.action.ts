import debounce from 'lodash/debounce';
import { useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { SortingState } from '@tanstack/react-table';
import { useClientsDrawerAction } from '../ClientsDrawer.action';
import { useClientConnectionDrawer, useDrawerStore } from '@/stores/drawer.store';

export const ALL_VALUE_OPTION = 'all';

export const useClientTableAction = () => {
  const { t } = useTranslation();

  const { data: clientConnection, isError, ...rest } = useClientsDrawerAction();

  const { searchText, cpeId, deviceType, connectionInterface, parentalControl, sorting, status } =
    useClientConnectionDrawer();

  const updateDrawer = useDrawerStore((state) => state.updateDrawerClientConnection);

  const handleChangeSorting = useCallback(
    (updaterOrValue: SortingState | ((old: SortingState) => SortingState)) => {
      const newSorting = typeof updaterOrValue === 'function' ? updaterOrValue(sorting) : updaterOrValue;
      updateDrawer({ sorting: newSorting });
    },
    [sorting, updateDrawer],
  );

  const debouncedSearchInput = useMemo(() => {
    return debounce((value: string) => {
      updateDrawer({ searchText: value });
    }, 500);
  }, [updateDrawer]);

  const handleChangeFilterState = useCallback(
    (key: string, value: any) => {
      updateDrawer({ [key]: value });
    },
    [updateDrawer],
  );

  const filteredData = useMemo(() => {
    const { results } = clientConnection?.data || {};

    if (!results) {
      return [];
    }

    return results.filter((item) => {
      const isFilterCpeId = cpeId === '' || cpeId === ALL_VALUE_OPTION || item.cpeId === cpeId;
      const isFilterDeviceType =
        deviceType === '' || deviceType === ALL_VALUE_OPTION || item.deviceInfo.deviceType === deviceType;
      const isFilterConnectionInterface =
        connectionInterface === '' ||
        connectionInterface === ALL_VALUE_OPTION ||
        item.networkConnection.connectionInterface === connectionInterface;

      let isFilterParentalControl = true;
      if (parentalControl === 'enabled') {
        // When an item has enabled parental control, parentalControls has two items, one has 'enabled' field as true and one is false
        isFilterParentalControl = item.parentalControls.some((control) => control.enabled === true);
      } else if (parentalControl === 'disabled') {
        isFilterParentalControl = item.parentalControls.every((control) => control.enabled === false);
      }

      const isFilterStatus =
        status === '' || status === ALL_VALUE_OPTION || status === (item.deviceInfo.isOnline ? 'online' : 'offline');

      const isFilterSearch =
        searchText === '' || item.deviceInfo.deviceName.toLowerCase().includes(searchText.toLowerCase());
      return (
        isFilterCpeId &&
        isFilterDeviceType &&
        isFilterConnectionInterface &&
        isFilterParentalControl &&
        isFilterStatus &&
        isFilterSearch
      );
    });
  }, [connectionInterface, cpeId, deviceType, clientConnection, parentalControl, searchText, status]);

  const selectionOptions = useMemo(() => {
    const { cpeIds, deviceTypes, connectionInterfaces } = clientConnection?.data || {};
    return {
      cpeIdsOptions: [
        {
          label: t('customer:healthCheck.wheel.client.drawer.summary.allCpe'),
          value: ALL_VALUE_OPTION,
        },
        ...(cpeIds || []),
      ],
      deviceTypesOptions: [
        {
          label: t('customer:healthCheck.wheel.client.drawer.summary.allType'),
          value: ALL_VALUE_OPTION,
        },
        ...(deviceTypes || []),
      ],
      connectionInterfacesOptions: [
        {
          label: t('customer:healthCheck.wheel.client.drawer.summary.allConnection'),
          value: ALL_VALUE_OPTION,
        },
        ...(connectionInterfaces || []),
      ],
      parentalControlOptions: [
        {
          label: t('customer:healthCheck.wheel.client.drawer.summary.parental'),
          value: ALL_VALUE_OPTION,
        },
        {
          label: t('customer:healthCheck.wheel.client.drawer.summary.enable'),
          value: 'enabled',
        },
        {
          label: t('customer:healthCheck.wheel.client.drawer.summary.disable'),
          value: 'disabled',
        },
      ],
    };
  }, [clientConnection, t]);

  return {
    ...rest,
    isError,
    data: clientConnection,
    filteredData,
    filterState: {
      cpeId,
      deviceType,
      connectionInterface,
      parentalControl,
      status,
      searchText,
    },
    setFilterState: handleChangeFilterState,
    sorting,
    handleChangeSorting,
    selectionOptions,
    debouncedSearchInput,
  };
};
