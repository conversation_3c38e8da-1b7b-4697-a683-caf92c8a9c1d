import { getCoreRowModel, getSortedRowModel, useReactTable } from '@tanstack/react-table';
import { useTranslation } from 'react-i18next';
import { Repeat, Search, X } from 'ui/UIAssets';
import {
  AxonButton,
  AxonInput,
  AxonSelectWrapper,
  AxonSheetClose,
  AxonTableData,
  AxonToggleGroup,
  AxonToggleGroupItem,
} from 'ui/UIComponents';
import { useClientTableAction } from './ClientTable.action';
import { useColumn } from './columns';

export type ClientsTableProps = {
  onSwitchView: () => void;
};

const ClientsTable = (props: ClientsTableProps) => {
  const { onSwitchView } = props;
  const { t } = useTranslation();

  const columns = useColumn(onSwitchView);
  const { selectionOptions, filteredData, filterState, setFilterState, debouncedSearchInput, isLoading } =
    useClientTableAction();

  const table = useReactTable({
    data: filteredData,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getRowId: (row) => row.networkAddress.macAddress,
  });

  return (
    <div className='flex h-full flex-col' data-testid='device-client-drawer-summary'>
      <div className='bg-surface-body h-d16 border-border-primary flex shrink-0 items-center justify-between gap-x-3 border-b px-6'>
        <p className='text-content-primary text-xl font-medium'>
          {t('customer:healthCheck.wheel.client.drawer.summary.clients')}{' '}
          <span className='text-content-tertiary' data-testid='device-client-drawer-summary-clients'>
            {filteredData.length}
          </span>
        </p>
        <AxonButton
          variant={'ghost'}
          className='ml-auto'
          onClick={onSwitchView}
          data-testid='device-client-drawer-summary-switch-to-stats'>
          <Repeat className='text-component-hyperlink mr-2 size-4' />{' '}
          <p className='text-component-hyperlink font-book text-xs'>
            {t('customer:healthCheck.wheel.client.drawer.summary.switchToStats')}
          </p>
        </AxonButton>
        <AxonSheetClose asChild>
          <AxonButton
            aria-label={t('ariaLabel.close')}
            variant={'outline'}
            size={'icon'}
            data-testid='device-client-drawer-summary-close'>
            <X className='size-4' />
          </AxonButton>
        </AxonSheetClose>
      </div>
      <div className='bg-surface-body border-border-primary flex h-[64px] items-center gap-x-3 px-6'>
        <AxonInput
          placeholder={t('customer:healthCheck.wheel.client.drawer.summary.searchByDeviceName')}
          startDecorator={<Search />}
          className='w-80'
          defaultValue={filterState.searchText}
          onChange={(e) => debouncedSearchInput(e.target.value)}
          data-testid='device-client-drawer-summary-search'
        />
        <div className='ml-auto'>
          <AxonSelectWrapper
            triggerAriaLabel={t('customer:healthCheck.wheel.client.drawer.summary.cpe')}
            placeHolder={t('customer:healthCheck.wheel.client.drawer.summary.cpe')}
            value={filterState.cpeId}
            onChange={(value) => setFilterState('cpeId', value)}
            options={selectionOptions.cpeIdsOptions as string[]}
            data-testid='device-client-drawer-summary-cpe-select'
          />
        </div>
        <div>
          <AxonSelectWrapper
            triggerAriaLabel={t('customer:healthCheck.wheel.client.drawer.summary.type')}
            placeHolder={t('customer:healthCheck.wheel.client.drawer.summary.type')}
            value={filterState.deviceType}
            onChange={(value) => setFilterState('deviceType', value)}
            options={selectionOptions.deviceTypesOptions as string[]}
            data-testid='device-client-drawer-summary-type-select'
          />
        </div>
        <div>
          <AxonSelectWrapper
            triggerAriaLabel={t('customer:healthCheck.wheel.client.drawer.summary.connection')}
            placeHolder={t('customer:healthCheck.wheel.client.drawer.summary.connection')}
            value={filterState.connectionInterface}
            onChange={(value) => setFilterState('connectionInterface', value)}
            options={selectionOptions.connectionInterfacesOptions as string[]}
            data-testid='device-client-drawer-summary-connection-select'
          />
        </div>
        <div>
          <AxonSelectWrapper
            triggerAriaLabel={t('customer:healthCheck.wheel.client.drawer.summary.parental')}
            placeHolder={t('customer:healthCheck.wheel.client.drawer.summary.parental')}
            value={filterState.parentalControl}
            onChange={(value) => setFilterState('parentalControl', value)}
            options={selectionOptions.parentalControlOptions}
            data-testid='device-client-drawer-summary-parental-select'
          />
        </div>
        <AxonToggleGroup
          onValueChange={(value) => setFilterState('status', value as 'online' | 'offline')}
          value={filterState.status}
          type='single'
          className='h-[40px]'
          defaultValue='all'
          data-testid='device-client-drawer-summary-status-toggle-group'>
          <AxonToggleGroupItem value='all' data-testid='device-client-drawer-summary-status-all'>
            {t('customer:healthCheck.wheel.client.drawer.summary.all')}
          </AxonToggleGroupItem>
          <AxonToggleGroupItem value='online' data-testid='device-client-drawer-summary-status-online'>
            {t('customer:healthCheck.wheel.client.drawer.summary.connected')}
          </AxonToggleGroupItem>
          <AxonToggleGroupItem value='offline' data-testid='device-client-drawer-summary-status-offline'>
            {t('customer:healthCheck.wheel.client.drawer.summary.disconnected')}
          </AxonToggleGroupItem>
        </AxonToggleGroup>
      </div>
      <div className='bg-surface-section border-border-primary flex-1 overflow-auto border-t'>
        <AxonTableData
          classes={{ wrapper: 'max-h-screen' }}
          table={table}
          showFooter={false}
          isLoading={isLoading}
          data-testid='device-client-drawer-summary-table'
        />
      </div>
    </div>
  );
};

export default ClientsTable;
