import { useConfigWidgetClientStats, useTabLineId } from '@/stores/tab.store';
import { useCallback, useEffect, useMemo } from 'react';
import { useGetClientStatisticsTableDrawer } from 'services/Client';
import get from 'lodash/get';
import { getDayjsFromDate, getTimezone, getUnixTime } from 'services/Utils';
import { DateRange } from './type';
import { useClientsDrawerAction } from '../ClientsDrawer.action';
import { useGetTimeRangeByLine } from '@/stores/timeRange.store';
import { useClientConnectionDrawer, useDrawerStore } from '@/stores/drawer.store';

export const useClientStatsAction = () => {
  const lineId = useTabLineId();
  const timeRangeSelected = useGetTimeRangeByLine(lineId || '');

  const {
    stats: { stationMac, cpeId, startDate, endDate, searchText },
  } = useClientConnectionDrawer();

  const updateDrawer = useDrawerStore((state) => state.updateDrawerClientConnectionStats);

  const { data: listClients, clientStatsOptions } = useClientsDrawerAction();

  const selectedClientInfo = useMemo(() => {
    const list = get(listClients, 'data.results');
    if (list) {
      return list.find((client) => client.networkAddress.macAddress === stationMac);
    }
  }, [listClients, stationMac]);

  const clientStatsConfig = useConfigWidgetClientStats();
  const defaultCpeId = cpeId || get(clientStatsConfig, 'device.cpeId', '') || get(clientStatsOptions, '0.cpeId', '');
  const defaultStationMac =
    stationMac || get(clientStatsConfig, 'device.stationMac', '') || get(clientStatsOptions, '0.value', '');
  const defaultStartDate = startDate || get(timeRangeSelected, 'startDate', null);
  const defaultEndDate = endDate || get(timeRangeSelected, 'endDate', null);
  const defaultSearchText = searchText || get(clientStatsConfig, 'searchText', '');

  useEffect(() => {
    updateDrawer({
      startDate: defaultStartDate,
      endDate: defaultEndDate,
      searchText: defaultSearchText,
      cpeId: defaultCpeId,
      stationMac: defaultStationMac,
    });
  }, [defaultCpeId, defaultEndDate, defaultSearchText, defaultStartDate, defaultStationMac, updateDrawer]);

  const {
    data: clientStats,
    isError: isClientStatsError,
    ...rest
  } = useGetClientStatisticsTableDrawer(
    {
      deviceId: cpeId ?? '',
      stationMac: stationMac ?? '',
      startDate: getUnixTime(startDate) || 0,
      endDate: getUnixTime(endDate) || undefined,
    },
    {
      enabled: !!cpeId && !!stationMac && !!startDate,
    },
  );

  const handleChangeSearch = (value: string) => {
    updateDrawer({
      searchText: value,
    });
  };

  const setDeviceFilterState = (value: string) => {
    const filter = clientStatsOptions.find((option) => option.value === value);
    updateDrawer({
      cpeId: filter?.cpeId || '',
      stationMac: filter?.value || '',
    });
  };

  const handleSetTimeRange = useCallback(
    (date?: DateRange) => {
      const startDate = getDayjsFromDate(date?.from).startOf('day').tz(getTimezone(), true).toDate();
      const endDate = getDayjsFromDate(date?.to).startOf('day').tz(getTimezone(), true).toDate();

      updateDrawer({
        startDate,
        endDate,
      });
    },
    [updateDrawer],
  );

  return {
    ...rest,
    isError: isClientStatsError,
    data: clientStats,
    startDate,
    endDate,
    searchText,
    selectedClientInfo,
    stationMac,
    clientStatsOptions,
    handleChangeSearch,
    setDeviceFilterState,
    handleSetTimeRange,
  };
};
