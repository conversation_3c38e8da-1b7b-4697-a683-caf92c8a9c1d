import { ColumnDef } from '@tanstack/react-table';
import { Minus } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { ClientStatsDrawer } from './type';
import { BoldSquareArrowDown, BoldSquareArrowUp, MoreHorizontal, TrendingUp } from 'ui/UIAssets';
import { AxonButton, AxonPopover, AxonPopoverContent, AxonPopoverTrigger, AxonSheetClose } from 'ui/UIComponents';
import { cn } from '@/utils';
import { getStatusFromQoE } from '@/components/HealthCheck/utils/wheel.util';
import { StatusTrend } from '@/components/StatusTrend';
import { useCallback } from 'react';
import { CPESections } from '@/constants/cpeElements';
import useTabStore, { useTabDeviceId, useTabLineId } from '@/stores/tab.store';
import { useShallow } from 'zustand/react/shallow';
import { ClientHistoryEMetric } from '@/stores/widgets.config';

type ChangeStatus = 'good' | 'bad' | 'none';

export const useColumn = () => {
  const { t } = useTranslation();
  const lineId = useTabLineId();
  const deviceId = useTabDeviceId() || lineId;
  const { setActiveTab, addTab, addConfigEnhance } = useTabStore(
    useShallow((state) => ({
      setActiveTab: state.setActiveTab,
      addTab: state.addTab,
      addConfigEnhance: state.addConfigEnhance,
    })),
  );

  const changeStatus = (changedInPercentage: number): ChangeStatus => {
    if (changedInPercentage > 0) return 'good';
    if (changedInPercentage < 0) return 'bad';
    return 'none';
  };

  const handleGoToClientHistory = useCallback(
    (metric: ClientHistoryEMetric) => {
      if (!lineId || !deviceId) return;
      addTab({
        id: deviceId,
        name: deviceId,
        type: 'device',
        deviceId: deviceId,
        customerId: lineId,
        config: {
          scrollTo: {
            section: CPESections.CLIENTS,
            widget: 'clientHistory',
          },
        },
      });
      setActiveTab({
        tabId: deviceId,
        tabType: 'device',
      });
      addConfigEnhance('widgets.clientHistory.filterState.metric', metric);
      addConfigEnhance('drawer.clientConnection', false);
    },
    [lineId, addTab, deviceId, addConfigEnhance, setActiveTab],
  );

  const columns: ColumnDef<ClientStatsDrawer>[] = [
    {
      accessorKey: 'label',
      header: t('customer:healthCheck.wheel.client.drawer.statistics.header.label'),
      cell: (info) => info.getValue() || '-',
    },
    {
      accessorKey: 'minMax',
      header: t('customer:healthCheck.wheel.client.drawer.statistics.header.minMax'),
      cell: (info) => info.getValue() || '-',
    },
    {
      accessorKey: 'average',
      header: t('customer:healthCheck.wheel.client.drawer.statistics.header.average'),
      cell: (info) => info.getValue() || '-',
    },
    {
      accessorKey: 'latestResult',
      header: t('customer:healthCheck.wheel.client.drawer.statistics.header.latestResult'),
      cell: ({ row }) => {
        const { latestResult, secondLatestResult, unit, isShowPercentChange, type } = row.original;
        if (type === ClientHistoryEMetric.QOE) {
          return <span>{t(getStatusFromQoE(latestResult))}</span>;
        }
        let percentChange = 0;
        if (latestResult !== null && secondLatestResult) {
          percentChange = ((latestResult - secondLatestResult) / secondLatestResult) * 100;
        }

        const changeStatusType = changeStatus(percentChange);

        return (
          <div className='flex items-center gap-x-2'>
            <div className='flex flex-col'>
              <span>{latestResult !== null ? `${latestResult} ${unit || ''}` : '-'}</span>
              {isShowPercentChange && latestResult !== null && secondLatestResult !== null && (
                <div className='flex items-center gap-x-2'>
                  {changeStatusType === 'good' ? (
                    <BoldSquareArrowUp className='text-content-meta-green size-4' />
                  ) : changeStatusType === 'bad' ? (
                    <BoldSquareArrowDown className='text-content-meta-red size-4' />
                  ) : (
                    <div className='rounded-[2px] bg-gray-200 p-1'>
                      <Minus size={4} />
                    </div>
                  )}
                  {changeStatusType === 'none' ? (
                    <span className='text-content-primary text-xs font-medium'>{t('home.noChange')}</span>
                  ) : (
                    <span
                      className={cn(
                        'text-xs',
                        changeStatusType === 'bad' ? 'text-content-meta-red' : 'text-content-meta-green',
                      )}>
                      {percentChange.toFixed(2)}%
                    </span>
                  )}
                  <span className='text-xs text-gray-500'>
                    {t('customer:healthCheck.wheel.client.drawer.statistics.vsLastPeriod')} (
                    {secondLatestResult !== null ? `${secondLatestResult} ${unit || ''}` : '-'})
                  </span>
                </div>
              )}
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: 'noOfTest',
      header: t('customer:healthCheck.wheel.client.drawer.statistics.header.noOfTests'),
      cell: (info) => info.getValue() || '-',
    },
    {
      accessorKey: 'weeklyTrends',
      header: t('customer:healthCheck.wheel.client.drawer.statistics.header.weeklyTrends'),
      cell: ({ row }) => <StatusTrend data={row.original.weeklyTrends || []} />,
    },
    {
      id: 'actions',
      cell: ({ row }) => (
        <AxonPopover>
          <AxonPopoverTrigger asChild>
            <AxonButton variant={'outline'} size={'icon'} aria-label={t('ariaLabel.moreActions')}>
              <MoreHorizontal />
            </AxonButton>
          </AxonPopoverTrigger>
          <AxonPopoverContent className='w-[200px]'>
            <AxonSheetClose asChild onClick={() => handleGoToClientHistory(row.original.type)}>
              <AxonButton
                variant='pure'
                className='flex items-center gap-x-2'
                aria-label={t('customer:healthCheck.wheel.client.drawer.statistics.body.openGraph')}>
                <TrendingUp />
                <p className='text-md'>{t('customer:healthCheck.wheel.client.drawer.statistics.body.openGraph')}</p>
              </AxonButton>
            </AxonSheetClose>
          </AxonPopoverContent>
        </AxonPopover>
      ),
    },
  ];

  return columns;
};
