import { ClientHistoryEMetric } from '@/stores/widgets.config';
import { WeeklyTrends } from '@/pages/Device/device.type';

export type ClientStatsDrawer = {
  type: ClientHistoryEMetric;
  label: string;
  minMax: string | null;
  average: string | null;
  latestResult: number | null;
  secondLatestResult: number | null;
  noOfTest: number | null;
  isShowPercentChange: boolean;
  unit: string | null;
  weeklyTrends: WeeklyTrends[];
};

export type DateRange = {
  from: Date | undefined;
  to?: Date;
};
