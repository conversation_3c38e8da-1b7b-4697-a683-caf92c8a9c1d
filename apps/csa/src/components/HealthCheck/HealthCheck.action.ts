import { useGetRealtimeHealthcheck } from 'services/Realtime';
import { useMemo } from 'react';
import { Slice, IHealthCheck } from './types';
import { useTabLineId } from '@/stores/tab.store';
import { useGetSelectedRequestInfo } from '@/stores/realtime.store';
import get from 'lodash/get';
import groupBy from 'lodash/groupBy';
import { getSliceText } from './utils/wheel.util';
import { useTranslation } from 'react-i18next';
import { useGetTimeRangeByLine } from '@/stores/timeRange.store';
import { getUnixTime } from 'services/Utils';
import { useGetData, EDataSection } from 'services/GetData';
import { useIsUseHistoricalData } from '@/hooks/useIsUseHistoricalData';

export const useHealthCheckAction = () => {
  const { t } = useTranslation();
  const lineId = useTabLineId() || '';

  // const selectedRequestId = useSelectedRequestId(lineId, 'LINE') || '';
  const selectedRequest = useGetSelectedRequestInfo(lineId, 'LINE');
  const realtimeRequestId = get(selectedRequest, 'realtimeRequestId', '');
  const isUseHistoricalData = useIsUseHistoricalData(lineId, 'LINE');

  const isDataReady = get(selectedRequest, 'isDataReady', false);
  const isRealtimeRequesting = get(selectedRequest, 'isRealtimeRequesting', false);
  const timeConfig = useGetTimeRangeByLine(lineId);
  const startDate = get(timeConfig, 'startDate', null);
  const endDate = get(timeConfig, 'endDate', null);

  const {
    data: healthCheckHistoricalData,
    isLoading: isLoadingHealthCheckHistoricalData,
    ...rest
    // @ts-ignore: getData is a remote untyped generic, we ignore it at runtime since module federation can't infer the generic type
  } = useGetData<IHealthCheck>(
    {
      lineId,
      data: EDataSection.HEALTH_CHECK,
      startDate: getUnixTime(startDate) || 0,
      endDate: getUnixTime(endDate) || 0,
    },
    { enabled: !!lineId && isUseHistoricalData && Boolean(startDate), staleTime: Infinity },
  );

  const { data: realtimeOverviewData, isFetching: isFetchingHealthcheckRealtime } = useGetRealtimeHealthcheck(
    {
      lineId,
      realtimeRequestId,
    },
    {
      enabled: isDataReady && Boolean(realtimeRequestId) && !isUseHistoricalData,
      staleTime: Infinity,
    },
  );

  const isRequestingRealtime = isFetchingHealthcheckRealtime || isRealtimeRequesting;
  const healthCheckData = isRequestingRealtime
    ? undefined
    : isUseHistoricalData
      ? healthCheckHistoricalData
      : realtimeOverviewData;
  const overview = get(healthCheckData, 'data.overview');

  const data = useMemo(() => {
    // client data
    const client = get(healthCheckData, 'data.details.clients') ?? [];
    const connectionTypeGroup = groupBy(client, 'connectionType');
    const ethernetConnection = connectionTypeGroup.ethernet?.length || 0;
    const wifiConnection = connectionTypeGroup.wifi?.length || 0;
    const numberUnknownConnectionType = client.length - ethernetConnection - wifiConnection;

    // cpe data
    const cpe = get(healthCheckData, 'data.details.cpes') ?? [];
    // lan/wlan data
    const lanWlan = get(healthCheckData, 'data.details.networks') ?? [];
    // wan data
    const wan = get(healthCheckData, 'data.details.wans');
    // service data
    const service = get(healthCheckData, 'data.details.services') ?? [];

    const result = {
      clients: {
        title: t(getSliceText(Slice.CLIENTS)),
        data: client,
        description: client.length,
        footerInfo: {
          stable: get(overview, 'clients.stable') ?? 0,
          unstable: get(overview, 'clients.unstable') ?? 0,
          veryUnstable: get(overview, 'clients.veryUnstable') ?? 0,
          disconnected: get(overview, 'clients.disconnected') ?? 0,
          ethernetConnection,
          wifiConnection,
          unknownConnection: numberUnknownConnectionType,
        },
      },
      services: {
        title: t(getSliceText(Slice.SERVICES)),
        data: service || [],
      },
      cpes: {
        title: t(getSliceText(Slice.CPE)),
        data: cpe || [],
      },
      networks: {
        title: t(getSliceText(Slice.LAN_WLAN)),
        data: lanWlan || [],
        footerInfo: {},
      },
      wans: {
        title: t(getSliceText(Slice.WAN)),
        data: wan || [],
      },
    };

    return result;
  }, [overview, healthCheckData, t]);

  return {
    ...rest,
    data,
    overview,
    isLoading: isRequestingRealtime || isLoadingHealthCheckHistoricalData,
  };
};
