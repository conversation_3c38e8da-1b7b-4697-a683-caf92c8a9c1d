import { getDayjsFromToday, getDayjsFromUnixTime } from 'services/Utils';

const MAX_MINUTES_FOR_MINUTE_INTERVAL = 120;

const MAX_MINUTES_FOR_HOURS_INTERVAL = 2880;

const ONE_DAY_IN_MINUTES = 1440;

export function getRelativetimeForCpeBootupTime(dateInMs: number): {
  translationKey: string;
  value: number | null;
} {
  const today = getDayjsFromToday(0)!;
  const dateInDayjs = getDayjsFromUnixTime(dateInMs);
  const minutes = today.diff(dateInDayjs, 'minute');
  let translationKey = '';
  let value: number | null = null;
  if (minutes === 0) {
    translationKey = 'time_unit:less_than_one_minute';
  } else if (minutes == 1) {
    translationKey = 'time_unit:one_minute';
  } else if (minutes < MAX_MINUTES_FOR_MINUTE_INTERVAL) {
    translationKey = `time_unit:minutes`;
    value = minutes;
  } else if (minutes < MAX_MINUTES_FOR_HOURS_INTERVAL) {
    const hours = Math.floor(minutes / 60);
    translationKey = `time_unit:hours`;
    value = hours;
  } else {
    const days = Math.floor(minutes / ONE_DAY_IN_MINUTES);
    translationKey = `time_unit:days`;
    value = days;
  }
  return { translationKey, value };
}
