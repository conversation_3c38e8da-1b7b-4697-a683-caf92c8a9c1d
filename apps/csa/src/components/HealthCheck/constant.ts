import { Slice } from './types';

export const MAP_SLICE_TO_DATA = {
  [Slice.WAN]: 'wans',
  [Slice.LAN_WLAN]: 'networks',
  [Slice.CLIENTS]: 'clients',
  [Slice.SERVICES]: 'services',
  [Slice.CPE]: 'cpes',
};

export const SERVICE_TYPE_OPTIONS = [
  { value: 'broadband', label: 'Broadband' },
  { value: 'streaming', label: 'Streaming' },
  { value: 'cybersecurity', label: 'Cybersecurity' },
  { value: 'voip', label: 'VoIP' },
  { value: 'homeSecurity', label: 'Home Security' },
];
