import { useEffect, useMemo, Fragment } from 'react';
import {
  calculatePoint,
  calculateSliceAngles,
  calculateOffset,
  calculateTextPosition,
  getSliceStyle,
  calculateCenterPoint,
  calculateTextOffset,
} from '../utils/wheel.util';
import useHealthCheckStore from '../store';
import { WheelConfig, IHealthCheck, BaseOverview, IHealthCheckOverview } from '../types';
import { EHealthCheckStatus } from '../enum';
import { MAP_SLICE_TO_DATA } from '../constant';

type SlicePathProps = {
  selectedSlice: number | null;
  wheelConfig: WheelConfig;
  overview?: IHealthCheck['overview'] | null;
};

const Colors = {
  [EHealthCheckStatus.STABLE]: 'transparent',
  [EHealthCheckStatus.UNSTABLE]: 'rgb(var(--content-meta-yellow))',
  [EHealthCheckStatus.VERY_UNSTABLE]: 'rgb(var(--content-meta-red))',
  [EHealthCheckStatus.UNKNOWN]: 'transparent',
};

function calculateStatus(
  currentSlide: keyof IHealthCheckOverview,
  overviewDataForSlice?: BaseOverview | { status: EHealthCheckStatus },
): EHealthCheckStatus {
  if (currentSlide === 'wans') {
    const data = overviewDataForSlice as { status: EHealthCheckStatus };
    return data?.status || EHealthCheckStatus.UNKNOWN;
  }
  const data = overviewDataForSlice as BaseOverview;
  if (!data) {
    return EHealthCheckStatus.UNKNOWN;
  }
  if (data.veryUnstable) {
    return EHealthCheckStatus.VERY_UNSTABLE;
  }
  if (data.unstable) {
    return EHealthCheckStatus.UNSTABLE;
  }
  if (data.stable) {
    return EHealthCheckStatus.STABLE;
  }

  return EHealthCheckStatus.UNKNOWN;
}

export const useGenerateSlices = (props: SlicePathProps) => {
  const setPaths = useHealthCheckStore((state) => state.setPaths);

  const { selectedSlice, wheelConfig, overview } = props;
  const { WHEEL_RADIUS, CENTER_X, CENTER_Y, INNER_CIRCLE_RADIUS, VISUAL_CONSTANTS } = wheelConfig;
  const { TOTAL_SLICES, STROKE_WIDTH, BASE_OFFSET, SELECTED_OFFSET_INCREASE, SLICE_ANGLE } = VISUAL_CONSTANTS;

  const slicePaths = useMemo(
    () =>
      Array.from({ length: TOTAL_SLICES }, (_, i) => {
        const isSelected = selectedSlice === i;

        const currentSlide: keyof IHealthCheckOverview = MAP_SLICE_TO_DATA[i];
        const currentOverviewData = overview?.[currentSlide];
        const status = calculateStatus(currentSlide, currentOverviewData);

        const { startAngle, endAngle, centerAngle } = calculateSliceAngles(i);
        const currentOffset = calculateOffset(isSelected, selectedSlice);

        // Calculate offset position
        const offsetPoint = calculatePoint(CENTER_X, CENTER_Y, currentOffset, centerAngle);
        const cx = offsetPoint.x;
        const cy = offsetPoint.y;

        const pathRadius = WHEEL_RADIUS - STROKE_WIDTH + (isSelected ? currentOffset : BASE_OFFSET);

        // Calculate path points
        const start = calculatePoint(cx, cy, pathRadius, startAngle);
        const end = calculatePoint(cx, cy, pathRadius, endAngle);
        const innerStart = calculatePoint(cx, cy, INNER_CIRCLE_RADIUS, startAngle);
        const innerEnd = calculatePoint(cx, cy, INNER_CIRCLE_RADIUS, endAngle);

        const pathData = `
                  M ${innerStart.x} ${innerStart.y}
                  L ${start.x} ${start.y}
                  A ${pathRadius} ${pathRadius} 0 0 1 ${end.x} ${end.y}
                  L ${innerEnd.x} ${innerEnd.y}
                  A ${INNER_CIRCLE_RADIUS} ${INNER_CIRCLE_RADIUS} 0 0 0 ${innerStart.x} ${innerStart.y}
                  Z
                `;

        return {
          data: pathData,
          rotationDeg: 360 - 90 - centerAngle,
          index: i,
          color: Colors[status],
        };
      }),
    [
      CENTER_X,
      CENTER_Y,
      WHEEL_RADIUS,
      selectedSlice,
      INNER_CIRCLE_RADIUS,
      BASE_OFFSET,
      STROKE_WIDTH,
      TOTAL_SLICES,
      overview,
    ],
  );

  const gradientDefs = useMemo(() => {
    return (
      <defs>
        {slicePaths.map((path) => {
          const startAngle = SLICE_ANGLE * path.index - 36;
          const endAngle = SLICE_ANGLE * (path.index + 1) - 36;
          const sliceCenterAngle = (startAngle + endAngle) / 2;
          const isSelected = path.index === selectedSlice;
          const currentOffset = path.index === selectedSlice ? BASE_OFFSET + SELECTED_OFFSET_INCREASE : BASE_OFFSET;
          const offsetX = currentOffset * Math.cos((startAngle + SLICE_ANGLE / 2 - 90) * (Math.PI / 180));
          const offsetY = currentOffset * Math.sin((startAngle + SLICE_ANGLE / 2 - 90) * (Math.PI / 180));
          const cx = CENTER_X + offsetX;
          const cy = CENTER_Y + offsetY;

          // End at outer circle relative to the current slice position
          const gradientEndX =
            cx +
            (WHEEL_RADIUS - STROKE_WIDTH + (isSelected ? currentOffset : 0)) *
              Math.cos((sliceCenterAngle - 90) * (Math.PI / 180));
          const gradientEndY =
            cy +
            (WHEEL_RADIUS - STROKE_WIDTH + (isSelected ? currentOffset : 0)) *
              Math.sin((sliceCenterAngle - 90) * (Math.PI / 180));
          return (
            <Fragment key={path.index}>
              <linearGradient
                id={`sliceGradient-${path.index}`}
                x1={CENTER_X}
                y1={CENTER_Y}
                x2={gradientEndX}
                y2={gradientEndY}
                gradientUnits='userSpaceOnUse'>
                <stop offset='0%' style={{ stopColor: path.color, stopOpacity: 0.2 }}>
                  <animate attributeName='offset' values='0;1;1' dur='4s' repeatCount='indefinite' keyTimes='0;0.5;1' />
                </stop>
                <stop offset='30%' style={{ stopColor: path.color, stopOpacity: 0.3 }}>
                  <animate
                    attributeName='offset'
                    values='0.3;1.3;1.3'
                    dur='4s'
                    repeatCount='indefinite'
                    keyTimes='0;0.5;1'
                  />
                </stop>
                <stop offset='100%' style={{ stopColor: path.color, stopOpacity: 0.2 }}>
                  <animate attributeName='offset' values='1;2;2' dur='4s' repeatCount='indefinite' keyTimes='0;0.5;1' />
                </stop>
              </linearGradient>
            </Fragment>
          );
        })}
      </defs>
    );
  }, [
    CENTER_X,
    CENTER_Y,
    WHEEL_RADIUS,
    selectedSlice,
    slicePaths,
    BASE_OFFSET,
    STROKE_WIDTH,
    SELECTED_OFFSET_INCREASE,
    SLICE_ANGLE,
  ]);

  const sliceContents = useMemo(
    () =>
      slicePaths.map(({ rotationDeg, index }) => {
        const { startAngle, endAngle } = calculateSliceAngles(index);
        const isSelected = selectedSlice === index;

        // Calculate where to anchor the text relative to the wheel
        const textOffset = calculateTextOffset(isSelected);
        const centerPoint = calculateCenterPoint({
          offset: textOffset,
          angle: startAngle,
          centerPoint: { x: CENTER_X, y: CENTER_Y },
        });

        // Calculate final text position from the anchor point
        const centerAngle = (startAngle + endAngle) / 2;
        const textPosition = calculateTextPosition({
          wheelRadius: WHEEL_RADIUS,
          centerPoint,
          centerAngle,
        });

        return {
          text: `Slice ${index + 1}`,
          style: getSliceStyle(textPosition),
          rotationDeg,
          index,
        };
      }),
    [slicePaths, CENTER_X, CENTER_Y, WHEEL_RADIUS, selectedSlice],
  );

  useEffect(() => {
    setPaths(slicePaths);
  }, [slicePaths, setPaths]);

  return {
    slicePaths,
    gradientDefs,
    sliceContents,
  };
};
