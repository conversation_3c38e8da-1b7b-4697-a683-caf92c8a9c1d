import { Fragment } from 'react';
import { TPath, WheelConfig } from '../types';
interface SlicePathProps {
  path: TPath;
  isActive: boolean;
  wheelConfig: WheelConfig;
  handleSliceClick: (id: number) => void;
}

export const SlicePath = (props: SlicePathProps) => {
  const { path, isActive, wheelConfig, handleSliceClick } = props;
  const {
    CENTER_X,
    CENTER_Y,
    VISUAL_CONSTANTS: { STROKE_WIDTH },
  } = wheelConfig;

  return (
    <Fragment key={path.index}>
      <path d={path.data} className='fill-surface-section' />
      <path
        d={path.data}
        strokeWidth={STROKE_WIDTH}
        className='stroke-border-flat cursor-pointer'
        onClick={() => handleSliceClick(path.index)}
        style={{
          transformOrigin: `${CENTER_X}px ${CENTER_Y}px`,
          opacity: isActive ? 1 : 0.3,
          transition: 'all 0.7s ',
          fill: `url(#sliceGradient-${path.index})`,
        }}
      />
    </Fragment>
  );
};
