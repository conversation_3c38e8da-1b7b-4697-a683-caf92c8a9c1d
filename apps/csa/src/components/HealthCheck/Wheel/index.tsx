import { memo } from 'react';
import { useKeyDown } from 'services/Hooks';
import { useShallow } from 'zustand/react/shallow';
import { MAP_SLICE_TO_DATA } from '../constant';
import { useGenerateSlices } from '../hooks/useGenerateSlices';
import useHealthCheckStore from '../store';
import { HealthCheckStore, IHealthCheck } from '../types';
import { SliceContent } from './SliceContent';
import { SlicePath } from './SlicePath';
import useCsaStore from '@/stores/csa.store';
import { calculateWheelConfig } from '../utils/wheel.util';

type WheelProps = {
  size?: number;
  isLoading?: boolean;
  data?: IHealthCheck['overview'] | null;
};

const Wheel = (props: WheelProps) => {
  const { size = 600, data, isLoading } = props;

  const isSearchOpen = useCsaStore((state) => state.isSearchOpen);

  const { selectedSlice, setSelectedSlice, paths, prevSlice, nextSlice } = useHealthCheckStore(
    useShallow((state: HealthCheckStore) => ({
      selectedSlice: state.selectedSlice,
      setSelectedSlice: state.setSelectedSlice,
      paths: state.paths,
      prevSlice: state.prevSlice,
      nextSlice: state.nextSlice,
    })),
  );
  const currentRotation = paths.find((path) => path.index === selectedSlice)?.rotationDeg || 0;

  //TODO fill color path base on status.

  const wheelConfig = calculateWheelConfig(size);
  const { CENTER_X, CENTER_Y, WHEEL_RADIUS, INNER_CIRCLE_RADIUS, OUTER_CIRCLE_PADDING, VISUAL_CONSTANTS } = wheelConfig;
  const { STROKE_WIDTH } = VISUAL_CONSTANTS;

  const { slicePaths, gradientDefs, sliceContents } = useGenerateSlices({
    selectedSlice,
    wheelConfig,
    overview: data,
  });

  const handleSliceClick = (index: number) => {
    setSelectedSlice(index);
  };

  useKeyDown(
    [
      [
        'Escape',
        (e) => {
          e.preventDefault();
          setSelectedSlice(null);
        },
      ],
      [
        'ArrowDown',
        (e) => {
          e.preventDefault();
          prevSlice();
        },
      ],
      [
        'ArrowUp',
        (e) => {
          e.preventDefault();
          nextSlice();
        },
      ],
    ],
    !isSearchOpen,
  );

  return (
    <div
      className='relative'
      style={{
        height: size,
        width: size,
        transform: `rotate(${currentRotation}deg)`,
        transformOrigin: 'center center',
        transition: 'all 0.7s ',
      }}>
      <svg width={size} height={size} xmlns='http://www.w3.org/2000/svg'>
        {/* Gradient defs */}
        {gradientDefs}

        {/* Outer circle */}
        <circle
          cx={CENTER_X}
          cy={CENTER_Y}
          r={WHEEL_RADIUS + OUTER_CIRCLE_PADDING}
          className='fill-surface-action stroke-border-flat'
          strokeWidth={STROKE_WIDTH}
          style={{ transition: 'all 0.7s ' }}
        />
        {/* Slice paths */}
        {slicePaths.map((path) => {
          const isActive = selectedSlice === path.index || selectedSlice === null; // when selectedSlice is null, all slices are active
          return (
            <SlicePath
              key={path.index}
              path={path}
              isActive={isActive}
              wheelConfig={wheelConfig}
              handleSliceClick={handleSliceClick}
            />
          );
        })}
        {/* Inner circle */}
        <circle
          cx={CENTER_X}
          cy={CENTER_Y}
          r={INNER_CIRCLE_RADIUS}
          className='fill-surface-tile'
          style={{
            opacity: 1,
            transition: 'all 0.7s ',
            filter: 'drop-shadow(0px 12.552px 50.209px rgba(0, 0, 0, 0.25))',
          }}
        />
      </svg>

      {/* Slice contents */}
      {sliceContents.map((comp) => (
        <SliceContent
          isLoading={isLoading}
          data={data?.[MAP_SLICE_TO_DATA[comp.index]]}
          key={comp.index}
          sliceIndex={comp.index}
          onClick={() => handleSliceClick(comp.index)}
          style={{
            ...comp.style,
            rotate: `${-1 * currentRotation}deg`,
            opacity: selectedSlice === comp.index ? 1 : selectedSlice !== null ? 0.5 : 1,
          }}
        />
      ))}
    </div>
  );
};

export default memo(Wheel);
