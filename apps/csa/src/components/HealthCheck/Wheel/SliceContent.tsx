import { AxonSkeletonLoader } from 'ui/UIComponents';
import { Slice, BaseOverview, IHealthCheckOverview } from '../types';
import { useMemo } from 'react';
import { getSliceText } from '../utils/wheel.util';
import { getStatusIcon, getStatusText } from '../utils/statusIcon.util';
import { EHealthCheckStatus } from '../enum';
import { useTranslation } from 'react-i18next';
import { TFunction } from 'i18next';
interface SliceComponentProps {
  sliceIndex: number;
  style: React.CSSProperties;
  onClick: () => void;
  isLoading?: boolean;
  data?: BaseOverview & IHealthCheckOverview['wans'];
}

const getSliceContent = (
  slice: Slice,
  t: TFunction<'translation', undefined>,
  data?: SliceComponentProps['data'],
  isLoading?: boolean,
) => {
  if (isLoading) {
    return (
      <div className='flex items-center justify-center gap-x-2'>
        <AxonSkeletonLoader className='h-5 min-w-8 basis-1/3' />
        <AxonSkeletonLoader className='h-5 min-w-8 basis-1/3' />
        <AxonSkeletonLoader className='h-5 min-w-8 basis-1/3' />
      </div>
    );
  }

  switch (slice) {
    case Slice.WAN: {
      return (
        <div data-testid={`healthcheck-wheel-${slice}-content`} className='flex items-center justify-center gap-x-2'>
          {getStatusIcon(data?.status)}
          <span className='text-content-primary text-extra-lg font-medium capitalize leading-[120%]'>
            {t(getStatusText(data?.status))}
          </span>
        </div>
      );
    }
    case Slice.LAN_WLAN:
      return (
        <div data-testid={`healthcheck-wheel-${slice}-content`} className='flex items-center justify-center gap-x-3'>
          <div className='flex items-center justify-center gap-x-2'>
            {getStatusIcon(EHealthCheckStatus.STABLE)}
            <span className='text-content-primary text-extra-lg font-medium leading-[120%]'>{data?.stable}</span>
          </div>
          <div className='flex items-center justify-center gap-x-2'>
            {getStatusIcon(EHealthCheckStatus.UNSTABLE)}
            <span className='text-content-primary text-extra-lg font-medium leading-[120%]'>{data?.unstable}</span>
          </div>
          <div className='flex items-center justify-center gap-x-2'>
            {getStatusIcon(EHealthCheckStatus.VERY_UNSTABLE)}
            <span className='text-content-primary text-extra-lg font-medium leading-[120%]'>{data?.veryUnstable}</span>
          </div>
          {/* <div className='flex items-center justify-center gap-x-2'>
            {getStatusIcon(EHealthCheckStatus.UNKNOWN)}
            <span className='text-content-primary text-extra-lg font-medium leading-[120%]'>{data?.disconnected}</span>
          </div> */}
        </div>
      );
    case Slice.CLIENTS:
      return (
        <div data-testid={`healthcheck-wheel-${slice}-content`} className='flex items-center justify-center gap-x-3'>
          <div className='flex items-center justify-center gap-x-2'>
            {getStatusIcon(EHealthCheckStatus.STABLE)}
            <span className='text-content-primary text-extra-lg font-medium leading-[120%]'>{data?.stable}</span>
          </div>
          <div className='flex items-center justify-center gap-x-2'>
            {getStatusIcon(EHealthCheckStatus.UNSTABLE)}
            <span className='text-content-primary text-extra-lg font-medium leading-[120%]'>{data?.unstable}</span>
          </div>
          <div className='flex items-center justify-center gap-x-2'>
            {getStatusIcon(EHealthCheckStatus.VERY_UNSTABLE)}
            <span className='text-content-primary text-extra-lg font-medium leading-[120%]'>{data?.veryUnstable}</span>
          </div>
          <div className='flex items-center justify-center gap-x-2'>
            {getStatusIcon(EHealthCheckStatus.UNKNOWN)}
            <span className='text-content-primary text-extra-lg font-medium leading-[120%]'>{data?.disconnected}</span>
          </div>
        </div>
      );
    case Slice.SERVICES:
      return (
        <div data-testid={`healthcheck-wheel-${slice}-content`} className='flex items-center justify-center gap-x-3'>
          <div className='flex items-center justify-center gap-x-2'>
            {getStatusIcon(EHealthCheckStatus.STABLE)}
            <span className='text-content-primary text-extra-lg font-medium leading-[120%]'>{data?.stable}</span>
          </div>
          <div className='flex items-center justify-center gap-x-2'>
            {getStatusIcon(EHealthCheckStatus.UNSTABLE)}
            <span className='text-content-primary text-extra-lg font-medium leading-[120%]'>{data?.unstable}</span>
          </div>
          <div className='flex items-center justify-center gap-x-2'>
            {getStatusIcon(EHealthCheckStatus.VERY_UNSTABLE)}
            <span className='text-content-primary text-extra-lg font-medium leading-[120%]'>{data?.veryUnstable}</span>
          </div>
          {/* <div className='flex items-center justify-center gap-x-2'>
            {getStatusIcon(EHealthCheckStatus.UNKNOWN)}
            <span className='text-content-primary text-extra-lg font-medium leading-[120%]'>{data?.disconnected}</span>
          </div> */}
        </div>
      );
    case Slice.CPE:
      return (
        <div data-testid={`healthcheck-wheel-${slice}-content`} className='flex items-center justify-center gap-x-3'>
          <div className='flex items-center justify-center gap-x-2'>
            {getStatusIcon(EHealthCheckStatus.STABLE)}
            <span className='text-content-primary text-extra-lg font-medium leading-[120%]'>{data?.stable}</span>
          </div>
          <div className='flex items-center justify-center gap-x-2'>
            {getStatusIcon(EHealthCheckStatus.UNSTABLE)}
            <span className='text-content-primary text-extra-lg font-medium leading-[120%]'>{data?.unstable}</span>
          </div>
          <div className='flex items-center justify-center gap-x-2'>
            {getStatusIcon(EHealthCheckStatus.VERY_UNSTABLE)}
            <span className='text-content-primary text-extra-lg font-medium leading-[120%]'>{data?.veryUnstable}</span>
          </div>
          <div className='flex items-center justify-center gap-x-2'>
            {getStatusIcon(EHealthCheckStatus.UNKNOWN)}
            <span className='text-content-primary text-extra-lg font-medium leading-[120%]'>{data?.disconnected}</span>
          </div>
        </div>
      );
    default:
      return t('unknown');
  }
};

export const SliceContent = (props: SliceComponentProps) => {
  const { sliceIndex, style, onClick, isLoading, data } = props;
  const { t } = useTranslation();
  const sliceContent = useMemo(() => {
    return getSliceContent(sliceIndex, t, data, isLoading);
  }, [sliceIndex, data, isLoading, t]);
  const title = useMemo(() => t(getSliceText(sliceIndex)), [sliceIndex, t]);

  return (
    <div
      data-testid={`healthcheck-wheel-${sliceIndex}`}
      style={style}
      className='absolute flex items-center justify-center'
      onClick={onClick}>
      <div className='gap-xs min-1 flex flex-col'>
        <p
          data-testid={`healthcheck-wheel-${sliceIndex}-title`}
          className='text-content-primary text-md font-suisse font-semibold tracking-[0.56px] opacity-60'>
          {title}
        </p>
        {sliceContent}
      </div>
    </div>
  );
};
