import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import { HealthCheckStore } from './types';
import useTabStore from '@/stores/tab.store';
import { TOTAL_SLICES } from './utils/wheel.util';
import { wheelTypeMap } from '@/components/Insights/insight.type';
import { ALL_VALUE_OPTION } from '@/components/Insights/InsightsDrawer';

const useHealthCheckStore = create<HealthCheckStore>()(
  immer(
    devtools(
      (set) => ({
        paths: [],
        selectedSlice: null,
        nextSlice: () => {
          set(
            (state) => {
              state.selectedSlice = ((state.selectedSlice ?? 0) + 1) % TOTAL_SLICES;
              useTabStore.getState().addConfigEnhance('healthCheck.selectedSlice', state.selectedSlice ?? null);
            },
            undefined,
            { type: 'nextSlice' },
          );
        },
        prevSlice: () => {
          set(
            (state) => {
              state.selectedSlice =
                (state.selectedSlice ?? 0) - 1 < 0 ? TOTAL_SLICES - 1 : (state.selectedSlice ?? 0) - 1;
              useTabStore.getState().addConfigEnhance('healthCheck.selectedSlice', state.selectedSlice ?? null);
            },
            undefined,
            { type: 'prevSlice' },
          );
        },
        setPaths: (paths) => {
          set(
            (state) => {
              state.paths = paths;
            },
            undefined,
            { type: 'setPaths', paths },
          );
        },

        setSelectedSlice: (selectedSlice) => {
          set(
            (state) => {
              state.selectedSlice = selectedSlice;
              useTabStore.getState().addConfigEnhance('healthCheck.selectedSlice', state.selectedSlice ?? null);
              useTabStore
                .getState()
                .addConfigEnhance(
                  'widgets.insight.filterQueryParams.category',
                  state.selectedSlice || state.selectedSlice === 0
                    ? wheelTypeMap[state.selectedSlice]
                    : ALL_VALUE_OPTION,
                );
            },
            undefined,
            { type: 'setSelectedSlice', selectedSlice },
          );
        },
      }),
      { name: 'HealthCheckStore', store: 'HealthCheckStore' },
    ),
  ),
);

export default useHealthCheckStore;
