import { default as CloseIcon } from '@/assets/vector.svg';
import ClientsDrawer from '@/components/drawers/ClientsDrawer';
import { HealthCheckDetailSkeleton } from '@/components/HealthCheck/Detail/Skeleton';
import { CPESections } from '@/constants/cpeElements';
import useTabStore, { TabConfig, useTabLineId } from '@/stores/tab.store';
import { ArrowDown, ArrowUp } from 'lucide-react';
import { useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { getRelativeTimeFromNow } from 'services/Utils';
import {
  BoldLinkRoundAngle,
  BoldTranslation,
  Circle,
  getDeviceImage,
  Maximize,
  Signifier,
  Triangle,
  X,
} from 'ui/UIAssets';
import { AxonButton, AxonCard, AxonSeparator, AxonTooltipWrapper } from 'ui/UIComponents';
import { useShallow } from 'zustand/react/shallow';
import useHealthCheckStore from '../store';
import { BaseOverview, IClient, ICpe, IHealthCheck, INetwork, IService, IWan, Slice } from '../types';
import { getStatusText } from '../utils/statusIcon.util';
import ItemDetail from './ItemDetail';
import WanDetail from './wans';
import { useDrawerStore } from '@/stores/drawer.store';

interface IFooterInfo {
  stable: number;
  unstable: number;
  veryUnstable: number;
  disconnected: number;
  ethernetConnection: number;
  wifiConnection: number;
  unknownConnection: number;
}

interface IProps {
  detail: {
    title: string;
    data: IHealthCheck['details'][keyof IHealthCheck['details']];
    description?: string;
    footerInfo?: IFooterInfo;
  };
  isLoading: boolean;
  overview: BaseOverview & { status: string };
  itemType: Slice;
}

const Detail = (props: IProps) => {
  const { detail, itemType, overview, isLoading } = props;
  const { data, title, description } = detail;
  const { setActiveTab, addTab } = useTabStore(
    useShallow((state) => ({
      setActiveTab: state.setActiveTab,
      addTab: state.addTab,
      addConfigEnhance: state.addConfigEnhance,
    })),
  );
  const [isSummaryView, setIsSummaryView] = useState(true);

  const onSwitchView = () => setIsSummaryView((prev) => !prev);

  const onCloseClientDrawer = () => setIsSummaryView(true);

  const openDrawer = useDrawerStore((state) => state.openDrawer);
  const lineId = useTabLineId();

  const onClickToNavigate = useCallback(
    (deviceId?: string, scrollTo?: TabConfig['scrollTo']) => {
      if (!deviceId) return;
      addTab({
        id: deviceId,
        name: deviceId,
        type: 'device',
        deviceId: deviceId,
        customerId: lineId,
        config: {
          scrollTo,
        },
      });
      setActiveTab({
        tabId: deviceId,
        tabType: 'device',
      });
    },
    [lineId, addTab, setActiveTab],
  );

  const { setSelectedSlice, prevSlice, nextSlice } = useHealthCheckStore(
    useShallow((state) => ({
      selectedSlice: state.selectedSlice,
      setSelectedSlice: state.setSelectedSlice,
      prevSlice: state.prevSlice,
      nextSlice: state.nextSlice,
    })),
  );

  const { t } = useTranslation();

  const DataContent = useMemo(() => {
    if (isLoading) {
      return [1, 2, 3, 4, 5].map((_, idx) => <HealthCheckDetailSkeleton key={idx} />);
    }

    if (data && itemType === Slice.WAN) {
      return <WanDetail data={data as IWan} onClickToNavigate={onClickToNavigate} />;
    }

    if (Array.isArray(data) && itemType !== Slice.WAN) {
      return data.map((item, idx) => (
        <Item key={idx} data={item} type={itemType} onClickToNavigate={onClickToNavigate} />
      ));
    }

    return null;
  }, [data, isLoading, itemType, onClickToNavigate]);

  return (
    <div className='health-check-detail flex flex-col gap-y-5'>
      <AxonCard className='relative flex h-[500px] flex-col overflow-hidden'>
        <div className='scrollbar-hidden flex-1 overflow-auto'>
          <div className='header h-d14 bg-surface-popover pl-xl pr-sm border-border-flat sticky top-0 z-10 flex items-center border-b'>
            <p
              data-testid='healthcheck-detail-title'
              className='font-suisse text-content-primary text-lg font-medium leading-[120%]'>
              {title} <span className='text-content-tertiary font-book leading-[120%]'>{description}</span>
            </p>
            <div className='ml-auto flex'>
              {itemType === Slice.CLIENTS && (
                <ClientsDrawer
                  isSummaryView={isSummaryView}
                  onSwitchView={onSwitchView}
                  onCloseDrawer={onCloseClientDrawer}
                />
              )}
              {itemType === Slice.SERVICES && (
                <AxonButton
                  className='p-2'
                  variant='ghost'
                  size='icon'
                  aria-label={t('ariaLabel.openServices')}
                  onClick={() => openDrawer('serviceSummary')}>
                  <Maximize size={1.5} className='text-content-primary size-4' />
                </AxonButton>
              )}
              <AxonButton
                data-testid='healthcheck-detail-close-button'
                className='p-2'
                variant='ghost'
                size='icon'
                aria-label={t('ariaLabel.close')}
                onClick={() => {
                  setSelectedSlice(null);
                }}>
                <X size={1.5} className='text-content-primary size-4' />
              </AxonButton>
            </div>
          </div>
          <div className='body p-sm'>{DataContent}</div>
        </div>
        <Footer type={itemType} overview={overview} footerInfo={detail.footerInfo} />
      </AxonCard>
      <div className='mt-10 flex flex-row items-center gap-x-3'>
        <AxonButton
          data-testid='healthcheck-detail-up-button'
          aria-label={t('up')}
          variant='outline'
          size='icon'
          onClick={nextSlice}>
          <ArrowUp size={16} />
        </AxonButton>
        <AxonButton
          data-testid='healthcheck-detail-down-button'
          aria-label={t('down')}
          variant='outline'
          size='icon'
          onClick={prevSlice}>
          <ArrowDown size={16} />
        </AxonButton>
        <p className='text-muted-foreground text-sm'>{t('toNavigate')}</p>
      </div>
      <div>
        <AxonButton
          data-testid='healthcheck-detail-esc-button'
          variant='outline'
          onClick={() => {
            setSelectedSlice(null);
          }}>
          ESC
        </AxonButton>
      </div>
    </div>
  );
};

interface IItemProps {
  data: IClient | INetwork | IWan | ICpe | IService;
  type: Slice;
  onClickToNavigate: (deviceId?: string, scrollTo?: TabConfig['scrollTo']) => void;
}

function Item(props: IItemProps) {
  const { t } = useTranslation();
  const { data, type, onClickToNavigate } = props;

  const cpeTypeMapping = {
    Router: t('customer:healthCheck.wheel.cpe.router'),
    Extender: t('customer:healthCheck.wheel.cpe.extender'),
  };

  switch (type) {
    case Slice.CLIENTS: {
      const client = data as IClient;
      const description1 = client.isOnline ? t(getStatusText(client.status)) : t('disconnected');
      const description2 = client.parentId
        ? client.connectionType === 'ethernet'
          ? `Eth | ${client.parentId}`
          : `${client.connectionBand || client.connectionInterface || 'N/A'} | ${client.parentId}`
        : '';

      return (
        <div
          className='p-xs border-border-flat flex flex-row items-start gap-3 border-b last:border-0'
          role='button'
          tabIndex={0}
          onClick={() => onClickToNavigate(client.parentId, { section: CPESections.CLIENTS })}>
          <div className='w-d10 h-d10 border-gradient-border bg-surface-action flex items-center justify-center rounded-sm border'>
            <img
              // className='mix-blend-hard-light dark:mix-blend-screen'
              src={getDeviceImage(client.deviceType)}
              alt='icon'
            />
          </div>
          <div className='min-w-0 flex-1'>
            <div className='mb-2xs'>
              <AxonTooltipWrapper
                label={
                  <p
                    data-testid={`healthcheck-clients-${client.deviceId}-name`}
                    className='text-content-primary text-md w-full min-w-0 truncate font-medium leading-[120%]'>
                    {client.deviceName}
                  </p>
                }
                content={<p>{client.deviceName}</p>}
              />
            </div>
            <div className='flex flex-col gap-[2px]'>
              <ItemDetail
                iconName={client.status}
                description={description1}
                data-testid={`healthcheck-clients-${client.deviceId}-status`}
              />
              <ItemDetail
                iconName={client.connectionType}
                description={description2}
                data-testid={`healthcheck-clients-${client.deviceId}-connection`}
              />
            </div>
          </div>
        </div>
      );
    }
    case Slice.SERVICES: {
      const service = data as IService;
      const {
        // speedStatus,
        latencyStatus,
        trafficStatus,
        latency,
        uploadSpeed,
        downloadSpeed,
        jitter,
        downloadTraffic,
        uploadTraffic,
      } = service;
      const downloadSpeedDescription = downloadSpeed !== null ? `${downloadSpeed} Mbps` : 'N/A';
      const uploadSpeedDescription = uploadSpeed !== null ? `${uploadSpeed} Mbps` : 'N/A';
      const showSpeed = downloadSpeed !== null || uploadSpeed !== null;
      const speedDescription = `${showSpeed ? `${downloadSpeedDescription} / ${uploadSpeedDescription}` : 'N/A'} ${t('customer:healthCheck.wheel.services.speedDnUp')}`;
      const latencyDescription = (latency !== null ? `${latency} ms` : 'N/A') + ` ${t('customer:healthCheck.latency')}`;
      const jitterDescription = (jitter !== null ? `${jitter} ms` : 'N/A') + ` ${t('customer:healthCheck.jitter')}`;
      const downloadTrafficDescription = downloadTraffic !== null ? `${downloadTraffic} GB` : 'N/A';
      const uploadTrafficDescription = uploadTraffic !== null ? `${uploadTraffic} GB` : 'N/A';
      const showTraffic = downloadTraffic !== null || uploadTraffic !== null;
      const trafficDescription = `${showTraffic ? `${downloadTrafficDescription} / ${uploadTrafficDescription}` : 'N/A'} ${t('customer:healthCheck.wheel.services.trafficDnUp')}`;

      return (
        <div className='p-xs border-border-flat flex flex-row items-start gap-3 border-b last:border-0'>
          <div className='w-d10 h-d10 border-gradient-border bg-surface-action flex items-center justify-center rounded-sm border'>
            <img src={getDeviceImage(service.logo)} />
          </div>
          <div className='min-w-0 flex-1'>
            <div className='mb-2xs'>
              <AxonTooltipWrapper
                label={
                  <p
                    data-testid={`healthcheck-services-${service.name}-name`}
                    className='text-content-tertiary font-suisses font-book w-full min-w-0 truncate text-xs leading-[120%]'>
                    {service.name}
                  </p>
                }
                content={<p>{service.name}</p>}
              />
            </div>
            <div className='flex flex-col'>
              <div className='mb-xs'>
                <ItemDetail
                  data-testid={`healthcheck-services-${service.name}-status`}
                  variant='bold'
                  iconName={service.status}
                  description={t(getStatusText(service.status))}
                />
              </div>
              <div className='flex flex-col gap-1'>
                <ItemDetail
                  data-testid={`healthcheck-services-${service.name}-speed`}
                  iconName={showSpeed ? 'stable' : 'unknown'}
                  description={speedDescription}
                />
                <ItemDetail
                  data-testid={`healthcheck-services-${service.name}-latency`}
                  iconName={latencyStatus || 'unknown'}
                  description={latencyDescription}
                />
                <ItemDetail
                  data-testid={`healthcheck-services-${service.name}-jitter`}
                  iconName={jitter ? 'stable' : 'unknown'}
                  description={jitterDescription}
                />
                <ItemDetail
                  data-testid={`healthcheck-services-${service.name}-traffic`}
                  iconName={trafficStatus || 'unknown'}
                  description={trafficDescription}
                />
                {/* <ItemDetail iconName={cpe.cpeStatus} description={firmwareDescripption} />
            <ItemDetail iconName={cpe.cpeStatus} description={lastRebootDescription} />
            <ItemDetail iconName={cpe.cpeStatus} description={rebootCountDescription} />
            <ItemDetail iconName={cpe.cpeStatus} description={cpuStatusDescription} />
            <ItemDetail iconName={cpe.cpeStatus} description={freeMemoryStatusDescription} /> */}
              </div>
            </div>
          </div>
        </div>
      );
    }

    case Slice.CPE: {
      const cpe = data as ICpe;
      const installedRelativeTime = getRelativeTimeFromNow(Number(cpe.installedTime));
      const installedDescription = installedRelativeTime
        ? `${t('customer:healthCheck.wheel.cpe.installed')} ${installedRelativeTime}`
        : `N/A ${t('customer:healthCheck.wheel.cpe.installed')}`;
      const firmwareDescripption = cpe.cpeFirmwareVersion
        ? `${t('customer:healthCheck.wheel.cpe.firmware')} ${cpe.cpeFirmwareVersion}`
        : `N/A ${t('customer:healthCheck.wheel.cpe.firmware')}`;
      const lastRebootRelativeTime = getRelativeTimeFromNow(Number(cpe.lastRebootTime));
      const lastRebootDescription = lastRebootRelativeTime
        ? `${t('customer:healthCheck.wheel.cpe.lastReboot')} ${lastRebootRelativeTime}`
        : `N/A ${t('customer:healthCheck.wheel.cpe.lastReboot')}`;
      const rebootCountDescription =
        cpe.powerCycle !== null
          ? `${cpe.powerCycle} ${t('customer:healthCheck.wheel.cpe.rebootCountDescription')}`
          : `N/A ${t('customer:healthCheck.wheel.cpe.rebootCountDescription')}`;
      const cpuStatusDescription = `${t(getStatusText(cpe.cpuStatus))} ${t('customer:healthCheck.wheel.cpe.cpuStatusDescription')}`;
      const freeMemoryStatusDescription = `${t(getStatusText(cpe.freeMemoryStatus))} ${t('customer:healthCheck.wheel.cpe.freeMemoryStatus')}`;
      return (
        <div className='p-xs border-border-flat flex flex-row items-start gap-3 border-b last:border-0'>
          <div className='w-d10 h-d10 border-gradient-border bg-surface-action flex items-center justify-center rounded-sm border'>
            <img src={getDeviceImage(cpe.cpeType)} />
          </div>
          <div className='min-w-0 flex-1'>
            <div
              role='button'
              tabIndex={0}
              className='flex flex-col'
              onClick={() => onClickToNavigate(cpe.cpeId, { section: CPESections.CPE })}>
              <div className='mb-2xs'>
                <AxonTooltipWrapper
                  label={
                    <p
                      data-testid={`healthcheck-cpe-${cpe.cpeId}-name`}
                      className='text-content-tertiary font-suisses font-book w-full min-w-0 truncate text-xs leading-[120%]'>
                      {cpeTypeMapping[cpe.cpeType]} / {cpe.cpeId}
                    </p>
                  }
                  content={
                    <p>
                      {cpeTypeMapping[cpe.cpeType]} / {cpe.cpeId}
                    </p>
                  }
                />
              </div>
            </div>
            <div className='flex flex-col'>
              <div className='mb-xs'>
                <ItemDetail
                  data-testid={`healthcheck-cpe-${cpe.cpeId}-status`}
                  variant='bold'
                  iconName={cpe.cpeStatus}
                  description={t(getStatusText(cpe.cpeStatus))}
                />
              </div>
              <div className='flex flex-col gap-1'>
                <ItemDetail data-testid={`healthcheck-cpe-${cpe.cpeId}-installed`} description={installedDescription} />
                <ItemDetail
                  data-testid={`healthcheck-cpe-${cpe.cpeId}-firmware`}
                  iconName={cpe.firmwareStatus}
                  description={firmwareDescripption}
                />
                <ItemDetail
                  data-testid={`healthcheck-cpe-${cpe.cpeId}-lastReboot`}
                  iconName={'stable'}
                  description={lastRebootDescription}
                />
                <ItemDetail
                  data-testid={`healthcheck-cpe-${cpe.cpeId}-rebootCount`}
                  iconName={'stable'}
                  description={rebootCountDescription}
                />
                <ItemDetail
                  data-testid={`healthcheck-cpe-${cpe.cpeId}-cpu`}
                  iconName={cpe.cpuStatus}
                  description={cpuStatusDescription}
                />
                <ItemDetail
                  data-testid={`healthcheck-cpe-${cpe.cpeId}-memory`}
                  iconName={cpe.freeMemoryStatus}
                  description={freeMemoryStatusDescription}
                />
              </div>
            </div>
          </div>
        </div>
      );
    }

    case Slice.LAN_WLAN: {
      const lanWlan = data as INetwork;
      const mappingDuplexMode = {
        Full: t('customer:healthCheck.wheel.lanWlan.fullDuplex'),
        Half: t('customer:healthCheck.wheel.lanWlan.halfDuplex'),
        Auto: t('customer:healthCheck.wheel.lanWlan.autoDuplex'),
        Unknown: t('customer:healthCheck.wheel.lanWlan.unknownDuplex'),
      };
      return (
        <div className='p-xs border-border-flat flex flex-row items-start gap-3 border-b last:border-0'>
          <div className='w-d10 h-d10 border-gradient-border bg-surface-action flex items-center justify-center rounded-sm border'>
            <img src={getDeviceImage(lanWlan.cpeType)} alt={getDeviceImage(lanWlan.cpeType)} />
          </div>
          <div className='min-w-0 flex-1'>
            <div className='mb-2xs'>
              <AxonTooltipWrapper
                label={
                  <p
                    data-testid={`healthcheck-network-${lanWlan.cpeId}-name`}
                    className='text-content-tertiary font-suisses font-book w-full min-w-0 truncate text-xs leading-[120%]'
                    role='button'
                    tabIndex={0}
                    onClick={() =>
                      onClickToNavigate(lanWlan.cpeId, {
                        section: CPESections.LAN_WLAN,
                      })
                    }>
                    {cpeTypeMapping[lanWlan.cpeType]} / {lanWlan.cpeId}
                  </p>
                }
                content={
                  <p>
                    {cpeTypeMapping[lanWlan.cpeType]} / {lanWlan.cpeId}
                  </p>
                }
              />
            </div>
            <div className='flex flex-col'>
              <div data-testid={`healthcheck-network-${lanWlan.cpeId}-connections`} className='flex flex-col gap-2'>
                {lanWlan.connections.map((connection) => {
                  const { name, connectionType, band, duplexMode, isActive, status } = connection;
                  const isEthernet = connectionType === 'ethernet';
                  const description1 = isEthernet
                    ? `${name}` + (duplexMode ? ` / (${mappingDuplexMode[duplexMode]})` : '')
                    : `${band || 'N/A'} (${name})`;

                  const description2 = isEthernet
                    ? isActive
                      ? t('customer:healthCheck.wheel.lanWlan.inUse')
                      : t('customer:healthCheck.wheel.lanWlan.notInUse')
                    : `${t(getStatusText(status))}`;

                  return (
                    <div
                      className='flex flex-col gap-1'
                      role='button'
                      tabIndex={0}
                      key={name}
                      onClick={() =>
                        onClickToNavigate(lanWlan.cpeId, {
                          section: CPESections.LAN_WLAN,
                          widget: isEthernet ? 'lanPorts' : 'wifiBands',
                        })
                      }>
                      <ItemDetail
                        data-testid={`healthcheck-network-${lanWlan.cpeId}-connections-${name}-name`}
                        iconName={connectionType}
                        description={description1}
                      />
                      <ItemDetail
                        data-testid={`healthcheck-network-${lanWlan.cpeId}-connections-${name}-status`}
                        iconName={status || 'unknown'}
                        description={description2}
                        variant='bold'
                      />
                    </div>
                  );
                })}
              </div>
            </div>
          </div>
        </div>
      );
    }
    default:
      return null;
  }
}

function Footer(props: { type: Slice; overview: IProps['overview']; footerInfo?: IFooterInfo }) {
  const { t } = useTranslation();
  const { type, footerInfo } = props;

  switch (type) {
    case Slice.CLIENTS: {
      return (
        <footer className='bg-surface-popover border-border-flat p-md sticky inset-x-0 bottom-0 flex flex-col gap-[2px] border-t'>
          <div className='gap-xs text-content-tertiary font-suisse font-book mb-1 flex flex-wrap items-center text-xs leading-[120%]'>
            <div className='flex items-center gap-1'>
              <BoldLinkRoundAngle className='text-content-primary size-4' />
              <span data-testid='healthcheck-clients-footer-ethernet'>
                {t('customer:healthCheck.wheel.footer.eth')}: {footerInfo?.ethernetConnection}
              </span>
              <AxonSeparator orientation='vertical' className='h-[10px]' />
              <span data-testid='healthcheck-clients-footer-wifi'>
                {t('customer:healthCheck.wheel.footer.wifi')}: {footerInfo?.wifiConnection}
              </span>
              {Number(footerInfo?.unknownConnection) > 0 && (
                <>
                  <AxonSeparator orientation='vertical' className='h-[10px]' />
                  <span data-testid='healthcheck-clients-footer-unknown'>
                    {t('customer:healthCheck.wheel.footer.unknown')}: {footerInfo?.unknownConnection}
                  </span>
                </>
              )}
            </div>
          </div>
          <div className='gap-xs text-content-tertiary font-suisse font-book mb-1 flex items-center text-xs leading-[120%]'>
            <BoldTranslation className='text-content-primary size-4' />
            <div className='gap-xs text-content-tertiary font-suisse font-book flex items-center text-xs leading-[120%]'>
              <div className='gap-xs flex items-center'>
                <Circle className='fill-content-meta-green text-gradient-border size-2' />
                <span data-testid='healthcheck-clients-footer-stable'>{footerInfo?.stable ?? 'N/A'}</span>
              </div>
              <AxonSeparator orientation='vertical' className='h-[10px]' />
              <div className='gap-xs flex items-center'>
                <Triangle className='fill-content-meta-orange text-gradient-border size-2' />
                <span data-testid='healthcheck-clients-footer-unstable'>{footerInfo?.unstable ?? 'N/A'}</span>
              </div>
              <AxonSeparator orientation='vertical' className='h-[10px]' />
              <div className='gap-xs flex items-center'>
                <CloseIcon className='text-content-meta-red size-2' />
                <span data-testid='healthcheck-clients-footer-veryUnstable'>{footerInfo?.veryUnstable ?? 'N/A'}</span>
              </div>
              <AxonSeparator orientation='vertical' className='h-[10px]' />
              <div className='gap-xs flex items-center'>
                <Signifier className='text-content-disabled size-2' />
                <span data-testid='healthcheck-clients-footer-disconnected'>{footerInfo?.disconnected ?? 'N/A'}</span>
              </div>
            </div>
          </div>
        </footer>
      );
    }

    case Slice.LAN_WLAN: {
      // if (!overview?.descriptions) {
      //   return null;
      // }
      // return (
      //   <footer className='bg-surface-popover border-border-flat p-md sticky inset-x-0 bottom-0 flex flex-col gap-[2px] border-t'>
      //     {overview.descriptions?.map((description, idx) => (
      //       <p className='text-content-tertiary font-suisse font-book text-xs leading-[120%]' key={idx}>
      //         {description}
      //       </p>
      //     ))}
      //   </footer>
      // );
    }
  }
  return null;
}

export default Detail;
