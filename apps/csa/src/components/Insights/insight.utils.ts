import { IInsight, wheelTypeMap } from './insight.type';

const typeOrder: Record<string, number> = {
  CRITICAL: 0,
  MAJOR: 1,
  MINOR: 2,
};

const componentOrder: Record<string, number> = {
  SERVICES: 0,
  WAN: 1,
  'LAN/WLAN': 2,
  CPE: 3,
  CLIENTS: 4,
};
const DEFAULT_TYPE_ORDER = 99;
const DEFAULT_COMPONENT_ORDER = 99;

export const sortDataInsight = (data: IInsight[], sortTime?: string) => {
  return data.sort((a, b) => {
    const sortByType = sortTime === undefined || sortTime === 'default';

    if (sortByType) {
      const typeA = typeOrder[a.type] ?? DEFAULT_TYPE_ORDER;
      const typeB = typeOrder[b.type] ?? DEFAULT_TYPE_ORDER;
      if (typeA !== typeB) return typeA - typeB;

      const weightA = a.advanced?.qoeWeight ?? 0;
      const weightB = b.advanced?.qoeWeight ?? 0;
      if (weightA !== weightB) return weightB - weightA;

      const compA = componentOrder[a.advanced?.wheelType ?? ''] ?? DEFAULT_COMPONENT_ORDER;
      const compB = componentOrder[b.advanced?.wheelType ?? ''] ?? DEFAULT_COMPONENT_ORDER;
      if (compA !== compB) return compA - compB;
    }

    const dateA = a.occurrenceDay ?? 0;
    const dateB = b.occurrenceDay ?? 0;
    return sortTime === 'oldest' ? dateA - dateB : dateB - dateA;
  });
};

export const conditionFilteringForClient = (item: IInsight, lineId: string) => {
  const wheelFilterKey = wheelTypeMap[2]; // client key
  return (
    wheelFilterKey.toLowerCase() === item.advanced?.wheelType.toLowerCase() ||
    (Boolean(item.deviceId) && item.deviceId !== lineId) // insight for extender, exclude router since the router is not count for clients.
  );
};
