import { useInsightDrawer } from '@/stores/drawer.store';
import { useCallback } from 'react';
import { getDayjsFromDate, getUnixTime } from 'services/Utils';
import { useQueryGetInsightTrend } from 'services/Insight';
import { useTabLineId } from '@/stores/tab.store';
import get from 'lodash/get';
import { IInsightTrend } from '@/components/Insights/insight.type';
import { EInsightCategoryType } from '@/components/Insights/insight.enum';

interface IInsightDialogAction {
  insightCode: string;
  isOpen: boolean;
  deviceId: string;
  band?: string;
  categoryType: EInsightCategoryType;
  stationMac?: string;
}

export const useInsightDialogAction = ({
  insightCode,
  isOpen,
  deviceId,
  band,
  categoryType,
  stationMac,
}: IInsightDialogAction) => {
  const { startDate, endDate } = useInsightDrawer();
  const lineId = useTabLineId() || '';

  const startDateDayjs = getDayjsFromDate(startDate);
  const endDateDayjs = getDayjsFromDate(endDate);

  const totalDays = endDateDayjs.diff(startDateDayjs, 'day') + 1;

  const {
    data: insightTrendDetail,
    isLoading: isInsightTrendLoading,
    isError: isInsightTrendError,
  } = useQueryGetInsightTrend(
    {
      lineId,
      deviceId,
      startDate: getUnixTime(startDate) || 0,
      endDate: getUnixTime(endDate) || undefined,
      insightCode,
      band,
      categoryType,
      stationMac,
    },
    {
      enabled:
        isOpen &&
        Boolean(lineId) &&
        Boolean(deviceId) &&
        Boolean(startDate) &&
        Boolean(endDate) &&
        Boolean(insightCode),
      staleTime: Infinity,
    },
  );

  const insightTrendData: IInsightTrend[] = get(insightTrendDetail, 'data.results') || [];

  const calculateWeeklyTrendBars = useCallback(
    (weeklyTrend: number[]) => {
      return Array.from({ length: totalDays > 7 ? 7 : totalDays })
        .map((_, index) => {
          const date = getDayjsFromDate(endDate).subtract(index, 'day').toDate();

          const hasData = weeklyTrend.some((trend) => {
            const trendDate = getDayjsFromDate(trend);
            return trendDate.isSame(date, 'day');
          });

          return { date, hasData };
        })
        .sort((a, b) => getDayjsFromDate(a.date).valueOf() - getDayjsFromDate(b.date).valueOf());
    },
    [totalDays, endDate],
  );

  return {
    startDate,
    endDate,
    calculateWeeklyTrendBars,
    totalDays,
    dataTrend: insightTrendData,
    isInsightTrendLoading,
    isInsightTrendError,
  };
};
