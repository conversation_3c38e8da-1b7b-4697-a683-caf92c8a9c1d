import useHealthCheckStore from '@/components/HealthCheck/store';
import { HealthCheckStore, Slice } from '@/components/HealthCheck/types';
import { getSliceText } from '@/components/HealthCheck/utils/wheel.util';
import { SliceText, wheelTypeMap } from '@/components/Insights/insight.type';
import InsightsCard from '@/components/Insights/InsightsCard';
import { useDrawerStore, useInsightDrawer } from '@/stores/drawer.store';
import { useTabType } from '@/stores/tab.store';
import get from 'lodash/get';
import { Maximize, Search } from 'lucide-react';
import { useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { getDayjsFromDate, getTimezone } from 'services/Utils';
import {
  AxonButton,
  AxonDateRangePicker,
  AxonInput,
  AxonSelectWrapper,
  AxonSheet,
  AxonSheetContent,
  AxonSheetTrigger,
  AxonSkeletonLoader,
} from 'ui/UIComponents';
import { useShallow } from 'zustand/react/shallow';
import { useFilterState } from './filterState';
import { useInsightDrawerData } from './insightDrawer.action';

export const ALL_VALUE_OPTION = 'all';

type DateRange = {
  from: Date | undefined;
  to?: Date;
};

const InsightsDrawer = () => {
  const { t } = useTranslation();
  const { searchText, selectedDeviceId, category, type, sortTime, startDate, endDate, changeFilter, stationMac } =
    useFilterState();
  const { filteredData, data, isLoading, isError } = useInsightDrawerData({
    searchText,
    deviceId: selectedDeviceId,
    category,
    type,
    sortTime,
    startDate,
    endDate,
    stationMac,
  });
  const { selectedSlice } = useHealthCheckStore(
    useShallow((state: HealthCheckStore) => ({
      selectedSlice: state.selectedSlice,
    })),
  );
  const tabType = useTabType();
  const isDeviceTab = tabType === 'device';
  const updateDrawerInsight = useDrawerStore(useShallow((state) => state.updateDrawerInsight));

  const deviceIds = get(data, 'data.deviceIds') as unknown as string[];
  const categoryIds = get(data, 'data.categoryIds') as unknown as { categoryId: string; categoryName: string }[];
  const rankTypes = get(data, 'data.rankTypes') as unknown as { rankType: string; rankTypeName: string }[];
  const stationMacs = get(data, 'data.stationMacs') as unknown as string[];

  const { open } = useInsightDrawer();
  const { openDrawer, closeDrawer } = useDrawerStore(
    useShallow((state) => ({
      openDrawer: state.openDrawer,
      closeDrawer: state.closeDrawer,
    })),
  );

  useEffect(() => {
    if (open) {
      updateDrawerInsight({
        category: isDeviceTab || selectedSlice === null ? ALL_VALUE_OPTION : wheelTypeMap[selectedSlice],
        sortTime: 'default',
      });
    }
  }, [isDeviceTab, open, selectedSlice, updateDrawerInsight]);

  const deviceOptions = useMemo(() => {
    if (deviceIds) {
      return [
        {
          label: t('insights.drawer.allDevices'),
          value: ALL_VALUE_OPTION,
        },
        ...deviceIds,
      ];
    }
    return [];
  }, [deviceIds, t]);

  const categoryOptions = useMemo(() => {
    if (categoryIds) {
      return [
        {
          label: t('insights.drawer.allCategories'),
          value: ALL_VALUE_OPTION,
        },
        {
          label: t(getSliceText(Slice.CLIENTS)),
          value: SliceText.CLIENTS,
        },
        {
          label: t(getSliceText(Slice.SERVICES)),
          value: SliceText.SERVICES,
        },
        {
          label: t(getSliceText(Slice.CPE)),
          value: SliceText.CPE,
        },
        {
          label: t(getSliceText(Slice.LAN_WLAN)),
          value: SliceText.LAN_WLAN,
        },
        {
          label: t(getSliceText(Slice.WAN)),
          value: SliceText.WAN,
        },
      ];
    }
    return [];
  }, [categoryIds, t]);

  const sortTimeOptions = useMemo(
    () => [
      {
        label: t('insights.drawer.default'),
        value: 'default',
      },
      {
        label: t('insights.drawer.oldest'),
        value: 'oldest',
      },
      {
        label: t('insights.drawer.newest'),
        value: 'newest',
      },
    ],
    [t],
  );

  const rankTypesOptions = useMemo(() => {
    if (rankTypes) {
      return [
        {
          label: t('insights.drawer.allSeverities'),
          value: ALL_VALUE_OPTION,
        },
        ...rankTypes.map((rank) => ({
          label: t(rank.rankTypeName.toLowerCase()),
          value: rank.rankType,
        })),
      ];
    }
    return [];
  }, [rankTypes, t]);

  const stationMacsOptions = useMemo(() => {
    if (stationMacs) {
      return [
        {
          label: t('insights.drawer.allStationMac'),
          value: ALL_VALUE_OPTION,
        },
        ...stationMacs.map((mac) => ({
          label: mac,
          value: mac,
        })),
      ];
    }
    return [];
  }, [stationMacs, t]);

  const handleSetTimeRange = (value?: DateRange) => {
    const startDate = getDayjsFromDate(value?.from).startOf('day').tz(getTimezone(), true).toDate();
    const endDate = getDayjsFromDate(value?.to).startOf('day').tz(getTimezone(), true).toDate();

    changeFilter({
      startDate,
      endDate,
    });
  };

  return (
    <AxonSheet onOpenChange={(e) => !e && closeDrawer('insight')} open={open}>
      <AxonSheetTrigger key='card-insights' asChild>
        <AxonButton
          data-testid='insights-drawer-button'
          disabled={isLoading || isError}
          aria-label={t('ariaLabel.openInsights')}
          variant='outline'
          className='p-none ml-auto size-8'
          onClick={() => openDrawer('insight')}>
          <Maximize size={16} />
        </AxonButton>
      </AxonSheetTrigger>
      <AxonSheetContent className='insight-axonsheet-content w-full max-w-full p-0 xl:w-3/4'>
        <div className='bg-surface-body'>
          <div className='border-b-border-flat flex h-16 items-center gap-3 border-b px-6 py-5 text-xl'>
            <h1 className='text-xl font-medium'>{t('insights.title')}</h1>
            <p className='text-content-tertiary flex items-center text-xl font-normal'>
              {isLoading ? <AxonSkeletonLoader className='h-5 w-8' /> : filteredData.length || 0}
            </p>
          </div>
          <div className='border-b-border-flat insight-search-block flex h-16 items-center justify-between gap-2 border-b px-6'>
            <div className='relative h-fit'>
              <Search className='text-content-tertiary absolute left-3 top-1/2 size-4 -translate-y-1/2' />
              <AxonInput
                placeholder={t('insights.drawer.searchByName')}
                className='h-8 w-[340px] pl-10'
                defaultValue={searchText}
                onChange={(e) => changeFilter({ searchText: e.target.value })}
              />
            </div>
            <div className='flex w-fit gap-2'>
              <AxonSelectWrapper
                classes={{
                  selectTrigger: 'gap-xs w-fit',
                }}
                triggerAriaLabel={t('insights.drawer.sortByTime')}
                value={sortTime}
                placeHolder={t('insights.drawer.sortByTime')}
                onChange={(value) => changeFilter({ sortTime: value })}
                options={sortTimeOptions}
              />
              <AxonSelectWrapper
                classes={{
                  selectTrigger: 'gap-xs w-fit',
                }}
                triggerAriaLabel={t('insights.drawer.device')}
                value={selectedDeviceId}
                placeHolder={t('insights.drawer.device')}
                onChange={(value) => changeFilter({ selectedDeviceId: value })}
                options={deviceOptions as string[]}
              />
              <AxonSelectWrapper
                classes={{
                  selectTrigger: 'gap-xs w-fit',
                }}
                triggerAriaLabel={t('insights.drawer.stationMac')}
                value={stationMac}
                placeHolder={t('insights.drawer.stationMac')}
                onChange={(value) => changeFilter({ stationMac: value })}
                options={stationMacsOptions}
              />
              <AxonSelectWrapper
                classes={{
                  selectTrigger: 'gap-xs w-fit',
                }}
                triggerAriaLabel={t('insights.drawer.category')}
                value={category}
                placeHolder={t('insights.drawer.category')}
                onChange={(value) => changeFilter({ category: value })}
                options={categoryOptions}
              />
              <AxonSelectWrapper
                value={type}
                classes={{
                  selectValue: '',
                  selectTrigger: 'gap-xs w-fit',
                }}
                triggerAriaLabel={t('insights.drawer.type')}
                onChange={(value) => changeFilter({ type: value })}
                placeHolder={t('insights.drawer.type')}
                options={rankTypesOptions}
              />
              <AxonDateRangePicker
                selected={{
                  from: startDate ?? undefined,
                  to: endDate ?? undefined,
                }}
                onApply={handleSetTimeRange}
                showIcon={false}
              />
            </div>
          </div>
        </div>

        {isLoading ? (
          <div className='flex h-full items-center justify-center'>
            <p className='text-content-tertiary font-book flex w-full items-center justify-center text-xl'>
              {t('loading')}
            </p>
          </div>
        ) : filteredData.length === 0 ? (
          <div className='flex h-full items-center justify-center'>
            <p className='text-content-tertiary font-book flex w-full items-center justify-center text-xl'>
              {t('insights.noInsight')}
            </p>
          </div>
        ) : (
          <div className='scrollbar-lg grid max-h-[calc(100%-128px)] grid-cols-[repeat(auto-fill,minmax(304px,1fr))] gap-3 overflow-auto p-8'>
            {filteredData.map((card, idx) => (
              <InsightsCard key={idx} data={card} />
            ))}
          </div>
        )}
      </AxonSheetContent>
    </AxonSheet>
  );
};

export default InsightsDrawer;
