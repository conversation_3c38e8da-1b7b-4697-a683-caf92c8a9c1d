import { useTabLineId } from '@/stores/tab.store';
import get from 'lodash/get';
import { useGetSelectedRequestInfo } from '@/stores/realtime.store';
import { useMemo } from 'react';
import { useQueryGetInsight } from 'services/Insight';
import { useGetRealtimeInsight } from 'services/Realtime';
import { getUnixTime } from 'services/Utils';
import { IInsight, wheelTypeMap } from '../insight.type';
import { ALL_VALUE_OPTION } from '.';
import { conditionFilteringForClient, sortDataInsight } from '../insight.utils';
import { SliceText } from '@/components/Insights/insight.type';
import { useIsUseHistoricalData } from '@/hooks/useIsUseHistoricalData';
import { useInsightDrawer } from '@/stores/drawer.store';

type FilterQueryParams = {
  searchText: string;
  deviceId: string;
  category: string;
  type: string;
  sortTime: string;
  startDate: Date | null;
  endDate: Date | null;
  stationMac: string;
};

export const useInsightDrawerData = (filterQueryParams: FilterQueryParams) => {
  const { searchText, deviceId, category, type, sortTime, startDate, endDate, stationMac } = filterQueryParams;
  const { open } = useInsightDrawer();

  const lineId = useTabLineId() || '';

  const selectedRequest = useGetSelectedRequestInfo(lineId, 'LINE');
  const realtimeRequestId = get(selectedRequest, 'realtimeRequestId') ?? '';
  const isRealTimeDataReady = get(selectedRequest, 'isDataReady', false);
  const isUseHistoricalData = useIsUseHistoricalData(lineId, 'LINE');

  const isRealtimeRequesting = get(selectedRequest, 'isRealtimeRequesting', false);

  const {
    data: insightHistoricalRes,
    isLoading: isInsightLoading,
    isError: isInsightError,
  } = useQueryGetInsight(
    {
      lineId,
      startDate: getUnixTime(startDate) || 0,
      endDate: getUnixTime(endDate) || undefined,
    },
    { enabled: !!open && Boolean(lineId) && Boolean(startDate) && isUseHistoricalData, staleTime: Infinity },
  );

  const {
    data: realtimeResponse,
    isFetching: isRealtimeInsightLoading,
    isError: isRealtimeInsightError,
  } = useGetRealtimeInsight(
    {
      lineId,
      startDate: getUnixTime(startDate) || 0,
      endDate: getUnixTime(endDate) || 0,
      realtimeRequestId,
    },
    {
      enabled:
        !!open &&
        isRealTimeDataReady &&
        Boolean(startDate) &&
        Boolean(lineId) &&
        Boolean(realtimeRequestId) &&
        !isUseHistoricalData,
      staleTime: Infinity,
    },
  );

  const isRequestingRealtime = isRealtimeRequesting || isRealtimeInsightLoading;
  const insightsData = isUseHistoricalData ? insightHistoricalRes : realtimeResponse;

  const insights = useMemo(() => {
    const data: IInsight[] = get(insightsData, 'data.insights') || [];
    return sortDataInsight(data);
  }, [insightsData]);

  const filteredData = useMemo(() => {
    const result = insights.filter((item) => {
      const filterByDeviceId =
        deviceId && deviceId !== ALL_VALUE_OPTION ? item.relatedDeviceIds?.includes(deviceId) : true;
      const filterByCategory =
        category && category !== ALL_VALUE_OPTION
          ? category === SliceText.CLIENTS
            ? conditionFilteringForClient(item, lineId)
            : category === SliceText.SERVICES
              ? item.advanced?.wheelType.toLowerCase() === wheelTypeMap[0].toLowerCase()
              : item.advanced?.wheelType === category
          : true;
      const filterByType = type && type !== ALL_VALUE_OPTION ? item.type === type : true;
      const filterByStationMac = stationMac && stationMac !== ALL_VALUE_OPTION ? item.stationMac === stationMac : true;
      const filterBySearch = searchText
        ? item.insightName.toLowerCase().includes(searchText.toLowerCase()) ||
          item.diagnostic.toLowerCase().includes(searchText.toLowerCase())
        : true;
      return filterByDeviceId && filterByCategory && filterByType && filterByStationMac && filterBySearch;
    });
    return sortDataInsight(result, sortTime);
  }, [insights, lineId, searchText, deviceId, category, stationMac, type, sortTime]);

  return {
    filteredData,
    data: insightsData,
    isLoading: isInsightLoading || isRequestingRealtime,
    isError: isInsightError || isRealtimeInsightError,
  };
};
