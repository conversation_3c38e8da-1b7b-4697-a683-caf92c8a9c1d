import { EInsightPriority, priorityLabelMap } from '@/components/Insights/insight.enum';
import { ArrowUp, ArrowUp2, ArrowUp3 } from 'ui/UIAssets';

interface IProps {
  priority: string;
}

const InsightsPriority = ({ priority }: IProps) => {
  const mapPriorityToArrow = () => {
    switch (priority) {
      case EInsightPriority.HIGH:
        return <ArrowUp3 className='text-content-meta-red' />;
      case EInsightPriority.MODERATE:
        return <ArrowUp2 className='text-content-meta-orange' />;
      case EInsightPriority.LOW:
        return <ArrowUp className='text-content-meta-yellow' />;
      default:
        return '';
    }
  };
  return (
    <>
      <div className='bg-content-meta-red/20 rounded-2xs p-1'>{mapPriorityToArrow()}</div>
      <p className='text-content-primary font-book text-xs'>{priorityLabelMap[priority]}</p>
    </>
  );
};

export default InsightsPriority;
