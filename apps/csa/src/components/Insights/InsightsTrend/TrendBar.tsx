import { cn } from '@/utils';

interface TrendBarProps {
  classes?: {
    container?: string;
    square?: string;
  };
  onClick?: (e) => void;
  text?: string;
}
export default function TrendBar({ classes, onClick, text }: TrendBarProps) {
  return (
    <div
      className={cn('relative flex min-h-10 flex-1 flex-col items-center', classes?.container)}
      data-testid='trend-bar'>
      <span
        className={cn(
          'border-gradient-border bg-surface-action--hover size-4 cursor-pointer rounded-[3px] border border-solid',
          classes?.square,
        )}
        onClick={(e) => {
          e.stopPropagation();
          onClick?.(e);
        }}
      />
      <span className='text-2xs text-content-tertiary absolute top-3 mt-2 h-4 whitespace-nowrap text-center font-medium'>
        {text ?? ''}
      </span>
    </div>
  );
}
