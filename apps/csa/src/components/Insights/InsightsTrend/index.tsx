import { IInsight, trendingBarClassMap } from '@/components/Insights/insight.type';
import { cn } from '@/utils';
import { AxonTooltipWrapper } from 'ui/UIComponents';
import { formatDate } from 'services/Utils';
import { useTheme } from 'ui/UIProviders';

interface IProps {
  card: IInsight;
}

export const InsightsTrend = ({ card }: IProps) => {
  const { theme } = useTheme();
  const isDarkMode = theme === 'dark';

  const { type, trend } = card;
  const dateFormatted = (date: number) => formatDate(date, 'D MMM');

  return (
    <div className='flex flex-1 items-center justify-end gap-1'>
      {trend &&
        trend.map((bar, index) => (
          <AxonTooltipWrapper
            key={index}
            label={
              <div
                key={index}
                className={cn(
                  trendingBarClassMap[type],
                  'rounded-2xs h-[12px] w-[6px]',
                  !bar.status && `${isDarkMode ? 'opacity-20' : 'opacity-30'}`,
                )}
              />
            }
            content={<p>{dateFormatted(bar.date)}</p>}
          />
        ))}
    </div>
  );
};
