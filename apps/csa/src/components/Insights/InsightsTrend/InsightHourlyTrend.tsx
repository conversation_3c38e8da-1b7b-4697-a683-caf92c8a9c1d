import {
  AxonPopover,
  AxonPopoverAnchor,
  AxonPopoverContent,
  AxonTooltip,
  AxonTooltipContent,
  AxonTooltipPortal,
  AxonTooltipProvider,
  AxonTooltipTrigger,
} from 'ui/UIComponents';
import React, { useRef } from 'react';
import TrendBar from '@/components/Insights/InsightsTrend/TrendBar';
import { today } from 'services/Utils';
import { useTranslation } from 'react-i18next';

export type MeasurableElement = {
  getBoundingClientRect: () => DOMRect;
};
export type InsightHourlyTrendData = {
  date: string;
  hours: { hour: number; status: boolean }[];
};
interface InsightHourlyTrendProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  triggerNode: MeasurableElement | null;
  data: InsightHourlyTrendData | null;
  classes?: {
    trendBar?: string;
    bgHover?: string;
  };
  onOpenChart: (open: boolean, hour: number) => void;
}
export default function InsightHourlyTrend({
  open,
  onOpenChange,
  triggerNode,
  data,
  classes,
  onOpenChart,
}: InsightHourlyTrendProps) {
  const { t } = useTranslation();
  const virtualRef = useRef<MeasurableElement | null>(null);
  if (triggerNode) {
    const rect = triggerNode.getBoundingClientRect();
    const xOffset = 270;
    const yOffset = 0;

    virtualRef.current = {
      getBoundingClientRect: () => ({
        x: rect.x + xOffset,
        y: rect.y + yOffset,
        width: rect.width,
        height: rect.height,
        top: rect.top + yOffset,
        left: rect.left + xOffset,
        right: rect.right + xOffset,
        bottom: rect.bottom + yOffset,
        toJSON: () => {},
      }),
    };
  }

  const checkNow = (hour: number) => {
    const currentDate = today('D MMM');
    const currentTime = Number(today('HH'));
    return currentDate === data?.date && hour === currentTime;
  };
  return (
    <AxonPopover open={open} onOpenChange={onOpenChange}>
      {virtualRef && <AxonPopoverAnchor virtualRef={virtualRef}></AxonPopoverAnchor>}
      <AxonPopoverContent
        id='insight-hourly-popover'
        role='dialog'
        className='p-sm w-fit pb-1'
        side='bottom'
        aria-labelledby='insight-hourly-popover-title'
        aria-describedby='insight-hourly-popover-description'
        onInteractOutside={(e) => {
          if ((e.target as HTMLElement).getAttribute('data-testid') === 'insight-hourly-chart-popover') {
            e.preventDefault();
          }
        }}>
        <div className='flex flex-row items-start gap-1'>
          <span className='text-2xs text-content-secondary mr-2 overflow-hidden text-ellipsis font-medium'>
            {data?.date}
          </span>
          {data &&
            data.hours.map((h, index) => (
              <AxonTooltipProvider delayDuration={100} key={index}>
                <AxonTooltip>
                  <AxonTooltipTrigger asChild>
                    <div className='flex items-start gap-1'>
                      <TrendBar
                        classes={{
                          square: `${h.status ? classes?.trendBar : ''} ${classes?.bgHover}`,
                        }}
                        onClick={() => onOpenChart(true, h.hour)}
                        text={
                          checkNow(h.hour)
                            ? t('datetime.now')
                            : index % 6 === 0
                              ? `${h.hour < 10 ? `0${h.hour}` : h.hour}`
                              : undefined
                        }
                      />
                      {(index + 1) % 6 === 0 && index !== data.hours.length - 1 && (
                        <span className='border-border-flat mx-0.5 h-4 border-l'></span>
                      )}
                    </div>
                  </AxonTooltipTrigger>
                  <AxonTooltipPortal>
                    <AxonTooltipContent>
                      <p>{h.hour < 10 ? `0${h.hour}` : h.hour}</p>
                    </AxonTooltipContent>
                  </AxonTooltipPortal>
                </AxonTooltip>
              </AxonTooltipProvider>
            ))}
        </div>
      </AxonPopoverContent>
    </AxonPopover>
  );
}
