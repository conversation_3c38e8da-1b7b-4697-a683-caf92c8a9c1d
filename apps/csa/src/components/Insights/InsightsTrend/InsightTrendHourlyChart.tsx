import { AxonL<PERSON><PERSON>hart, AxonPopover, AxonPopoverAnchor, AxonPopoverContent } from 'ui/UIComponents';
import React, { memo, useRef } from 'react';
import { DATETIME_FORMAT, formatDate } from 'services/Utils';
import get from 'lodash/get';
import { MeasurableElement } from '@/components/Insights/InsightsTrend/InsightHourlyTrend';

interface InsightHourlyChartProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  triggerNode: MeasurableElement | null;
  hour: number;
}

const chartData = [
  {
    date: 1759392000000,
    STA_LAT_1: 13,
  },
  {
    date: 1759392060000,
    STA_LAT_1: 12,
  },
  {
    date: 1759392120000,
    STA_LAT_1: 4,
  },
  {
    date: 1759392180000,
    STA_LAT_1: 1,
  },
  {
    date: 1759392240000,
    STA_LAT_1: 4,
  },
  {
    date: 1759392300000,
    STA_LAT_1: 6,
  },
  {
    date: 1759392360000,
    STA_LAT_1: 10,
  },
  {
    date: 1759392420000,
    STA_LAT_1: 12,
  },
  {
    date: 1759392480000,
    STA_LAT_1: 8,
  },
  {
    date: 1759392540000,
    STA_LAT_1: 1,
  },
  {
    date: 1759392600000,
    STA_LAT_1: 12,
  },
  {
    date: 1759392660000,
    STA_LAT_1: 11,
  },
  {
    date: 1759392720000,
    STA_LAT_1: 1,
  },
  {
    date: 1759392780000,
    STA_LAT_1: 10,
  },
  {
    date: 1759392840000,
    STA_LAT_1: 9,
  },
  {
    date: 1759392900000,
    STA_LAT_1: 1,
  },
  {
    date: 1759392960000,
    STA_LAT_1: 0,
  },
  {
    date: 1759393020000,
    STA_LAT_1: 1,
  },
  {
    date: 1759393080000,
    STA_LAT_1: 12,
  },
  {
    date: 1759393140000,
    STA_LAT_1: 7,
  },
  {
    date: 1759393200000,
    STA_LAT_1: 14,
  },
  {
    date: 1759393260000,
    STA_LAT_1: 12,
  },
  {
    date: 1759393320000,
    STA_LAT_1: 2,
  },
  {
    date: 1759393380000,
    STA_LAT_1: 2,
  },
  {
    date: 1759393440000,
    STA_LAT_1: 5,
  },
  {
    date: 1759393500000,
    STA_LAT_1: 10,
  },
  {
    date: 1759393560000,
    STA_LAT_1: 9,
  },
  {
    date: 1759393620000,
    STA_LAT_1: 9,
  },
  {
    date: 1759393680000,
    STA_LAT_1: 11,
  },
  {
    date: 1759393740000,
    STA_LAT_1: 8,
  },
  {
    date: 1759393800000,
    STA_LAT_1: 12,
  },
  {
    date: 1759393860000,
    STA_LAT_1: 2,
  },
  {
    date: 1759393920000,
    STA_LAT_1: 4,
  },
  {
    date: 1759393980000,
    STA_LAT_1: 5,
  },
  {
    date: 1759394040000,
    STA_LAT_1: 2,
  },
  {
    date: 1759394100000,
    STA_LAT_1: 6,
  },
  {
    date: 1759394160000,
    STA_LAT_1: 3,
  },
  {
    date: 1759394220000,
    STA_LAT_1: 13,
  },
  {
    date: 1759394280000,
    STA_LAT_1: 5,
  },
  {
    date: 1759394340000,
    STA_LAT_1: 9,
  },
  {
    date: 1759394400000,
    STA_LAT_1: 8,
  },
  {
    date: 1759394460000,
    STA_LAT_1: 10,
  },
  {
    date: 1759394520000,
    STA_LAT_1: 7,
  },
  {
    date: 1759394580000,
    STA_LAT_1: 6,
  },
  {
    date: 1759394640000,
    STA_LAT_1: 12,
  },
  {
    date: 1759394700000,
    STA_LAT_1: 11,
  },
  {
    date: 1759394760000,
    STA_LAT_1: 7,
  },
  {
    date: 1759394820000,
    STA_LAT_1: 5,
  },
  {
    date: 1759394880000,
    STA_LAT_1: 4,
  },
  {
    date: 1759394940000,
    STA_LAT_1: 6,
  },
  {
    date: 1759395000000,
    STA_LAT_1: 4,
  },
  {
    date: 1759395060000,
    STA_LAT_1: 11,
  },
  {
    date: 1759395120000,
    STA_LAT_1: 0,
  },
  {
    date: 1759395180000,
    STA_LAT_1: 13,
  },
  {
    date: 1759395240000,
    STA_LAT_1: 5,
  },
  {
    date: 1759395300000,
    STA_LAT_1: 2,
  },
  {
    date: 1759395360000,
    STA_LAT_1: 9,
  },
  {
    date: 1759395420000,
    STA_LAT_1: 7,
  },
  {
    date: 1759395480000,
    STA_LAT_1: 4,
  },
  {
    date: 1759395540000,
    STA_LAT_1: 10,
  },
];
const InsightTrendHourlyChart = ({ open, onOpenChange, triggerNode, hour }: InsightHourlyChartProps) => {
  const data = chartData.map((d, i) => ({
    date: d.date,
    minute: i,
    STA_LAT_1: d.STA_LAT_1,
  }));

  const hourString = (h: number) => h.toString().padStart(2, '0');
  const Tick = memo(({ payload, x, y }: any) => {
    const val = payload.value;
    return (
      <g transform={`translate(${x},${y})`}>
        <text x={0} y={0} dy={16} textAnchor='end' fill='#666'>
          {hourString(hour)}:{hourString(val)}
        </text>
      </g>
    );
  });
  const virtualRef = useRef<MeasurableElement | null>(null);
  if (triggerNode) {
    const rect = triggerNode.getBoundingClientRect();
    const xOffset = 270;
    const yOffset = 0;

    virtualRef.current = {
      getBoundingClientRect: () => ({
        x: rect.x + xOffset,
        y: rect.y + yOffset,
        width: rect.width,
        height: rect.height,
        top: rect.top + yOffset,
        left: rect.left + xOffset,
        right: rect.right + xOffset,
        bottom: rect.bottom + yOffset,
        toJSON: () => {},
      }),
    };
  }

  return (
    <AxonPopover open={open} onOpenChange={onOpenChange} modal={false}>
      {virtualRef && <AxonPopoverAnchor virtualRef={virtualRef}></AxonPopoverAnchor>}
      <AxonPopoverContent
        id='insight-hourly-chart-popover'
        role='dialog'
        className='p-sm w-fit pb-1'
        side='bottom'
        aria-labelledby='insight-hourly-chart-popover-title'
        aria-describedby='insight-hourly-chart-popover-description'
        data-testid='insight-hourly-chart-popover'>
        <div className='h-[350px] p-2'>
          <AxonLineChart
            chartProps={{ data }}
            linesProps={[
              {
                dataKey: 'STA_LAT_1',
                name: 'STA_LAT_1',
                color: 'rgb(var(--content-meta-red))',
              },
            ]}
            xAxisDateTime={false}
            xAxisProps={{ dataKey: 'minute', domain: [0, 59], ticks: [0, 15, 30, 45, 60], tick: <Tick /> }}
            tooltipProps={{
              labelFormatter: (_, payload) => {
                return formatDate(get(payload, '0.payload.date'), DATETIME_FORMAT.DATE_TIME);
              },
            }}
          />
        </div>
      </AxonPopoverContent>
    </AxonPopover>
  );
};

export default InsightTrendHourlyChart;
