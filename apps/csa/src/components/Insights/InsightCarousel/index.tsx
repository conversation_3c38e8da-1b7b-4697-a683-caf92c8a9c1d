'use client';

import { forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import { AnimatePresence, motion, usePresenceData } from 'framer-motion';
import { IInsight } from '../insight.type';
import InsightsCard from '@/components/Insights/InsightsCard';
import { AxonButton } from 'ui/UIComponents';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { DetailDialog } from '@/components/Insights/InsightsDrawer/detailDialog';
import { useKeyDown } from 'services/Hooks';

export interface InsightsCarouselRef {
  next: () => void;
  back: () => void;
  openDetail: () => void;
}

interface Props {
  insights: IInsight[];
}

const InsightsCarousel = forwardRef<InsightsCarouselRef, Props>(({ insights }, ref) => {
  const [index, setIndex] = useState(0);
  const [direction, setDirection] = useState<1 | -1>(1);
  const [shuffleSignal, setShuffleSignal] = useState(0);
  const [isHover, setIsHover] = useState<boolean>(false);
  const [isDetailOpen, setIsDetailOpen] = useState(false);

  useEffect(() => {
    setIndex(0);
  }, [insights]);

  const isValidIndex = index >= 0 && index < insights.length;
  const current = isValidIndex ? insights[index] : insights[0];
  const checkNext = index < insights.length - 1;
  const checkBack = index > 0;

  const handleNext = () => {
    if (checkNext) {
      setDirection(-1);
      setIndex((prev) => prev + 1);
      setShuffleSignal((s) => s + 1);
    }
  };

  const handleBack = () => {
    if (checkBack) {
      setDirection(1);
      setIndex((prev) => prev - 1);
      setShuffleSignal((s) => s + 1);
    }
  };

  useImperativeHandle(ref, () => ({
    next: handleNext,
    back: handleBack,
    openDetail: () => setIsDetailOpen(true),
  }));

  useKeyDown([
    [
      'ctrl+Enter',
      (e) => {
        e.preventDefault();
        setIsDetailOpen(true);
      },
    ],
    [
      'ctrl+arrowleft',
      (e) => {
        e.preventDefault();
        handleBack();
      },
    ],
    [
      'ctrl+arrowright',
      (e) => {
        e.preventDefault();
        handleNext();
      },
    ],
    [
      'command+arrowleft',
      (e) => {
        e.preventDefault();
        handleBack();
      },
    ],
    [
      'command+arrowright',
      (e) => {
        e.preventDefault();
        handleNext();
      },
    ],
  ]);

  return (
    <div
      className='relative h-[360px] w-full'
      onMouseEnter={() => setIsHover(true)}
      onMouseLeave={() => setIsHover(false)}>
      {index < insights.length - 2 && (
        <motion.div
          key={`bottom-shadow-${shuffleSignal}`}
          initial={{ scale: 0.9, opacity: 0.3 }}
          animate={{ scale: 0.92, opacity: 0.5 }}
          transition={{ duration: 0.5 }}
          className='bg-component-alert-tile-bg border-gradient-border 3xl:-left-6 absolute -left-8 top-6 h-[296px] w-full scale-x-[85%] rounded-md mix-blend-luminosity'></motion.div>
      )}

      {index < insights.length - 1 && (
        <motion.div
          key={`middle-shadow-${shuffleSignal}`}
          initial={{ scale: 0.93, opacity: 0.4 }}
          animate={{ scale: 0.96, opacity: 0.7 }}
          transition={{ duration: 0.5 }}
          className='bg-component-rcmd-tile-bg border-gradient-border 3xl:-left-3 absolute -left-4 top-3 h-[320px] w-full scale-x-[85%] rounded-md mix-blend-luminosity'></motion.div>
      )}
      <AnimatePresence custom={direction} initial={false} mode='popLayout'>
        <Slide key={`${current?.insightCode}-${shuffleSignal}`} data={current} />
      </AnimatePresence>
      <DetailDialog data={current} open={isDetailOpen} setOpen={setIsDetailOpen} showTriggerBtn={false} />
      {isHover && (
        <>
          <div className='absolute -right-4 bottom-1/2 z-50'>
            <AxonButton
              variant='outline'
              size='icon'
              onClick={handleNext}
              className='rounded-full'
              disabled={!checkNext}>
              <ChevronRight size={16} />
            </AxonButton>
          </div>
          <div className='absolute -left-4 bottom-1/2 z-50'>
            <AxonButton
              variant='outline'
              size='icon'
              onClick={handleBack}
              className='rounded-full'
              disabled={!checkBack}>
              <ChevronLeft size={16} />
            </AxonButton>
          </div>
        </>
      )}
    </div>
  );
});

const Slide = ({ data }: { data: IInsight }) => {
  const direction = usePresenceData();

  return (
    <motion.div
      initial={{ opacity: 0, x: direction * 40, scale: 0.85 }}
      animate={{
        opacity: 1,
        x: 0,
        scale: 1,
        transition: {
          type: 'spring',
          bounce: 0.5,
          visualDuration: 0.5,
        },
      }}
      exit={{ opacity: 0, x: direction * -40, scale: 0.85 }}
      className='absolute w-full'>
      <InsightsCard data={data} />
    </motion.div>
  );
};
export default InsightsCarousel;
