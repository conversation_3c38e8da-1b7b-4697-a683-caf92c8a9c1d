import Markdown from 'markdown-to-jsx';
import { PropsWithChildren } from 'react';

const MarkdownToJsx = ({ content }: { content: string }) => {
  const components = {
    h2: {
      component: (props: PropsWithChildren) => <h2 {...props} className='mb-1 mt-2 text-lg font-medium' />,
    },
    h3: {
      component: (props: PropsWithChildren) => <h3 {...props} className='text-md mb-1 mt-2 font-medium' />,
    },
    h4: {
      component: (props: PropsWithChildren) => <h4 {...props} className='mb-1 mt-2 font-medium' />,
    },
    p: {
      component: (props: PropsWithChildren) => <p {...props} className='mb-1' />,
    },
    a: {
      component: (props: PropsWithChildren) => (
        <a {...props} className='text-accent underline' target='_blank' rel='noopener noreferrer' />
      ),
    },
    ul: {
      component: (props: PropsWithChildren) => <ul {...props} className='mb-1 list-disc space-y-1 pl-4' />,
    },
    ol: {
      component: (props: PropsWithChildren) => <ol {...props} className='mb-1 list-decimal space-y-1 pl-4' />,
    },
    li: {
      component: (props: PropsWithChildren) => <li {...props} className='mb-1' />,
    },
    code: {
      component: (props: PropsWithChildren) => <code {...props} className='rounded px-1.5 py-0.5 font-mono text-sm' />,
    },
    pre: {
      component: (props: PropsWithChildren) => <pre {...props} className='mb-1 overflow-x-auto rounded p-4' />,
    },
    blockquote: {
      component: (props: PropsWithChildren) => <blockquote {...props} className='mb-1 border-l-4 pl-4 italic' />,
    },
  };

  return <Markdown options={{ overrides: components, forceBlock: true }}>{content}</Markdown>;
};

export default MarkdownToJsx;
