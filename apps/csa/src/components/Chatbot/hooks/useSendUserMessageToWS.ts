import { useChatbotStore, useCurrentTabChatbotUrl } from '../chatbotStore';
import { getWsMessage } from '../utils';
import { useChatbotWs } from './useChatbotWs';

export const useSendUserMessageToWS = () => {
  const chatbotUrl = useCurrentTabChatbotUrl();
  const { sendJsonMessage } = useChatbotWs(chatbotUrl);
  const currentTabKey = useChatbotStore.use.currentTabKey();
  const sendChatbotMessage = useChatbotStore.use.sendMessageForTab();
  const sendLoadingMessage = useChatbotStore.use.sendLoadingMessageForTab();

  return (content: string) => {
    if (!currentTabKey) return;
    sendChatbotMessage(currentTabKey, { owner: 'user', content });
    sendLoadingMessage(currentTabKey);

    if (sendJsonMessage) {
      sendJsonMessage(getWsMessage(content));
    }
  };
};
