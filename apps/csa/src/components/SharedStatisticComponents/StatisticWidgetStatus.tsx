import { CpeStatusIcon } from '@/pages/Device/CpeStatistics/icons/CpeStatusIcon';
import { cn } from '@/utils';
import { EQoeStatus } from '@/utils/QOE.util';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useInterval } from 'services/Hooks';
import { getRelativeTimeFromNow } from 'services/Utils';

interface StatisticWidgetStatusProps extends React.HTMLAttributes<HTMLDivElement> {
  status: EQoeStatus;
  lastUpdated?: number;
  lastBootupTime?: number;
}

export const StatisticWidgetStatus = ({
  status,
  lastBootupTime,
  lastUpdated,
  ...props
}: StatisticWidgetStatusProps) => {
  const { t } = useTranslation();
  const [count, setCount] = useState(0); // trigger re-render after 1 minute
  useInterval(
    () => {
      setCount(count + 1);
    },
    lastUpdated ? 60000 : null,
  );

  const testIdPrefix = props['data-testid'] ?? 'device-statistics-widget-status';

  if (status !== EQoeStatus.UNKNOWN || lastUpdated || lastBootupTime) {
    return (
      <div
        className='flex w-full items-center justify-start gap-1'
        data-testid={`${testIdPrefix}-container`}
        {...props}>
        <CpeStatusIcon
          className={cn(`text-content-disabled`, {
            'text-content-meta-green': status === EQoeStatus.STABLE,
            'text-content-meta-yellow': status === EQoeStatus.UNSTABLE,
            'text-content-meta-red': status === EQoeStatus.VERY_UNSTABLE,
          })}
        />
        <span className='flex items-center gap-1 text-xs'>
          <span
            className={cn(`text-content-disabled font-medium`, {
              'text-content-meta-green': status === EQoeStatus.STABLE,
              'text-content-meta-yellow': status === EQoeStatus.UNSTABLE,
              'text-content-meta-red': status === EQoeStatus.VERY_UNSTABLE,
            })}
            data-testid={`${testIdPrefix}-status`}>
            {t(status)}
          </span>
          <span className='font-book text-content-primary opacity-60' data-testid={`${testIdPrefix}-last-updated`}>
            {t('since')} {lastUpdated ? getRelativeTimeFromNow(lastUpdated) : t('unknown')}
          </span>
          {lastBootupTime && (
            <span className='font-book text-content-primary opacity-60' data-testid={`${testIdPrefix}-last-bootup`}>
              ({t('activeSince')} {getRelativeTimeFromNow(lastBootupTime)})
            </span>
          )}
        </span>
      </div>
    );
  }

  return (
    <div className='flex w-full items-center justify-start gap-1' data-testid={`${testIdPrefix}-no-status`} {...props}>
      <CpeStatusIcon className='text-content-disabled' />
      <p className='font-book text-content-tertiary'>{t('noStatusDataAvailable')}</p>
    </div>
  );
};
