export interface LogProps {
  logs: EventLog[];
}

// Common fields for all logs
export interface BaseLog {
  category: string | null;
  source: string;
  deviceType: string | null;
  eventType: EventType;
  cpeId: string;
  timeStamp: number;
  color: 'success' | 'info' | 'danger';
}
export type LogsResponse = {
  logs: EventLog[];
  sources: string[];
  eventTypes: string[];
  cpeIds: string[];
};

// EventType union
export type EventType =
  | 'KeepAliveDown'
  | 'AgentRestart'
  | 'InternetUp'
  | 'InternetDown'
  | 'WifiInterfaceStatusChanged'
  | 'ChannelChangeByWiFiDriverCompleted'
  | 'WanChange'
  | 'InvalidL3Fixed'
  | 'InvalidL3'
  | 'KeepAliveUp'
  | 'WanUp'
  | 'WanDown';

export interface WanUpLog extends BaseLog {
  eventType: 'WanUp';
  detail: { linkDown: boolean };
}

export interface WanDownLog extends BaseLog {
  eventType: 'WanDown';
  detail: { linkDown: boolean } | null;
}

export interface KeepAliveDownLog extends BaseLog {
  eventType: 'KeepAliveDown';
  detail: null;
}

export interface KeepAliveUpLog extends BaseLog {
  eventType: 'KeepAliveUp';
  detail: null;
}

export interface AgentRestartLog extends BaseLog {
  eventType: 'AgentRestart';
  detail: {
    hardwareVersionString: string;
    currentSoftwareVersion: string;
    cpeFirmwareVersion: string;
    platform: string;
    agentRestartCause: string;
  };
}

export interface InternetUpLog extends BaseLog {
  eventType: 'InternetUp';
  detail: null;
}

export interface InternetDownLog extends BaseLog {
  eventType: 'InternetDown';
  detail: null;
}

export interface WifiInterfaceStatusChangedLog extends BaseLog {
  eventType: 'WifiInterfaceStatusChanged';
  detail: {
    interface: string;
    ifaceDown: boolean;
  };
}

export interface ChannelChangeByWiFiDriverCompletedLog extends BaseLog {
  eventType: 'ChannelChangeByWiFiDriverCompleted';
  detail: {
    interface: string;
    currentChannel: {
      primary: number;
      bandwidth: number;
      autoBandwidth: boolean;
    };
    targetChannel: {
      primary: number;
      bandwidth: number;
      autoBandwidth: boolean;
    };
  };
}

export interface WanChangeLog extends BaseLog {
  eventType: 'WanChange';
  detail: {
    wanIpAddress: string;
    wanNetworkMask: string;
    gatewayIpAddress: string;
    wanMacAddress: string;
    previousWanIpAddress: string;
    previousWanNetworkMask: string;
    previousGatewayIpAddress: string;
    previousWanMacAddress: string;
  };
}

export interface InvalidL3FixedLog extends BaseLog {
  eventType: 'InvalidL3Fixed';
  detail: {
    stationMacAddress: string;
    stationIpAddress: string;
    lanAddress: string;
    lanNetmask: string;
    elapsedSeconds: number;
    fixed: boolean;
  };
}

export interface InvalidL3Log extends BaseLog {
  eventType: 'InvalidL3';
  detail: {
    stationMacAddress: string;
    stationIpAddress: string;
    lanAddress: string;
    lanNetmask: string;
  };
}

// Union type for all event logs
export type EventLog =
  | BaseLog
  | KeepAliveDownLog
  | KeepAliveUpLog
  | AgentRestartLog
  | InternetUpLog
  | InternetDownLog
  | WifiInterfaceStatusChangedLog
  | ChannelChangeByWiFiDriverCompletedLog
  | WanChangeLog
  | InvalidL3FixedLog
  | InvalidL3Log;
