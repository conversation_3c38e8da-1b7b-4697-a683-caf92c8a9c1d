import { Fragment } from 'react';
import { AxonSeparator, AxonSkeletonLoader } from 'ui/UIComponents';

const LoadingScreen = () => {
  return (
    <>
      {[1, 2, 3].map((v) => (
        <Fragment key={v}>
          <div className='flex flex-row items-start gap-x-3'>
            <AxonSkeletonLoader className='size-[20px]' />
            <div className='flex flex-col gap-y-1'>
              <AxonSkeletonLoader className='h-[18px] w-[250px]' />
              <AxonSkeletonLoader className='h-[15px] w-[150px]' />
            </div>
          </div>
          {v !== 3 && <AxonSeparator />}
        </Fragment>
      ))}
    </>
  );
};

export default LoadingScreen;
