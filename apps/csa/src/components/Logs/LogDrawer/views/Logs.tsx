import { useTranslation } from 'react-i18next';
import { DATETIME_FORMAT, formatDate } from 'services/Utils';
import { getDeviceImage, router } from 'ui/UIAssets';
import {
  AxonCard,
  AxonImage,
  AxonTooltip,
  AxonTooltipContent,
  AxonTooltipProvider,
  AxonTooltipTrigger,
} from 'ui/UIComponents';
import { useTheme } from 'ui/UIProviders';
import { useVirtualizer } from '@tanstack/react-virtual';
import { useRef } from 'react';
import { LogProps } from '../../logs.type';

export const Logs = ({ logs }: LogProps) => {
  const { t } = useTranslation();

  const { theme } = useTheme();
  const isDarkmode = theme === 'dark';

  const translatedLogs = logs.map((log) => ({
    ...log,
    eventType: t(`logs.eventTypes.${log.eventType}`, { defaultValue: log.eventType }),
    description: t(`logs.eventDescriptions.${log.eventType}`, { defaultValue: log.eventType }),
  }));

  const parentRef = useRef<HTMLDivElement>(null);
  const itemsPerRow = 4;
  const rows = Math.ceil(translatedLogs.length / itemsPerRow);

  const rowVirtualizer = useVirtualizer({
    count: rows,
    getScrollElement: () => parentRef.current,
    estimateSize: () => 340,
    overscan: 1,
  });

  return (
    <div ref={parentRef} className='scrollbar-lg h-full overflow-auto p-6' style={{ position: 'relative' }}>
      <div
        style={{
          height: `${rowVirtualizer.getTotalSize()}px`,
          position: 'relative',
          width: '100%',
        }}>
        {rowVirtualizer.getVirtualItems().map((virtualRow) => {
          const startIndex = virtualRow.index * itemsPerRow;
          const endIndex = Math.min(startIndex + itemsPerRow, translatedLogs.length);
          const rowLogs = translatedLogs.slice(startIndex, endIndex);

          return (
            <div
              key={virtualRow.key}
              style={{
                position: 'absolute',
                top: 0,
                left: 0,
                width: '100%',
                height: `${virtualRow.size}px`,
                transform: `translateY(${virtualRow.start}px)`,
              }}
              className='grid grid-cols-4 gap-4'>
              {rowLogs.map((item, i) => (
                <AxonCard
                  key={`${item.timeStamp}-${item.eventType}-${i}`}
                  className='relative h-[328px]'
                  style={{
                    clipPath: 'polygon(0 0, 100% 0, 100% 87%, 88% 100%, 0 100%)',
                  }}>
                  <span className='absolute bottom-0 right-0 origin-bottom-right'>
                    <svg xmlns='http://www.w3.org/2000/svg' width='37' height='40' viewBox='0 0 37 40' fill='none'>
                      <path
                        d='M0 8.88889C0 3.97969 3.68121 0 8.22222 0H37V3.10219C37 3.35766 36.9022 3.60344 36.7267 3.7891L2.5 40H0V8.88889Z'
                        fill={`url(#paint0_linear_${i}-${virtualRow.index})`}
                      />
                      <defs>
                        <linearGradient
                          id={`paint0_linear_${i}-${virtualRow.index}`}
                          x1='18.5'
                          y1='0'
                          x2='18.5'
                          y2='40'
                          gradientUnits='userSpaceOnUse'>
                          <stop offset='0.4' stopColor={isDarkmode ? '#fff' : '#000'} stopOpacity='0.16' />
                          <stop offset='1' stopColor={isDarkmode ? '#fff' : '#000'} stopOpacity='0.02' />
                        </linearGradient>
                      </defs>
                    </svg>
                  </span>
                  <div className='flex h-full flex-col gap-y-4 p-6'>
                    <div className='header-card flex flex-row items-center'>
                      <div className='border-gradient-border bg-component-pill-babyblue rounded-xl border px-3 py-1'>
                        <p className='font-book text-content-primary text-sm'>{t('logs.event')}</p>
                      </div>
                      <div className='ml-auto flex items-center gap-x-2'>
                        <AxonTooltipProvider>
                          <AxonTooltip>
                            <AxonTooltipTrigger>
                              <p className='text-content-tertiary w-40 truncate text-right'>
                                {item.source || item.cpeId || ''}
                              </p>
                            </AxonTooltipTrigger>
                            <AxonTooltipContent>
                              <p className='text-content-tertiary'>{item.source || item.cpeId || ''}</p>
                            </AxonTooltipContent>
                          </AxonTooltip>
                        </AxonTooltipProvider>
                        <AxonImage
                          src={item.deviceType ? getDeviceImage(item.deviceType) : router}
                          alt='Event Image'
                          size='xs'
                          className='p-1'
                        />
                      </div>
                    </div>
                    <div className='flex flex-1 flex-col gap-y-2'>
                      <p className='text-content-primary text-xl font-medium'>{item.eventType}</p>
                      <p className='text-content-primary scrollbar-sm h-16 overflow-auto opacity-75' tabIndex={0}>
                        {item.description}
                      </p>
                    </div>
                    {/* <div className='flex flex-row items-center gap-4'>
                    <AxonButton variant='default'>Open</AxonButton>
                  </div> */}
                    <div className='mt-auto text-xs'>
                      <p className='w-4/5'>
                        <span className='text-content-secondary'>{t('logs.lastModifiedOn')} </span>
                        <span className='font-semibold'>
                          {formatDate(item.timeStamp || 0, DATETIME_FORMAT.DATE_TIME)}
                        </span>
                      </p>
                    </div>
                  </div>
                </AxonCard>
              ))}
            </div>
          );
        })}
      </div>
    </div>
  );
};
