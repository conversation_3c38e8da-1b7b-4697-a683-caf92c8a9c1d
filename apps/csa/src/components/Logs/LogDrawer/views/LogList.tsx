import {
  ColumnDef,
  getCoreRowModel,
  getExpandedRowModel,
  getSortedRowModel,
  useReactTable,
} from '@tanstack/react-table';
import { DATETIME_FORMAT, formatDate } from 'services/Utils';
import { AxonTableData } from 'ui/UIComponents';
import { useTranslation } from 'react-i18next';
import { LogProps } from '../../logs.type';

export const LogList = ({ logs }: LogProps) => {
  const { t } = useTranslation();
  const columns: ColumnDef<LogProps['logs'][number]>[] = [
    // {
    //   accessorKey: 'category',
    //   header: t('logs.category'),
    //   cell: (info) => info.getValue(),
    // },
    {
      accessorKey: 'source',
      header: t('logs.source'),
      cell: (info) => info.row.original.source || info.row.original.cpeId || '-',
    },
    {
      accessorKey: 'eventType',
      header: t('logs.event'),
      cell: (info) => t(`logs.eventTypes.${info.getValue()}`, { defaultValue: info.getValue() }),
    },
    {
      accessorKey: 'timeStamp',
      header: t('logs.occurredAt'),
      cell: (info) => {
        const value = info.getValue<LogProps['logs'][number]['timeStamp']>();
        return formatDate(value, DATETIME_FORMAT.DATE_TIME);
      },
    },
    { accessorKey: 'resolvedAt', header: t('logs.resolvedAt'), cell: (info) => info.getValue() },
  ];

  const table = useReactTable({
    data: logs,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getExpandedRowModel: getExpandedRowModel(),
    getSortedRowModel: getSortedRowModel(),
  });

  return <AxonTableData tabIndex={0} table={table} showFooter={false} />;
};
