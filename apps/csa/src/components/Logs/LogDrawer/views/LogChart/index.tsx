import { Chevron<PERSON>eft, ChevronRight, <PERSON>hai<PERSON>, ZoomIn, ZoomOut } from 'lucide-react';
import React, { FunctionComponent, SVGProps, useLayoutEffect, useRef, useState } from 'react';
import ReactDOMServer from 'react-dom/server';
import { useTranslation } from 'react-i18next';
import { formatDate, getDayjsFromDate, getTimezone } from 'services/Utils';
import { AxonButton, AxonTooltipWrapper } from 'ui/UIComponents';
import * as vis from 'vis-timeline/standalone';
import 'vis-timeline/styles/vis-timeline-graph2d.min.css';
import {
  ChannelChangeByWiFiDriverCompletedLog,
  EventType,
  InvalidL3FixedLog,
  InvalidL3Log,
  LogProps,
  WanDownLog,
  WanUpLog,
  WifiInterfaceStatusChangedLog,
} from '../../../logs.type';
import './logChart.css';
import BaseLogView from './logViews/BaseLogView';
import ChannelChangeByWifiDriverLogView from './logViews/ChannelChangeByWifiDriverLogView';
import InvalidL3LogView from './logViews/InvalidL3LogView';
import WanLogView from './logViews/WanLogView';
import WifiInterfaceStatusChangeLogView from './logViews/WifiInterfaceStatusChangeLogView';
import { useEventDrawer } from '@/stores/drawer.store';
import moment, { MomentInput } from 'moment-timezone';

export const convertReactSvgToPlainString = (component: React.ReactNode) => {
  return ReactDOMServer.renderToStaticMarkup(component);
};

interface LogChartProps extends LogProps {
  logCount: Record<string, { count: number; icon: FunctionComponent<SVGProps<SVGSVGElement>> }>;
}

const LogChart = ({ logs, logCount }: LogChartProps) => {
  const { t } = useTranslation();
  const containerRef = useRef<HTMLDivElement | null>(null);
  const timelineRef = useRef<vis.Timeline | null>(null);
  const [selectedLog, setSelectedLog] = useState<LogProps['logs'][number]>();
  const [popupPosition, setPopupPosition] = useState({ x: 0, y: 0 });
  const hoverTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const { startDate, endDate } = useEventDrawer();

  const currentLang = localStorage.getItem('lang') ?? process.env.LANGUAGE_ENV ?? 'de';

  useLayoutEffect(() => {
    if (!containerRef.current) return;

    const formattedLogs = logs.map((l, index) => {
      const Icon = logCount[l.eventType]?.icon;
      return {
        id: index,
        icon: Icon ? convertReactSvgToPlainString(<Icon className='size-4' />) : '',
        content: t(`logs.eventTypes.${l.eventType}`, { defaultValue: l.eventType }),
        start: formatDate(l.timeStamp) ?? '',
        color: l.color,
      };
    });

    const items = new vis.DataSet(formattedLogs);

    const min = startDate ? getDayjsFromDate(startDate).tz(getTimezone()).startOf('day').toDate() : undefined;
    const max = endDate ? getDayjsFromDate(endDate).tz(getTimezone()).endOf('day').add(12, 'hour').toDate() : undefined;

    const options: vis.TimelineOptions = {
      moment: function (date: MomentInput) {
        return moment(date).tz(getTimezone());
      },
      xss: {
        disabled: true,
      },
      height: '100%',
      align: 'left',
      margin: { item: 10, axis: 20 },
      zoomMin: 1000 * 60 * 60, // 1 hour
      zoomMax: 1000 * 60 * 60 * 24 * 16, // 16 days
      locale: currentLang,
      min,
      max,
      start: min,
      end: max,
      template: function (item: (typeof formattedLogs)[number]) {
        return `
        <p class="log-${item.color} log-text flex items-center gap-x-2">
          ${item.icon}
          ${item.content}
        </p>`;
      },
    };

    timelineRef.current = new vis.Timeline(containerRef.current, items, options);

    timelineRef.current.on('itemover', (props: { item: number; event: MouseEvent }) => {
      const id = props.item;
      if (!containerRef.current) return;

      if (hoverTimeoutRef.current) {
        clearTimeout(hoverTimeoutRef.current);
      }

      hoverTimeoutRef.current = setTimeout(() => {
        const containerRect = containerRef.current?.getBoundingClientRect();
        if (!containerRect) return;

        setSelectedLog(logs[id]);
        setPopupPosition({
          x: props.event.clientX - containerRect.left,
          y: props.event.clientY - containerRect.top,
        });
      }, 200); // delay time
    });

    timelineRef.current.on('itemout', () => {
      if (hoverTimeoutRef.current) {
        clearTimeout(hoverTimeoutRef.current);
        hoverTimeoutRef.current = null;
      }
      setSelectedLog(undefined);
    });

    return () => {
      timelineRef.current?.destroy();
    };
  }, [logs, currentLang, logCount, t, startDate, endDate]);

  const timelineFit = () => {
    if (!timelineRef.current) return;
    timelineRef.current.fit();
  };

  const timeLineGoLeft = () => {
    if (!timelineRef.current) return;
    const range = timelineRef.current.getWindow();
    const interval = (range.end.getTime() - range.start.getTime()) * 0.2;
    timelineRef.current.setWindow(new Date(range.start.getTime() - interval), new Date(range.end.getTime() - interval));
  };

  const timeLineGoRight = () => {
    if (!timelineRef.current) return;
    const range = timelineRef.current.getWindow();
    const interval = (range.end.getTime() - range.start.getTime()) * 0.2;
    timelineRef.current.setWindow(new Date(range.start.getTime() + interval), new Date(range.end.getTime() + interval));
  };

  const timelineZoomIn = () => {
    if (!timelineRef.current) return;
    timelineRef.current.zoomIn(0.5);
  };

  const timelineZoomOut = () => {
    if (!timelineRef.current) return;
    timelineRef.current.zoomOut(0.5);
  };

  const renderViewBaseOnEventType = (eventType: EventType) => {
    if (!selectedLog) return;
    switch (eventType) {
      case 'ChannelChangeByWiFiDriverCompleted':
        return <ChannelChangeByWifiDriverLogView log={selectedLog as ChannelChangeByWiFiDriverCompletedLog} />;
      case 'WanUp':
      case 'WanDown':
        return <WanLogView log={selectedLog as WanUpLog | WanDownLog} />;
      case 'WifiInterfaceStatusChanged':
        return <WifiInterfaceStatusChangeLogView log={selectedLog as WifiInterfaceStatusChangedLog} />;
      case 'InvalidL3Fixed':
      case 'InvalidL3':
        return <InvalidL3LogView log={selectedLog as InvalidL3Log | InvalidL3FixedLog} />;
      case 'AgentRestart':
      case 'WanChange':
      case 'InternetDown':
      case 'InternetUp':
      case 'KeepAliveDown':
      case 'KeepAliveUp':
      default:
        return <BaseLogView log={selectedLog} />;
    }
  };

  return (
    <div id='event-time-line-container' className='relative flex size-full flex-col'>
      <div id='event-time-line' ref={containerRef} className='flex-1' />
      <div className='absolute right-4 top-4 z-10 flex gap-x-2'>
        <AxonTooltipWrapper
          label={
            <AxonButton onClick={timeLineGoLeft} size={'icon-xs'} variant={'solid-info'}>
              <ChevronLeft className='size-4' />
            </AxonButton>
          }
          content={t('logs.controls.timeLineGoLeft')}
        />

        <AxonTooltipWrapper
          label={
            <AxonButton onClick={timeLineGoRight} size={'icon-xs'} variant={'solid-info'}>
              <ChevronRight className='size-4' />
            </AxonButton>
          }
          content={t('logs.controls.timeLineGoRight')}
        />
        <AxonTooltipWrapper
          label={
            <AxonButton onClick={timelineFit} size={'icon-xs'} variant={'solid-info'}>
              <Crosshair className='size-4' />
            </AxonButton>
          }
          content={t('logs.controls.timelineFit')}
        />
        <AxonTooltipWrapper
          label={
            <AxonButton onClick={timelineZoomIn} size={'icon-xs'} variant={'solid-info'}>
              <ZoomIn className='size-4' />
            </AxonButton>
          }
          content={t('logs.controls.timelineZoomIn')}
        />
        <AxonTooltipWrapper
          label={
            <AxonButton onClick={timelineZoomOut} size={'icon-xs'} variant={'solid-info'}>
              <ZoomOut className='size-4' />
            </AxonButton>
          }
          content={t('logs.controls.timelineZoomOut')}
        />
      </div>

      <div tabIndex={0} className='scrollbar-lg grid h-[330px] auto-rows-[40px] grid-cols-4 overflow-auto p-6'>
        {Object.entries(logCount).map(([type, { count, icon: Icon }]) => (
          <div key={type} className='flex items-center gap-x-2'>
            <Icon className='text-content-secondary size-4' />
            <p className='w-[250px]'>{t(`logs.eventTypes.${type}`, { defaultValue: type })}</p>
            <p>{count}</p>
          </div>
        ))}
      </div>
      {selectedLog && (
        <div
          className='bg-surface-body absolute z-10 flex flex-col gap-y-2 rounded border p-3 shadow-md'
          style={{
            top: popupPosition.y,
            left: popupPosition.x,
          }}>
          {renderViewBaseOnEventType(selectedLog.eventType)}
        </div>
      )}
    </div>
  );
};

export default LogChart;
