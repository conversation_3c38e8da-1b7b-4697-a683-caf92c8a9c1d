import { InvalidL3FixedLog, InvalidL3Log } from '@/components/Logs/logs.type';
import { useTranslation } from 'react-i18next';
import { DATETIME_FORMAT, formatDate, humanizeDuration } from 'services/Utils';
import get from 'lodash/get';

interface InvalidL3LogViewProps {
  log: InvalidL3Log | InvalidL3FixedLog;
}

const InvalidL3LogView = ({ log }: InvalidL3LogViewProps) => {
  const { t } = useTranslation();

  return (
    <div>
      <div className='flex flex-row items-center gap-x-2'>
        <p className='w-[150px]'>{t('logs.details.event')}</p>
        <p className='font-semibold'>{t(`logs.eventTypes.${log.eventType}`, { defaultValue: log.eventType })}</p>
      </div>
      <div className='flex flex-row items-center gap-x-2'>
        <p className='w-[150px]'>{t('logs.details.accessPoint')}</p>
        <p className=''>{log.cpeId}</p>
      </div>
      <div className='flex flex-row items-center gap-x-2'>
        <p className='w-[150px]'>{t('logs.details.at')}</p>
        <p className=''>{formatDate(log.timeStamp, DATETIME_FORMAT.DATE_TIME)}</p>
      </div>

      <div className='flex flex-row items-center gap-x-2'>
        <p className='w-[150px]'>{t('logs.details.ipAddress')}</p>
        <p className=''>{get(log, 'detail.stationIpAddress', 'N/A')}</p>
      </div>
      <div className='flex flex-row items-center gap-x-2'>
        <p className='w-[150px]'>{t('logs.details.device')}</p>
        <p className=''>{get(log, 'detail.stationMacAddress', 'N/A')}</p>
      </div>
      <div className='flex flex-row items-center gap-x-2'>
        <p className='w-[150px]'>{t('logs.details.lanAddress')}</p>
        <p className=''>{get(log, 'detail.lanAddress', 'N/A')}</p>
      </div>
      <div className='flex flex-row items-center gap-x-2'>
        <p className='w-[150px]'>{t('logs.details.lanSubnet')}</p>
        <p className=''>{get(log, 'detail.lanNetmask', 'N/A')}</p>
      </div>
      {log.eventType === 'InvalidL3Fixed' && log.detail.elapsedSeconds && (
        <div className='flex flex-row items-center gap-x-2'>
          <p className='w-[150px]'>{t('logs.details.errorDuration')}</p>
          <p className=''>{humanizeDuration(log.detail.elapsedSeconds)}</p>
        </div>
      )}
    </div>
  );
};

export default InvalidL3LogView;
