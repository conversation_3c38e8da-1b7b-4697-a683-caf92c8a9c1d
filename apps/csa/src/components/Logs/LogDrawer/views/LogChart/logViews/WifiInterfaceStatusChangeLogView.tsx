import { WifiInterfaceStatusChangedLog } from '@/components/Logs/logs.type';
import { useTranslation } from 'react-i18next';
import { DATETIME_FORMAT, formatDate } from 'services/Utils';

interface WifiInterfaceStatusChangeLogViewProps {
  log: WifiInterfaceStatusChangedLog;
}

const WifiInterfaceStatusChangeLogView = ({ log }: WifiInterfaceStatusChangeLogViewProps) => {
  const { t } = useTranslation();
  return (
    <div>
      <div className='flex flex-row items-center gap-x-2'>
        <p className='w-[150px]'>{t('logs.details.event')}</p>
        <p className='font-semibold'>{t(`logs.eventTypes.${log.eventType}`, { defaultValue: log.eventType })}</p>
      </div>
      <div className='flex flex-row items-center gap-x-2'>
        <p className='w-[150px]'>{t('logs.details.accessPoint')}</p>
        <p className=''>{log.cpeId}</p>
      </div>
      <div className='flex flex-row items-center gap-x-2'>
        <p className='w-[150px]'>{t('logs.details.at')}</p>
        <p className=''>{formatDate(log.timeStamp, DATETIME_FORMAT.DATE_TIME)}</p>
      </div>
      <div className='flex flex-row items-center gap-x-2'>
        <p className='w-[150px]'>{t('logs.details.interface')}</p>
        <p className=''>{log.detail.interface}</p>
      </div>
      <div className='flex flex-row items-center gap-x-2'>
        <p className='w-[150px]'>{t('logs.details.status')}</p>
        <p className=''>{log.detail.ifaceDown ? t('logs.details.disabled') : t('logs.details.enabled')}</p>
      </div>
    </div>
  );
};

export default WifiInterfaceStatusChangeLogView;
