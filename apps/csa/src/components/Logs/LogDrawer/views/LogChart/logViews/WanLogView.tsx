import { WanDownLog, WanUpLog } from '@/components/Logs/logs.type';
import { useTranslation } from 'react-i18next';
import { DATETIME_FORMAT, formatDate } from 'services/Utils';

interface WanLogViewProps {
  log: WanUpLog | WanDownLog;
}

const WanLogView = ({ log }: WanLogViewProps) => {
  const { t } = useTranslation();
  return (
    <div>
      <div className='flex flex-row items-center gap-x-2'>
        <p className='w-[150px]'>{t('logs.details.event')}</p>
        <p className='font-semibold'>{t(`logs.eventTypes.${log.eventType}`, { defaultValue: log.eventType })}</p>
      </div>
      <div className='flex flex-row items-center gap-x-2'>
        <p className='w-[150px]'>{t('logs.details.accessPoint')}</p>
        <p className=''>{log.cpeId}</p>
      </div>
      <div className='flex flex-row items-center gap-x-2'>
        <p className='w-[150px]'>{t('logs.details.at')}</p>
        <p className=''>{formatDate(log.timeStamp, DATETIME_FORMAT.DATE_TIME)}</p>
      </div>
      {log.eventType === 'WanDown' && (
        <div className='flex flex-row items-center gap-x-2'>
          <p className='w-[150px]'>{t('logs.details.linkDown')}</p>
          <p className=''>{log.detail?.linkDown ? t('logs.details.yes') : t('logs.details.no')}</p>
        </div>
      )}
    </div>
  );
};

export default WanLogView;
