import { ChannelChangeByWiFiDriverCompletedLog } from '@/components/Logs/logs.type';
import { useTranslation } from 'react-i18next';
import { DATETIME_FORMAT, formatDate } from 'services/Utils';
import get from 'lodash/get';

interface ChannelChangeByWifiDriverLogViewProps {
  log: ChannelChangeByWiFiDriverCompletedLog;
}

const ChannelChangeByWifiDriverLogView = ({ log }: ChannelChangeByWifiDriverLogViewProps) => {
  const { t } = useTranslation();
  return (
    <div>
      <div className='flex flex-row items-center gap-x-2'>
        <p className='w-[150px]'>{t('logs.details.event')}</p>
        <p className='font-semibold'>{t(`logs.eventTypes.${log.eventType}`, { defaultValue: log.eventType })}</p>
      </div>
      <div className='flex flex-row items-center gap-x-2'>
        <p className='w-[150px]'>{t('logs.details.accessPoint')}</p>
        <p className=''>{get(log, 'cpeId', 'N/A')}</p>
      </div>
      <div className='flex flex-row items-center gap-x-2'>
        <p className='w-[150px]'>{t('logs.details.at')}</p>
        <p className=''>{formatDate(log.timeStamp, DATETIME_FORMAT.DATE_TIME)}</p>
      </div>
      <div className='flex flex-row items-center gap-x-2'>
        <p className='w-[150px]'>{t('logs.details.interface')}</p>
        <p className=''>{get(log, 'detail.interface', 'N/A')}</p>
      </div>
      <div className='flex flex-row items-center gap-x-2'>
        <p className='w-[150px]'>{t('logs.details.sourceChannel')}</p>
        <p className=''>{get(log, 'detail.currentChannel.primary', 'N/A')}</p>
      </div>
      <div className='flex flex-row items-center gap-x-2'>
        <p className='w-[150px]'>{t('logs.details.sourceBandwidth')}</p>
        <p className=''>{get(log, 'detail.currentChannel.bandwidth', 'N/A')}</p>
      </div>
      <div className='flex flex-row items-center gap-x-2'>
        <p className='w-[150px]'>{t('logs.details.targetChannel')}</p>
        <p className=''>{get(log, 'detail.targetChannel.primary', 'N/A')}</p>
      </div>
      <div className='flex flex-row items-center gap-x-2'>
        <p className='w-[150px]'>{t('logs.details.targetBandwidth')}</p>
        <p className=''>{get(log, 'detail.targetChannel.bandwidth', 'N/A')}</p>
      </div>
    </div>
  );
};

export default ChannelChangeByWifiDriverLogView;
