import { useTabDeviceId, useTabLineId, useTabType } from '@/stores/tab.store';
import { useDrawerStore, useEventDrawer } from '@/stores/drawer.store';
import { useEffect } from 'react';
import { useShallow } from 'zustand/react/shallow';
import { LogDrawerTabs } from '@/stores/widgets.config';
import { useGetTimeRangeByLine } from '@/stores/timeRange.store';

export function useFilterState() {
  const lineId = useTabLineId();
  const timeRangeSelected = useGetTimeRangeByLine(lineId || '');
  const defaultStartDate = timeRangeSelected?.endDate || null;
  const defaultEndDate = timeRangeSelected?.endDate || null;

  const deviceId = useTabDeviceId();
  const tabType = useTabType();

  const updateDrawer = useDrawerStore(useShallow((state) => state.updateDrawerEvent));

  const { searchText, startDate, endDate, open, sourceFilter, eventTypeFilter, cpeIdFilter, viewMode } =
    useEventDrawer();

  useEffect(() => {
    if (!open) {
      updateDrawer({
        searchText: '',
        startDate: defaultStartDate,
        endDate: defaultEndDate,
        sourceFilter: 'all',
        eventTypeFilter: 'all',
        cpeIdFilter: tabType === 'device' ? deviceId || '' : 'all',
        viewMode: LogDrawerTabs.Logs,
      });
    }
  }, [open, defaultEndDate, defaultStartDate, updateDrawer, tabType, deviceId]);

  const changeFilter = ({
    startDate,
    endDate,
    searchText,
    sourceFilter,
    eventTypeFilter,
    cpeIdFilter,
    viewMode,
  }: {
    startDate?: Date | null;
    endDate?: Date | null;
    searchText?: string;
    sourceFilter?: string;
    eventTypeFilter?: string;
    cpeIdFilter?: string;
    viewMode?: (typeof LogDrawerTabs)[keyof typeof LogDrawerTabs];
  }) => {
    updateDrawer({
      ...(startDate !== undefined ? { startDate } : {}),
      ...(endDate !== undefined ? { endDate } : {}),
      ...(searchText !== undefined ? { searchText } : {}),
      ...(sourceFilter !== undefined ? { sourceFilter } : {}),
      ...(eventTypeFilter !== undefined ? { eventTypeFilter } : {}),
      ...(cpeIdFilter !== undefined ? { cpeIdFilter } : {}),
      ...(viewMode !== undefined ? { viewMode } : {}),
    });
  };

  return {
    searchText,
    startDate,
    endDate,
    sourceFilter,
    eventTypeFilter,
    cpeIdFilter,
    viewMode,
    changeFilter,
  };
}
