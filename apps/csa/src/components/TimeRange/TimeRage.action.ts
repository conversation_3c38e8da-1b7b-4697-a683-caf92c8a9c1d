import { startRealtimeProcess } from 'services/Realtime';
import { useTabLineId } from '@/stores/tab.store';
import useRealtimeConfigStore, { useRealtimeLineConfig, useGetSelectedRequestInfo } from '@/stores/realtime.store';
import { useTranslation } from 'react-i18next';
import { AxonToast } from 'ui/UIComponents';
import { TRealtimeMetric } from '@/types/realtime.type';
import { useState } from 'react';

export const useTimeRangeAction = () => {
  const updateRealtimeConfig = useRealtimeConfigStore((state) => state.updateRealtimeConfig);
  const updateRealtimeState = useRealtimeConfigStore((state) => state.updateRealtimeState);
  const updateOnDemand = useRealtimeConfigStore((state) => state.updateOnDemand);

  const { t } = useTranslation();
  const lineId = useTabLineId() || '';
  const realtimeLineConfig = useRealtimeLineConfig(lineId);
  const selectedRequest = useGetSelectedRequestInfo(lineId, 'LINE');
  const [showDialog, setShowDialog] = useState(false);

  const handleGetRealtime = () => {
    if (realtimeLineConfig?.isOnFly) {
      // has not supported yet.
      return;
    }
    const metric: TRealtimeMetric[] = ['operational']; // default metric which is general data.
    if (realtimeLineConfig?.isWifiScan) {
      metric.push('wifiScan');
    }
    if (realtimeLineConfig?.isWifiSpeed) {
      metric.push('wifiSpeed');
    }
    if (realtimeLineConfig?.isBroadbandSpeed) {
      metric.push('broadbandSpeed');
    }
    startRealtimeProcess(
      {
        lineId,
        metric,
      },
      updateRealtimeState,
      (status) => {
        if (status === 'SUCCESS') {
          updateOnDemand(lineId, true);

          setShowDialog(false);
          AxonToast.success(t('customer:realtime.success'));
        } else if (status === 'FAILED') {
          AxonToast.error(t('customer:realtime.error'));
        }
      },
    );
  };

  const handleChangeOnDemandConfig = (key: string, value: boolean) => {
    updateRealtimeConfig([lineId, 'line', key], value);
  };

  const handleCancel = () => {
    setShowDialog(false);
  };

  const handleChangeToggle = (value: boolean) => {
    // if user want to turn on on-demand
    if (value) {
      const shouldShowDialog = Boolean(!selectedRequest);
      if (shouldShowDialog) {
        setShowDialog(true);
      } else {
        updateOnDemand(lineId, value);
      }
    } else {
      updateOnDemand(lineId, value);
    }
  };
  return {
    handleGetRealtime,
    handleChangeToggle,
    handleChangeOnDemandConfig,
    showDialog,
    setShowDialog,
    handleCancel,
  };
};
