import {
  AxonAlertDialog,
  AxonAlertDialogContent,
  AxonAlertDialogFooter,
  AxonAlertDialogHeader,
  AxonAlertDialogTitle,
  AxonAlertDialogDescription,
  AxonAlertDialogCancel,
  AxonAlertDialogAction,
  AxonRadioGroup,
  AxonRadioGroupItem,
  AxonCheckbox,
  AxonLabel,
  AxonSwitch,
  ProgressBar,
} from 'ui/UIComponents';
import { useTranslation } from 'react-i18next';
import { cn } from '@/utils';
import {
  useIsOnDemand,
  useIsOnFly,
  useRealtimeLineConfig,
  useGetSelectedRequestInfo,
  useLineIsBusy,
} from '@/stores/realtime.store';
import get from 'lodash/get';
import { useTabLineId } from '@/stores/tab.store';
import { useGetCapabilities } from '@/hooks/useGetCapabilities';

const DEMAND_OPTION = {
  ON_THE_FLY: 'on-the-fly',
  REAL_TIME: 'real-time',
};

interface IProps {
  showDialog: boolean;
  handleChangeToggle: (value: boolean) => void;
  handleChangeOnDemandConfig: (key: string, value: boolean) => void;
  handleCancel: () => void;
  handleGetRealtime: () => void;
}

export const SwitchOnDemandButton = (props: IProps) => {
  const { showDialog, handleChangeToggle, handleChangeOnDemandConfig, handleCancel, handleGetRealtime } = props;
  const { t } = useTranslation();
  const lineId = useTabLineId() || '';
  const isOnDemand = useIsOnDemand(lineId);
  const isOnFlyData = useIsOnFly(lineId);
  const realtimeLineConfig = useRealtimeLineConfig(lineId);

  const selectedRequestInfo = useGetSelectedRequestInfo(lineId, 'LINE');
  const isRealtimeRequesting = get(selectedRequestInfo, 'isRealtimeRequesting');
  const currentStepText = get(selectedRequestInfo, 'currentStepText') || t('realtime.analyzing');
  const progress = get(selectedRequestInfo, 'progress', 0);
  const capabilities = useGetCapabilities();
  const lineIsBusy = useLineIsBusy(lineId);
  const isRealtimeEnabled = get(capabilities, 'isRealtimeEnabled', false);
  const disabled = lineIsBusy || isRealtimeRequesting || (!isRealtimeEnabled && !isOnDemand);

  return (
    <>
      {/* Separate the switch from the dialog trigger */}
      <div className='gap-xs flex items-center'>
        <AxonLabel
          htmlFor='customer-switch-demand-mode'
          className='text-content-secondary font-book font-global text-sm hover:cursor-pointer'>
          {t('realtime.onDemand')}
        </AxonLabel>
        <AxonSwitch
          disabled={disabled}
          id='customer-switch-demand-mode'
          data-testid='customer-switch-demand-mode'
          className='text-secondary font-book font-global text-sm'
          checked={isOnDemand}
          onCheckedChange={handleChangeToggle}
        />
      </div>
      {/* Controlled dialog without a trigger */}
      <AxonAlertDialog open={showDialog} onOpenChange={handleCancel}>
        <AxonAlertDialogContent className={cn('p-xl w-xs max-w-full')}>
          <AxonAlertDialogHeader className='gap-xl items-start'>
            <AxonAlertDialogTitle
              data-testid='realtime-dialog-title'
              className={cn('text-content-primary self-start text-lg font-medium')}>
              {t('realtime.title')}
            </AxonAlertDialogTitle>
            <AxonAlertDialogDescription
              data-testid='realtime-dialog-description'
              className={cn('text-content-secondary text-md')}>
              {t('realtime.selectOne')}
            </AxonAlertDialogDescription>
            <div className='gap-xl flex self-start'>
              <AxonRadioGroup
                className='gap-md'
                defaultValue={DEMAND_OPTION.REAL_TIME}
                value={isOnFlyData ? DEMAND_OPTION.ON_THE_FLY : DEMAND_OPTION.REAL_TIME}
                onValueChange={(value) => handleChangeOnDemandConfig('isOnFly', value === DEMAND_OPTION.ON_THE_FLY)}>
                <div className='gap-xs flex items-start'>
                  <AxonRadioGroupItem
                    variant='primary'
                    id='on-the-fly'
                    disabled // on the fly has not supported yet.
                    value={DEMAND_OPTION.ON_THE_FLY}
                    size='large'
                    className='shrink-0'
                  />
                  <div className='text-left opacity-50'>
                    <AxonLabel
                      className='text-content-secondary text-md mb-1 font-normal leading-[120%]'
                      htmlFor='on-the-fly'>
                      {t('realtime.onTheFly')}
                    </AxonLabel>
                    <p className='text-content-secondary font-book text-sm leading-[120%]'>
                      {t('realtime.onTheFlyDescription')}
                    </p>
                  </div>
                </div>
                <div className='gap-xs flex items-start'>
                  <AxonRadioGroupItem
                    variant='primary'
                    id='real-time'
                    value={DEMAND_OPTION.REAL_TIME}
                    size='large'
                    className='shrink-0'
                  />
                  <div className='text-left'>
                    <AxonLabel
                      className='text-content-secondary text-md mb-1 font-normal leading-[120%]'
                      htmlFor='real-time'>
                      {t('realtime.realtime')}
                    </AxonLabel>
                    <p className='text-content-secondary font-book text-sm leading-[120%]'>
                      {t('realtime.realtimeDescription')}
                    </p>
                    <div className='gap-xs mt-md flex flex-col'>
                      {/* General Data */}
                      <div className='gap-xs flex items-center'>
                        <AxonCheckbox data-testid='general-data-checkbox' id='general-data' checked={true} disabled />
                        <AxonLabel
                          data-testid='general-data-label'
                          className='text-content-secondary font-global text-md font-regular leading-[120%] !opacity-100'
                          htmlFor='general-data'>
                          {t('realtime.generalData')}
                        </AxonLabel>
                      </div>
                      {/* Broadband Speed/Latency */}
                      <div className='gap-xs flex items-center'>
                        <AxonCheckbox
                          data-testid='broadband-speed-latency-checkbox'
                          id='broadband-speed-latency'
                          checked={Boolean(realtimeLineConfig?.isBroadbandSpeed)}
                          onCheckedChange={(value) => handleChangeOnDemandConfig('isBroadbandSpeed', Boolean(value))}
                          disabled={isOnFlyData}
                        />
                        <AxonLabel
                          data-testid='broadband-speed-latency-label'
                          className='text-content-secondary font-global text-md font-regular leading-[120%]'
                          htmlFor='broadband-speed-latency'>
                          {t('realtime.broadbandSpeedLatency')}
                        </AxonLabel>
                      </div>
                      {/* Wi-Fi Speed/Latency */}
                      <div className='gap-xs flex items-center'>
                        <AxonCheckbox
                          data-testid='wifi-speed-latency-checkbox'
                          id='wifi-speed-latency'
                          checked={Boolean(realtimeLineConfig?.isWifiSpeed)}
                          disabled={isOnFlyData}
                          onCheckedChange={(value) => handleChangeOnDemandConfig('isWifiSpeed', Boolean(value))}
                        />
                        <AxonLabel
                          data-testid='wifi-speed-latency-label'
                          className='text-content-secondary font-global text-md font-regular leading-[120%]'
                          htmlFor='wifi-speed-latency'>
                          {t('realtime.wifiSpeedLatency')}
                        </AxonLabel>
                      </div>
                      {/* Wi-Fi scan */}
                      {/* <div className='gap-xs flex items-center'>
                        <AxonCheckbox
                          data-testid='wifi-scan-checkbox'
                          id='wifi-scan'
                          checked={Boolean(realtimeLineConfig?.isWifiScan)}
                          disabled={isOnFlyData}
                          onCheckedChange={(value) => handleChangeOnDemandConfig('isWifiScan', Boolean(value))}
                        />
                        <AxonLabel
                          data-testid='wifi-scan-label'
                          className='text-content-secondary font-global text-md font-regular leading-[120%]'
                          htmlFor='wifi-scan'>
                          {t('realtime.wifiScan')}
                        </AxonLabel>
                      </div> */}
                    </div>
                  </div>
                </div>
              </AxonRadioGroup>
            </div>
          </AxonAlertDialogHeader>
          <AxonAlertDialogFooter className={cn('pt-4')}>
            <AxonAlertDialogCancel data-testid='realtime-dialog-cancel-button' onClick={handleCancel}>
              {t('cancel')}
            </AxonAlertDialogCancel>
            <AxonAlertDialogAction data-testid='realtime-dialog-start-button' onClick={handleGetRealtime}>
              {t('realtime.confirm')}
            </AxonAlertDialogAction>
          </AxonAlertDialogFooter>
        </AxonAlertDialogContent>
      </AxonAlertDialog>
      {isRealtimeRequesting && (
        <AxonAlertDialog open>
          <AxonAlertDialogContent className='w-xs max-w-full'>
            <AxonAlertDialogHeader>
              <AxonAlertDialogTitle>{currentStepText}</AxonAlertDialogTitle>
            </AxonAlertDialogHeader>
            <AxonAlertDialogDescription>
              <ProgressBar
                total={100}
                values={[{ value: progress || 0, label: 'Done', color: 'rgb(var(--surface-accent-purple))' }]}
              />
            </AxonAlertDialogDescription>
            <AxonAlertDialogFooter className={cn('pt-4')}>
              <AxonAlertDialogCancel disabled data-testid='realtime-dialog-cancel-button' onClick={handleCancel}>
                {t('Abort')}
              </AxonAlertDialogCancel>
            </AxonAlertDialogFooter>
          </AxonAlertDialogContent>
        </AxonAlertDialog>
      )}
    </>
  );
};
