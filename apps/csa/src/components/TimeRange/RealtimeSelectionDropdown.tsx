import {
  AxonSelect,
  AxonSelectTrigger,
  AxonSelectContent,
  AxonSelectItem,
  AxonSelectValue,
  AxonButton,
} from 'ui/UIComponents';
import { formatDate, DATETIME_FORMAT } from 'services/Utils';
import useRealtimeConfigStore, {
  useIsOnDemand,
  useGetListRequestInfo,
  useSelectedRequestId,
} from '@/stores/realtime.store';

import { useTabLineId } from '@/stores/tab.store';
import { RefreshCcw } from 'ui/UIAssets';

interface IProps {
  onRefresh: () => void;
}

export const TimeRangeDropdown = ({ onRefresh }: IProps) => {
  const updateSelection = useRealtimeConfigStore((state) => state.updateRequestIdSelection);
  const lineId = useTabLineId() || '';
  const selectedRequestId = useSelectedRequestId(lineId, 'LINE');
  const listRequestInfo = useGetListRequestInfo(lineId, 'LINE');
  const isOnDemand = useIsOnDemand(lineId);
  const disabled = !isOnDemand;

  function handleChangeRequestId(requestId: string) {
    updateSelection(lineId, 'LINE', requestId);
  }

  if (!isOnDemand || listRequestInfo.length === 0) return null;

  return (
    <div className='gap-xs flex items-center'>
      <AxonSelect disabled={disabled} value={selectedRequestId} onValueChange={handleChangeRequestId}>
        <AxonSelectTrigger className='text-surface-accent-purple h-9 w-[140px] border-none bg-transparent p-0'>
          <AxonSelectValue placeholder='Select an option' />
        </AxonSelectTrigger>
        <AxonSelectContent>
          {listRequestInfo.map((request) => (
            <AxonSelectItem key={request.realtimeRequestId} value={request.realtimeRequestId!}>
              {formatDate(request.lastUpdated!, DATETIME_FORMAT.DATE_TIME)}
            </AxonSelectItem>
          ))}
        </AxonSelectContent>
      </AxonSelect>
      <AxonButton disabled={disabled} className='size-4' variant={'ghost'} size={'icon'} onClick={onRefresh}>
        <RefreshCcw className='text-surface-accent-purple' />
      </AxonButton>
    </div>
  );
};
