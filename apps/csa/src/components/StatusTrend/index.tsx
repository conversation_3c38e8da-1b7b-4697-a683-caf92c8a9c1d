import { DATETIME_FORMAT, formatDate, isToday } from 'services/Utils';
import { cn } from '@/utils';
import {
  AxonSeparator,
  AxonTooltip,
  AxonTooltipContent,
  AxonTooltipPortal,
  AxonTooltipProvider,
  AxonTooltipTrigger,
} from 'ui/UIComponents';
import React from 'react';
import { useTranslation } from 'react-i18next';

function getColorByStatusForTrend(status: string): string {
  if (status === 'stable') {
    return 'rgb(var(--content-meta-green))';
  }
  if (status === 'unstable') {
    return 'rgb(var(--gradient-line-graph-yellow-start))';
  }
  if (status === 'veryUnstable') {
    return 'rgb(var(--content-meta-red))';
  }
  if (status === 'unknown') {
    return 'rgb(var(--surface-action-hover))';
  }
  return 'rgb(var(--surface-action-hover))';
}

function getTextColorByStatusForTrend(status: string): string {
  if (status === 'stable') {
    return 'rgb(var(--content-meta-green))';
  }
  if (status === 'unstable') {
    return 'rgb(var(--gradient-line-graph-yellow-start))';
  }
  if (status === 'veryUnstable') {
    return 'rgb(var(--content-meta-red))';
  }
  if (status === 'unknown') {
    return 'rgb(var(--content-meta-gray))';
  }
  return 'rgb(var(--content-meta-gray))';
}

type AxonStatusWeekLyTrendProps = {
  data: {
    status: string;
    date: number;
  }[];
  dateSpacing?: number;
  hideDate?: boolean;
  hideSeparator?: boolean;
  compact?: boolean;
};

export const StatusTrend = ({
  data,
  dateSpacing = 3,
  hideDate = false,
  hideSeparator = false,
  compact = false,
}: AxonStatusWeekLyTrendProps) => {
  const { t } = useTranslation();

  const shouldShowDate = (index: number) => {
    const isLast = index === data.length - 1;

    if (isLast) return true;

    const distanceFromEnd = data.length - 1 - index;
    return index % dateSpacing === 0 && distanceFromEnd >= dateSpacing;
  };

  return (
    <div className={cn('flex flex-row items-center gap-x-1', !hideDate && 'pb-3')}>
      {data.map((item, index) => {
        const isLast = index === data.length - 1;
        const showDate = hideDate
          ? false
          : shouldShowDate(index)
            ? isLast && isToday(item.date)
              ? t('datetime.today')
              : formatDate(item.date, DATETIME_FORMAT.MONTH_DAY)
            : '';

        return (
          <React.Fragment key={item.date}>
            <div className='relative'>
              <AxonTooltipProvider delayDuration={100}>
                <AxonTooltip>
                  <AxonTooltipTrigger asChild>
                    <div
                      className={cn(
                        'border-gradient-border rounded-2xs size-3 border',
                        compact && 'w-1.5',
                        isLast && isToday(item.date) && 'w-8',
                      )}
                      style={{ backgroundColor: getColorByStatusForTrend(item.status) }}
                    />
                  </AxonTooltipTrigger>
                  <AxonTooltipPortal>
                    <AxonTooltipContent>
                      <div className='flex items-center gap-x-2'>
                        <p>{formatDate(item.date, DATETIME_FORMAT.MONTH_DAY)}</p>
                        <p style={{ color: getTextColorByStatusForTrend(item.status) }}>{t(`${item.status}`)}</p>
                      </div>
                    </AxonTooltipContent>
                  </AxonTooltipPortal>
                </AxonTooltip>
              </AxonTooltipProvider>

              {showDate && (
                <p className='text-content-tertiary text-2xs absolute left-1/2 -translate-x-1/2 whitespace-nowrap font-medium'>
                  {showDate}
                </p>
              )}
            </div>
            {index < data.length - 1 && !hideSeparator && <AxonSeparator orientation='vertical' className='h-2' />}
          </React.Fragment>
        );
      })}
    </div>
  );
};
