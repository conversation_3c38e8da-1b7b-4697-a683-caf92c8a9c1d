import { useDrawerStore, useWifiStatsDrawer } from '@/stores/drawer.store';
import { useTabDeviceId } from '@/stores/tab.store';
import { getCoreRowModel, getFilteredRowModel, getSortedRowModel, useReactTable } from '@tanstack/react-table';
import get from 'lodash/get';
import { Search } from 'lucide-react';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { getDayjsFromDate, getTimezone, getUnixTime } from 'services/Utils';
import { useGetWifiStatsDrawer } from 'services/WifiStatistics';
import { AxonDateRangePicker, AxonSheet, AxonSheetContent, AxonTableData, AxonTableInputSearch } from 'ui/UIComponents';
import { useShallow } from 'zustand/react/shallow';
import { WifiBandSelect } from './WifiBandSelect';
import { useColumn } from './columns';
import { useFilterState } from './filterState';

type DateRange = {
  from: Date | undefined;
  to?: Date;
};

interface WifiStatsDrawerProps {
  bands: {
    bandId: string;
    bandName: string;
  }[];
}

export const WifiStatsDrawer = ({ bands }: WifiStatsDrawerProps) => {
  const { t } = useTranslation();
  const deviceId = useTabDeviceId() || '';

  const { open } = useWifiStatsDrawer();
  const { searchText, startDate, endDate, selectedBandId, changeFilter } = useFilterState();

  const closeDrawer = useDrawerStore(useShallow((state) => state.closeDrawer));

  const columns = useColumn();

  const {
    data: wifiData,
    isLoading: isLoadingWifiStats,
    isError: isErrorWifiStats,
  } = useGetWifiStatsDrawer(
    {
      deviceId,
      startDate: getUnixTime(startDate) || 0,
      endDate: getUnixTime(endDate) || undefined,
    },
    {
      enabled: !!open && !!deviceId && !!startDate,
    },
  );

  const dataTable = useMemo(() => get(wifiData, `data.result.${selectedBandId}`) || [], [wifiData, selectedBandId]);

  const table = useReactTable({
    data: dataTable,
    columns,
    state: {
      globalFilter: searchText,
    },
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getSortedRowModel: getSortedRowModel(),
  });

  function handleSetTimeRange(date?: DateRange) {
    const startDate = getDayjsFromDate(date?.from).startOf('day').tz(getTimezone(), true).toDate();
    const endDate = getDayjsFromDate(date?.to).startOf('day').tz(getTimezone(), true).toDate();

    changeFilter({
      startDate,
      endDate,
    });
  }

  function handleChangeSearch(value: string) {
    changeFilter({
      searchText: value,
    });
  }

  function handleChangeBand(value: string) {
    changeFilter({
      selectedBandId: value,
    });
  }

  const customBands = useMemo(() => {
    if (!bands) return [];
    return bands.map((band) => {
      return {
        ...band,
        bandName: `${t('device:lanWlan.wifiStats.title')} ${band.bandName}`,
      };
    });
  }, [bands, t]);

  return (
    <AxonSheet
      open={open}
      onOpenChange={(open) => !open && closeDrawer('wifiStatistics')}
      data-testid='device-wifi-stats-drawer'>
      <AxonSheetContent
        className='insight-axonsheet-content md w-full overflow-y-auto p-0 sm:max-w-5xl md:max-w-screen-md 2xl:max-w-screen-lg'
        data-testid='device-wifi-stats-drawer-content'>
        <div className='border-b-border-flat flex h-16 items-center border-b px-4'>
          <WifiBandSelect
            style='drawer'
            wifiBands={customBands}
            selectedBandId={selectedBandId}
            setSelectedBandId={handleChangeBand}
            data-testid='device-wifi-stats-drawer-band-select'
          />
        </div>
        <div className='border-b-border-flat insight-search-block flex h-16 items-center justify-between border-b px-4'>
          <div className='relative h-fit'>
            <Search className='text-content-tertiary absolute left-3 top-1/2 size-4 -translate-y-1/2' />

            <AxonTableInputSearch
              value={searchText}
              onChange={handleChangeSearch}
              placeholder={t('device:deviceInfo.cpeStats.searchInputPlaceholder')}
              className='h-8 w-[360px] pl-10'
              data-testid='device-wifi-stats-drawer-statistic-search-input'
            />
          </div>
          <div className='flex w-fit gap-2'>
            <AxonDateRangePicker
              selected={{
                from: startDate ?? undefined,
                to: endDate ?? undefined,
              }}
              onApply={handleSetTimeRange}
              showIcon={false}
              data-testid='device-wifi-stats-drawer-statistic-date-range-picker'
            />
          </div>
        </div>
        <div className='flex flex-wrap content-start items-start self-stretch'>
          <AxonTableData
            table={table}
            showFooter={false}
            isLoading={isLoadingWifiStats}
            isError={isErrorWifiStats}
            data-testid='device-wifi-stats-drawer-statistic-table'
          />
        </div>
      </AxonSheetContent>
    </AxonSheet>
  );
};
