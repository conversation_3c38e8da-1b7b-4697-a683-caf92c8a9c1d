import { cn } from '@/utils';
import { useTranslation } from 'react-i18next';
import {
  AxonSelect,
  AxonSelectContent,
  AxonSelectGroup,
  AxonSelectItem,
  AxonSelectLabel,
  AxonSelectTrigger,
  AxonSelectValue,
} from 'ui/UIComponents';

interface Props {
  disabled?: boolean;
  wifiBands?: {
    bandId: string;
    bandName: string;
  }[];
  style?: 'inline' | 'drawer';
  selectedBandId: string;
  setSelectedBandId: (id: string) => void;
  dataTestId?: string;
}

export const WifiBandSelect = (props: Props) => {
  const { t } = useTranslation();
  const {
    disabled,
    wifiBands,
    style = 'inline',
    selectedBandId,
    setSelectedBandId,
    dataTestId = 'wifi-band-select',
  } = props;

  return (
    <AxonSelect disabled={disabled} value={selectedBandId} onValueChange={setSelectedBandId} data-testid={dataTestId}>
      <AxonSelectTrigger
        className={cn('w-fit shrink-0 gap-x-2 border-none bg-transparent', style === 'drawer' && 'text-xl font-medium')}
        aria-label={t('device:lanWlan.wifiStats.selectBand')}
        data-testid={`${dataTestId}-trigger`}>
        <AxonSelectValue placeholder={t('device:lanWlan.wifiStats.selectBand')} data-testid={`${dataTestId}-value`} />
      </AxonSelectTrigger>
      <AxonSelectContent data-testid={`${dataTestId}-content`}>
        <AxonSelectGroup data-testid={`${dataTestId}-group`}>
          <AxonSelectLabel data-testid={`${dataTestId}-label`}>
            {t('device:lanWlan.wifiStats.selectBand')}
          </AxonSelectLabel>
          {wifiBands?.map((b) => {
            return (
              <AxonSelectItem
                key={`${b.bandName}-${b.bandId}`}
                value={b.bandId}
                data-testid={`${dataTestId}-option-${b.bandId}`}>
                {b.bandName}
              </AxonSelectItem>
            );
          })}
        </AxonSelectGroup>
      </AxonSelectContent>
    </AxonSelect>
  );
};
