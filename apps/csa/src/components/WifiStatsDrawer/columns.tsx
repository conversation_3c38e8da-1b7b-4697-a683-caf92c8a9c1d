import { useTranslation } from 'react-i18next';
import { StatusTrend } from '@/components/StatusTrend';
import { toRoundedDecimalNumber } from '@/utils/number';

export const useColumn = () => {
  const { t } = useTranslation();

  const columns = [
    { accessorKey: 'type', header: t('device:deviceInfo.cpeStats.table.header.type'), cell: (info) => info.getValue() },
    {
      accessorKey: 'minMax',
      header: t('device:deviceInfo.cpeStats.table.header.minMax'),
      cell: ({ row }) => {
        const { min, max, unit } = row.original;
        const minText = min !== null ? String(toRoundedDecimalNumber(min)) : '';
        const maxText = max !== null ? String(toRoundedDecimalNumber(max)) : '';
        return !minText && !maxText ? '' : `${minText || 'N/A'} - ${maxText || 'N/A'} ${unit}`;
      },
    },
    {
      accessorKey: 'average',
      header: t('device:deviceInfo.cpeStats.table.header.average'),
      cell: ({ row }) => {
        const { average, unit } = row.original;
        return average !== null ? `${toRoundedDecimalNumber(average)} ${unit}` : '';
      },
    },
    {
      accessorKey: 'latest',
      header: t('device:deviceInfo.cpeStats.table.header.latestResult'),
      cell: ({ row }) => {
        const { latest, unit } = row.original;
        if (typeof latest === 'string') return latest;
        return latest !== null ? `${toRoundedDecimalNumber(latest)} ${unit}` : '';
      },
    },
    {
      accessorKey: 'noOfTest',
      header: t('device:deviceInfo.cpeStats.table.header.noOfTests'),
      cell: ({ row }) => {
        const { noOfTest } = row.original;
        return noOfTest !== null ? noOfTest : '';
      },
    },
    {
      accessorKey: 'weeklyTrends',
      header: t('device:deviceInfo.cpeStats.table.header.weeklyTrend'),
      cell: ({ row }) => <StatusTrend data={row.original.weeklyTrends || []} />,
    },
  ];

  return columns;
};
