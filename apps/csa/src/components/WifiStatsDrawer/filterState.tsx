import { useConfigWidgetWifiStatistic, useTabLineId } from '@/stores/tab.store';
import get from 'lodash/get';
import { useDrawerStore, useWifiStatsDrawer } from '@/stores/drawer.store';
import { useEffect } from 'react';
import { useShallow } from 'zustand/react/shallow';
import { useGetTimeRangeByLine } from '@/stores/timeRange.store';

export function useFilterState() {
  const defaultBandId = useConfigWidgetWifiStatistic()!.selectedBandId;
  const lineId = useTabLineId();
  const timeRangeSelected = useGetTimeRangeByLine(lineId || '');
  const defaultStartDate = get(timeRangeSelected, 'startDate', null);
  const defaultEndDate = get(timeRangeSelected, 'endDate', null);

  const updateDrawer = useDrawerStore(useShallow((state) => state.updateDrawerWifiStats));

  const { searchText, startDate, endDate, open, selectedBandId } = useWifiStatsDrawer();

  useEffect(() => {
    if (!open) {
      updateDrawer({
        selectedBandId: defaultBandId,
        startDate: defaultStartDate,
        endDate: defaultEndDate,
      });
    }
  }, [open, defaultBandId, defaultEndDate, defaultStartDate, updateDrawer]);

  const changeFilter = ({
    startDate,
    endDate,
    searchText,
    selectedBandId,
  }: {
    startDate?: Date | null;
    endDate?: Date | null;
    searchText?: string;
    selectedBandId?: string;
  }) => {
    updateDrawer({
      ...(startDate !== undefined ? { startDate } : {}),
      ...(endDate !== undefined ? { endDate } : {}),
      ...(searchText !== undefined ? { searchText } : {}),
      ...(selectedBandId !== undefined ? { selectedBandId } : {}),
    });
  };
  return {
    startDate,
    endDate,
    selectedBandId,
    searchText,
    changeFilter,
  };
}
