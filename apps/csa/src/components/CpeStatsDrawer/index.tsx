import { useCpeStatsDrawer, useDrawerStore } from '@/stores/drawer.store';
import { useTabLineId } from '@/stores/tab.store';
import { cn } from '@/utils';
import { getCoreRowModel, getSortedRowModel, useReactTable } from '@tanstack/react-table';
import { kebabCase } from 'lodash';
import { Search } from 'lucide-react';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useQueryGetCpeStatsDrawer } from 'services/CPEService';
import { useGetSidebarTopology } from 'services/Topology';
import { getDayjsFromDate, getTimezone, getUnixTime } from 'services/Utils';
import {
  AxonDateRangePicker,
  AxonInput,
  AxonSelectWrapper,
  AxonSheet,
  AxonSheetContent,
  AxonTableData,
} from 'ui/UIComponents';
import { useShallow } from 'zustand/react/shallow';
import { useColumn } from './columns';
import { useFilterState } from './filterState';

type DateRange = {
  from: Date | undefined;
  to?: Date;
};

const CpeStatsDrawer = () => {
  const { t } = useTranslation();

  const lineId = useTabLineId() || '';
  const columns = useColumn();

  const { searchText, startDate, endDate, selectedDeviceId, changeFilter } = useFilterState();
  const { open } = useCpeStatsDrawer();
  const closeDrawer = useDrawerStore(useShallow((state) => state.closeDrawer));

  const { data } = useGetSidebarTopology(
    { lineId },
    {
      enabled: !!lineId,
    },
  );

  const devices = useMemo(() => {
    if (data && data.data && Array.isArray(data.data.devices)) {
      return data.data.devices;
    }

    return [];
  }, [data]);

  const {
    data: queryData,
    isLoading,
    isError,
  } = useQueryGetCpeStatsDrawer(
    {
      customerId: lineId,
      deviceId: selectedDeviceId,
      startDate: getUnixTime(startDate) || 0,
      endDate: getUnixTime(endDate) || 0,
    },
    { enabled: !!lineId && !!selectedDeviceId && !!startDate && !!endDate && !!open, staleTime: Infinity },
  );

  const parameters = useMemo(() => {
    if (queryData && queryData.data) {
      const parameters = Array.isArray(queryData?.data?.parameters) ? queryData?.data?.parameters : [];
      return parameters;
    }

    return [];
  }, [queryData]);

  const handleSetCustomTimeRange = (value?: DateRange) => {
    const startDate = getDayjsFromDate(value?.from).startOf('day').tz(getTimezone(), true).toDate();
    const endDate = getDayjsFromDate(value?.to).startOf('day').tz(getTimezone(), true).toDate();

    changeFilter({
      startDate,
      endDate,
    });
  };

  const filteredParameters = useMemo(() => {
    if (searchText) {
      return parameters.filter((p) => p.type.toLowerCase().includes(searchText.toLowerCase()));
    }

    return parameters;
  }, [parameters, searchText]);

  const table = useReactTable({
    columns,
    data: filteredParameters,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getRowId: (row) => kebabCase(row.type),
  });

  return (
    <AxonSheet
      open={open}
      onOpenChange={(open) => !open && closeDrawer('cpeStatistics')}
      data-testid='cpe-stats-drawer'>
      <AxonSheetContent
        className={cn(
          'insight-axonsheet-content lg w-full overflow-y-auto p-0 sm:max-w-5xl md:max-w-screen-lg 2xl:max-w-screen-xl',
        )}
        data-testid='cpe-stats-drawer-content'>
        <div className='flex h-screen flex-col'>
          <div className={cn('border-b-border-flat h-16 border-b px-4 py-5 text-lg font-medium')}>
            <AxonSelectWrapper
              classes={{
                selectTrigger: 'gap-xs w-fit border text-xl hover:bg-accent',
              }}
              triggerAriaLabel={t('ariaLabel.selectDevice')}
              value={selectedDeviceId}
              onChange={(newDeviceId) => changeFilter({ selectedDeviceId: newDeviceId })}
              options={devices.map((d) => {
                return {
                  label: `${t('device:deviceInfo.cpeStats.title')} ${d.deviceUniqueId}`,
                  value: d.deviceUniqueId,
                };
              })}
              data-testid='cpe-stats-drawer-device-select'
            />
          </div>
          <div className={cn('border-b-border-flat flex items-center justify-between border-b p-4')}>
            <div className='relative w-96'>
              <Search size={16} className='text-muted-foreground absolute left-3 top-3' />
              <AxonInput
                placeholder={t('device:deviceInfo.cpeStats.searchInputPlaceholder')}
                className='pl-10'
                value={searchText}
                onChange={(e) => changeFilter({ searchText: e.target.value })}
                data-testid='cpe-stats-drawer-search-input'
              />
            </div>
            <div className='ml-auto flex items-center gap-x-2'>
              <AxonDateRangePicker
                selected={{
                  from: startDate ?? undefined,
                  to: endDate ?? undefined,
                }}
                onApply={handleSetCustomTimeRange}
                showIcon={false}
                data-testid='cpe-stats-drawer-date-range-picker'
              />
            </div>
          </div>

          <div className={cn('bg-surface-section h-full')}>
            <AxonTableData
              table={table}
              showFooter={false}
              isLoading={isLoading}
              isError={isError}
              data-testid='cpe-stats-drawer-table'
            />
          </div>
        </div>
      </AxonSheetContent>
    </AxonSheet>
  );
};

export default CpeStatsDrawer;
