import { ColumnDef } from '@tanstack/react-table';
import { BoldDangerCircle } from 'ui/UIAssets';
import { useTranslation } from 'react-i18next';
import { CpeStatsDrawer } from './type.remote';
import { StatusTrend } from '../StatusTrend';

export const useColumn = () => {
  const { t } = useTranslation();

  const columns: ColumnDef<CpeStatsDrawer>[] = [
    {
      header: t('device:deviceInfo.cpeStats.table.header.type'),
      accessorKey: 'type',
      cell: ({ row }) => {
        const { type } = row.original;
        return <span className='text-md text-content-primary font-medium'>{type}</span>;
      },
    },
    {
      header: t('device:deviceInfo.cpeStats.table.header.minMax'),
      accessorKey: `minMax`,
      cell: ({ row }) => {
        const { minMax, unit } = row.original;
        return <span className='text-md text-content-primary font-normal'>{minMax ? `${minMax} ${unit}` : '-'}</span>;
      },
    },
    {
      header: t('device:deviceInfo.cpeStats.table.header.average'),
      accessorKey: 'average',
      cell: ({ row }) => {
        const { average, unit } = row.original;
        return (
          <span className='text-md text-content-primary font-normal'>
            {typeof average !== 'undefined' && average !== null ? `${average} ${unit}` : '-'}
          </span>
        );
      },
    },
    {
      header: t('device:deviceInfo.cpeStats.table.header.latestResult'),
      accessorKey: 'latestResult',
      cell: ({ row }) => {
        const { latestResult, unit, hasWarning } = row.original;
        let valueText = '-';
        switch (typeof latestResult) {
          case 'number':
            valueText = `${latestResult} ${unit}`;
            break;
          case 'string':
            valueText = t(latestResult);
            break;
        }

        return (
          <div className='flex flex-col items-start gap-2'>
            <span className='text-content-primary'>{valueText}</span>
            {hasWarning && (
              <div className='flex items-center gap-1'>
                <BoldDangerCircle color='rgb(var(--content-meta-red))' />
                <span className='text-content-meta-red text-xs font-medium'>
                  {t('device:deviceInfo.cpeStats.table.body.warning')}
                </span>
                <span className='font-book text-xs opacity-60'>
                  {t('device:deviceInfo.cpeStats.table.body.tooHigh')}
                </span>
              </div>
            )}
          </div>
        );
      },
    },
    {
      header: t('device:deviceInfo.cpeStats.table.header.noOfTests'),
      accessorKey: 'noOfTest',
      cell: ({ row }) => {
        const { noOfTest } = row.original;
        return <span className='text-md text-content-primary font-normal'>{noOfTest}</span>;
      },
    },
    {
      accessorKey: 'weeklyTrend',
      header: t('device:deviceInfo.cpeStats.table.header.weeklyTrend'),
      cell: ({ row }) => <StatusTrend data={row.original.weeklyTrend || []} />,
    },
  ];

  return columns;
};
