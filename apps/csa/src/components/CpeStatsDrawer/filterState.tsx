import { useTabDeviceId, useTabLineId } from '@/stores/tab.store';
import get from 'lodash/get';
import { useCpeStatsDrawer, useDrawerStore } from '@/stores/drawer.store';
import { useEffect } from 'react';
import { useShallow } from 'zustand/react/shallow';
import { useGetTimeRangeByLine } from '@/stores/timeRange.store';

export function useFilterState() {
  const lineId = useTabLineId() || '';
  const timeRangeSelected = useGetTimeRangeByLine(lineId);
  const defaultStartDate = get(timeRangeSelected, 'startDate', null);
  const defaultEndDate = get(timeRangeSelected, 'endDate', null);

  const defaultDeviceId = useTabDeviceId() || '';

  const updateDrawer = useDrawerStore(useShallow((state) => state.updateDrawerCpeStats));

  const { searchText, startDate, endDate, open, selectedDeviceId } = useCpeStatsDrawer();

  useEffect(() => {
    if (!open) {
      updateDrawer({
        searchText: '',
        startDate: defaultStartDate,
        endDate: defaultEndDate,
        selectedDeviceId: defaultDeviceId,
      });
    }
  }, [open, defaultEndDate, defaultStartDate, defaultDeviceId, updateDrawer]);

  const changeFilter = ({
    startDate,
    endDate,
    searchText,
    selectedDeviceId,
  }: {
    startDate?: Date | null;
    endDate?: Date | null;
    searchText?: string;
    selectedDeviceId?: string;
  }) => {
    updateDrawer({
      ...(startDate !== undefined ? { startDate } : {}),
      ...(endDate !== undefined ? { endDate } : {}),
      ...(searchText !== undefined ? { searchText } : {}),
      ...(selectedDeviceId !== undefined ? { selectedDeviceId } : {}),
    });
  };
  return {
    searchText,
    startDate,
    endDate,
    selectedDeviceId,
    changeFilter,
  };
}
