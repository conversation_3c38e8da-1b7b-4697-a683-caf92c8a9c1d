import useTabStore from '@/stores/tab.store';
import useTimeRangeStore, {
  ETimeRangeUnit,
  useInitLineTimeRange,
  useRemoveTimeRangeByLine,
} from '@/stores/timeRange.store';
import { useEffect } from 'react';

const useSyncTimeRangeStoreAndTabStore = () => {
  const allTabs = useTabStore((state) => state.allTabs);
  const timeRanges = useTimeRangeStore((state) => state.timeRanges);
  const removeTimeRangeByLine = useRemoveTimeRangeByLine();
  const initLineTimeRange = useInitLineTimeRange();

  useEffect(() => {
    // remove time range from time range store if tab is not present
    Object.keys(timeRanges).forEach((lineId) => {
      if (!allTabs.some((tab) => tab.customerId === lineId)) {
        removeTimeRangeByLine(lineId);
      }
    });
  }, [allTabs, timeRanges, removeTimeRangeByLine]);

  useEffect(() => {
    //  Init time range store for new tab if the tab is not present in time range store
    allTabs.forEach((tab) => {
      if (tab.customerId && !timeRanges[tab.customerId]) {
        initLineTimeRange(tab.customerId, {
          unit: ETimeRangeUnit.YESTERDAY,
        });
      }
    });
  }, [allTabs, timeRanges, initLineTimeRange]);
};

export default useSyncTimeRangeStoreAndTabStore;
