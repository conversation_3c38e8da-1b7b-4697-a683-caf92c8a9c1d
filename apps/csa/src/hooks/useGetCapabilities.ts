import { useTabDeviceId, useTabLineId, useTabType } from '@/stores/tab.store';
import { useGetCpeInfo } from 'services/CPEService';
import get from 'lodash/get';
import { useGetLineInfo } from 'services/LineInfo';

export const useGetCapabilities = () => {
  const deviceId = useTabDeviceId() || '';
  const lineId = useTabLineId();
  const { data: cpeInfo } = useGetCpeInfo(deviceId, { enabled: !!deviceId });
  const { data: lineInfo } = useGetLineInfo(deviceId || lineId || '');
  const tabType = useTabType();
  const capabilities =
    tabType === 'device' ? get(cpeInfo, 'data.config.capabilities') : get(lineInfo, 'data.config.capabilities');

  return capabilities;
};
