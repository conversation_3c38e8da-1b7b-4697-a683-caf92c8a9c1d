import { useTabDeviceId, useTabLineId, useTabType } from '@/stores/tab.store';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import get from 'lodash/get';
import { useEffect } from 'react';
import { useGetCpeInfo } from 'services/CPEService';
import { useGetLineInfo } from 'services/LineInfo';
import { setTimezone } from 'services/Utils';

export const useSyncTimeZone = () => {
  return useQuery({
    queryKey: ['timezone'],
    queryFn: () => {
      return '';
    },
  });
};
export const useUpdateTimeZone = () => {
  const queryClient = useQueryClient();

  const lineId = useTabLineId();
  const deviceId = useTabDeviceId();
  const type = useTabType();
  const tabType = useTabType();

  const { data: lineInfo } = useGetLineInfo(lineId || '', {
    enabled: type === 'customer' && !!lineId,
  });

  const { data: cpeInfo } = useGetCpeInfo(deviceId || '', {
    enabled: type === 'device' && !!deviceId,
  });

  const lineTimezone = get(lineInfo, 'data.isp.timezone');
  const cpeTimezone = get(cpeInfo, 'data.timezone');

  const timezone = tabType === 'customer' ? lineTimezone : cpeTimezone;

  useEffect(() => {
    if (timezone) {
      setTimezone(timezone);
      localStorage.setItem('timezone', timezone);
      queryClient.setQueryData(['timezone'], timezone);
    }
  }, [timezone, queryClient]);
};
