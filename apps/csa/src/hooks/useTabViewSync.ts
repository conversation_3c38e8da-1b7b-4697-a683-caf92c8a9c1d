import { DrawerType, useDrawerStore, useOpeningDrawer } from '@/stores/drawer.store';
import useTabStore, {
  useConfigHealthCheck,
  useConfigNetworkTopology,
  useConfigScrollTo,
  useTabDeviceId,
  useTabId,
  useTabLineId,
} from '@/stores/tab.store';
import { useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { useShallow } from 'zustand/react/shallow';

const sliceToNumber = (slice: string): number | null => {
  const map: Record<string, number> = {
    wan: 0,
    wlan: 1,
    clients: 2,
    services: 3,
    cpe: 4,
  };
  return map[slice] ?? null;
};

const sliceToName = (index: number): string => {
  const list = ['wan', 'wlan', 'clients', 'services', 'cpe'];
  return list[index] ?? '';
};

export const useTabViewSync = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const enabled = process.env.ENABLE_SYNC_URL === 'true' || process.env.NODE_ENV === 'production';

  const lineId = useTabLineId();
  const deviceId = useTabDeviceId();
  const tabId = useTabId();
  const addTab = useTabStore((state) => state.addTab);
  const setActiveTab = useTabStore((state) => state.setActiveTab);
  const clearTabs = useTabStore((state) => state.clearTabs);
  const setIsHydrated = useTabStore((state) => state.setIsHydrated);
  const isHydrated = useTabStore((state) => state.isHydrated);
  const healthCheckConfig = useConfigHealthCheck();
  const scrollTo = useConfigScrollTo();
  const networkTopology = useConfigNetworkTopology();

  const { openDrawer } = useDrawerStore(
    useShallow((state) => ({
      openDrawer: state.openDrawer,
    })),
  );

  const openingDrawer = useOpeningDrawer();

  useEffect(() => {
    // sync tab view from url params
    if (!enabled) return;

    const customerId = searchParams.get('customerId');
    const deviceId = searchParams.get('deviceId');
    const slice = searchParams.get('slice');
    const drawerName = searchParams.get('drawer');
    const isTopologyOpen = searchParams.get('topologyOpen') === 'true';

    if (!customerId) {
      setIsHydrated(true);
      return;
    }

    const id = deviceId || customerId;
    const type = deviceId ? 'device' : 'customer';

    addTab({
      id,
      name: id,
      type,
      customerId,
      deviceId: deviceId || null,
      config: {
        networkTopology: isTopologyOpen ? { open: true } : undefined,
        healthCheck: slice ? { selectedSlice: sliceToNumber(slice) } : undefined,
      },
    });
    setActiveTab({ tabId: id, tabType: type });
    if (drawerName) openDrawer(drawerName as DrawerType);
    setIsHydrated(true);
  }, [searchParams, addTab, setActiveTab, clearTabs, setIsHydrated, enabled, openDrawer]);

  useEffect(() => {
    // sync url params from tab view
    if (!isHydrated || !enabled) return;

    if (!tabId) {
      navigate('/csa', { replace: true });
      return;
    }

    const params = new URLSearchParams();

    if (lineId) params.set('customerId', lineId);
    if (deviceId) params.set('deviceId', deviceId);

    const slice = healthCheckConfig?.selectedSlice;
    if (slice !== undefined && slice !== null) {
      params.set('slice', sliceToName(slice));
    }

    // Sync drawer state
    if (openingDrawer !== null) params.set('drawer', openingDrawer);

    const topologyOpen = networkTopology?.open;
    if (topologyOpen) params.set('topologyOpen', 'true');

    const newQuery = params.toString();
    if (window.location.search !== `?${newQuery}`) {
      navigate(`/csa?${newQuery}`, { replace: true });
    }
  }, [
    scrollTo,
    healthCheckConfig,
    isHydrated,
    navigate,
    enabled,
    lineId,
    deviceId,
    tabId,
    networkTopology,
    openingDrawer,
  ]);
};
