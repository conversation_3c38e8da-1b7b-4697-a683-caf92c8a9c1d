export const CPESections = {
  CPE: 'cpe',
  LAN_WLAN: 'lanWlan',
  CLIENTS: 'clients',
  WAN: 'wan',
  SERVICES: 'services',
};

export const CPEWidgets = {
  WAN_STATISTICS: 'wanStatistics',
  WIFI_BANDS: 'wifiBands',
  LAN_PORTS: 'lanPorts',
};

// Map each widget to its corresponding section
export const WIDGET_TO_SECTION = {
  [CPEWidgets.WAN_STATISTICS]: CPESections.WAN,
  [CPEWidgets.WIFI_BANDS]: CPESections.LAN_WLAN,
  [CPEWidgets.LAN_PORTS]: CPESections.LAN_WLAN,
};

export type CPESection = (typeof CPESections)[keyof typeof CPESections];
export type CPEWidget = (typeof CPEWidgets)[keyof typeof CPEWidgets];
