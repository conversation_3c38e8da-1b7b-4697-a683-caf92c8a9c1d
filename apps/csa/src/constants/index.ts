import { TRealtimeMetric } from '@/types/realtime.type';

export const METRIC = {
  LINE: ['operational', 'wifiScan', 'wifiSpeed', 'broadbandSpeed'],
  WIFI_STATISTIC: ['wifiSpeed'],
  WAN_STATISTIC: ['broadbandSpeed'],
} satisfies Record<string, TRealtimeMetric[]>;

export const REQUIRE_METRIC = {
  LINE: ['operational'],
  WIFI_STATISTIC: ['wifiSpeed'],
  WAN_STATISTIC: ['broadbandSpeed'],
} satisfies Record<string, TRealtimeMetric[]>;
