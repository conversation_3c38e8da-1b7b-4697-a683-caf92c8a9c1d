import { SortingState } from '@tanstack/react-table';

const ALL_VALUE_OPTION = 'all';

type ConnectionType = 'ethernet' | 'wifi';
interface CpeClient {
  name: string;
  mac: string;
  type: string;
}

interface CpeBand {
  bandId: string;
  band: string;
  label: string;
  networkType: string;
  connectionType: ConnectionType;
}

export enum ClientHistoryEMetric {
  WIFI_PHY_RATE = 'phyRate',
  WIFI_THROUGHPUT = 'throughput',
  TRAFFIC_DOWN = 'trafficDown',
  TRAFFIC_UP = 'trafficUp',
  LATENCY = 'latency',
  SNR = 'snr',
  RSSI = 'rssi',
  QOE = 'qoe',
}

export enum LogDrawerTabs {
  Logs = 'logs',
  LogList = 'logList',
  LogChart = 'logChart',
}

export enum CpeHistoryChartMetric {
  CPU_MEMORY_USAGE = 'cpuMemoryUsage',
  CPU_LOAD = 'cpuLoad',
  CHIPSET_TEMPERATURE = 'chipsetTemperature',
  POWER_CYCLE = 'powerCycle',
  QOE = 'qoe',
  RSSI = 'rssi',
  DISCONNECTION_EVENT = 'disconnEvents',
}

export enum WifiStatisticChartMetric {
  RSSI = 'rssi',
  DISCONNECTION_EVENT = 'disconnEvents',
}

export const BASE_WAN_HISTORY_METRICS = {
  QOE: 'qoe',
  STATUS: 'status',
  SPEED: 'speed',
  LATENCY: 'latency',
  JITTER: 'jitter',
  TRAFFIC: 'traffic',
  TRANSMISSION_ERROR: 'transmissionError',
  PACKET_LOST: 'packetLost',
  INTERNET_USAGE: 'internetUsage',
};

export enum LanWlanHistoryType {
  QOE = 'wifiQoE',
  CHANNEL = 'channel',
  CONNECTIVITY = 'connectivity',
  WIFI_SPEED = 'wifiSpeed',
  WIFI_TRANMISSION = 'wifiTransmissionErrors',
  WIFI_CHANNEL_NOISE = 'wifiChannelNoise',
  WIFI_CHIPSET_TEMPERATURE = 'wifiChannelUtilization',
  WIFI_TRAFFIC = 'wifiTraffic',
  ETHERNET_TRAFFIC = 'ethernetTraffic',
  WIFI_CHANNEL_INTERFERENCE = 'wifiChannelInterference',
}

export const CELLULAR_WAN_HISTORY_METRICS = {
  LTE_CELL_ID: 'lteCellId',
  LTE_BAND: 'lteBand',
  '5G_BAND': '5gBand',
  VOICE_REDUNDANCY: 'voiceRedundancy',
  LTE_RSRP: 'lteRsrp',
  LTE_RSRQ: 'lteRsrq',
  '5G_RSRP': '5gRsrp',
  '5G_RSRQ': '5gRsrq',
};

export type WidgetsConfigKey = keyof WidgetsConfig;
export const DRAWER_TYPE = {
  INSIGHT: 'insight',
  WORKBENCH: 'workbench',
  CPE_STATISTICS: 'cpeStatistic',
  WIFI_STATISTICS: 'wifiStatistic',
  WAN_STATISTICS: 'wanStatistic',
  CLIENT_CONNECTION: 'clientConnection',
  EVENT: 'event',
  SERVICE_SUMMARY: 'serviceSummary',
  SERVICE_STATS: 'serviceStatistic',
};

export type WidgetsConfig = {
  cpeHistory?: {
    selectedMetric: CpeHistoryChartMetric;
  };
  wifiStatistic?: {
    selectedBandId: string;
  };
  lanPorts?: {
    sorting: SortingState;
  };
  wifiBands?: {
    sorting: SortingState;
  };
  lanWlanHistory?: {
    filterState: {
      metric: LanWlanHistoryType;
      bandIds: string[] | null;
    };
  };
  wanHistory?: {
    selectedMetric: string;
  };
  clientConnection?: {
    filterState: {
      search: string;
      cpeId: string;
      deviceType: string;
      connectionInterface: string;
      parentalControl: string;
      status: 'online' | 'offline' | '' | typeof ALL_VALUE_OPTION;
    };
    sorting: SortingState;
  };
  clientStats?: {
    globalFilter: string;
    device: {
      cpeId: string;
      stationMac: string;
    };
  };
  clientHistory?: {
    filterState: {
      client: CpeClient[] | null;
      metric: ClientHistoryEMetric;
      band: CpeBand[] | null;
      cpeIds: string[] | null;
    };
  };
  serviceHistory?: {
    selectedMetric: string;
  };
};

export type DeepReadonly<T> = {
  readonly [P in keyof T]: DeepReadonly<T[P]>;
};

export const defaultWidgetsConfig: DeepReadonly<WidgetsConfig> = {
  cpeHistory: {
    selectedMetric: CpeHistoryChartMetric.QOE,
  },
  wifiStatistic: {
    selectedBandId: '',
  },
  lanPorts: {
    sorting: [],
  },
  wifiBands: {
    sorting: [],
  },
  lanWlanHistory: {
    filterState: {
      bandIds: [],
      metric: LanWlanHistoryType.QOE,
    },
  },

  wanHistory: {
    selectedMetric: BASE_WAN_HISTORY_METRICS.STATUS,
  },
  clientConnection: {
    filterState: {
      search: '',
      cpeId: ALL_VALUE_OPTION,
      deviceType: ALL_VALUE_OPTION,
      connectionInterface: ALL_VALUE_OPTION,
      parentalControl: ALL_VALUE_OPTION,
      status: ALL_VALUE_OPTION,
    },
    sorting: [],
  },
  clientStats: {
    globalFilter: '',
    device: {
      cpeId: '',
      stationMac: '',
    },
  },
  clientHistory: {
    filterState: {
      client: null,
      metric: ClientHistoryEMetric.RSSI,
      band: null,
      cpeIds: null,
    },
  },
  serviceHistory: {
    selectedMetric: BASE_WAN_HISTORY_METRICS.SPEED,
  },
};
