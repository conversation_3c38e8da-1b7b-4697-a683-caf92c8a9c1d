import lodashSet from 'lodash/set';
import { create } from 'zustand';
import { createJSONStorage, devtools, persist } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import lodashGet from 'lodash/get';
import { TRealtimeMetric } from '../types/realtime.type';
import { METRIC, REQUIRE_METRIC } from '@/constants';
import { pollRealtimeProcess } from 'services/Realtime';
import { dayjsTz } from 'services/Utils';
import useTimeRangeStore, { ETimeRangeUnit } from './timeRange.store';
import type { LineRealtimeMetaData, RequestInfo, StepState } from './realtime.type';

interface RealtimeStore {
  config: {
    [lineId: string]: {
      isBusy: boolean;
      line: LineRealtimeMetaData;
      requestInfo: RequestInfo[];
    };
  };
  selection: {
    [lineId: string]: {
      [key in keyof typeof REQUIRE_METRIC]?: string;
    };
  };
  updateRealtimeConfig: (path: string | string[], value: any) => void;
  updateRequestIdSelection: (lineId: string, type: keyof typeof REQUIRE_METRIC, requestId: string) => void;
  addRequestInfo: (lineId: string, requestInfo: RequestInfo) => void;
  updateRealtimeState: (lineId: string, step: StepState, metaData: any) => void;
  cleanupExpiredHistoricals: (isHydrated: boolean) => void;
  updateOnDemand: (lineId: string, value: boolean) => void;
}

const useRealtimeConfigStore = create<RealtimeStore>()(
  persist(
    immer(
      devtools(
        (set, get): RealtimeStore => ({
          config: {},
          selection: {},
          updateRequestIdSelection: (lineId: string, type: keyof typeof REQUIRE_METRIC, requestId: string) => {
            set(
              (state) => {
                lodashSet(state.selection, [lineId, type], requestId);
              },
              false,
              'updateSelection',
            );
          },
          addRequestInfo: (lineId: string, requestInfo: RequestInfo) => {
            set(
              (state) => {
                // add requestInfo to existing requestInfo.
                const requestInfos = state.config[lineId]?.requestInfo || [];
                lodashSet(state.config, [lineId, 'requestInfo'], [...requestInfos, requestInfo]);
              },
              false,
              'addRealtimeRequestInfo',
            );
          },

          updateRealtimeState: (lineId: string, step: StepState, metaData: any) => {
            set(
              (state) => {
                if (step === 'INIT') {
                  // add new request.
                  const newRequestInfo = {
                    metric: metaData.metric,
                    isRealtimeRequesting: true,
                    isDataReady: false,
                    lastUpdated: null,
                    currentStepId: null,
                    currentStepText: null,
                    progress: 0,
                  };
                  const requestInfo = state.config[lineId]?.requestInfo || [];
                  lodashSet(state.config, [lineId, 'requestInfo'], [...requestInfo, newRequestInfo]);
                  lodashSet(state.config, [lineId, 'isBusy'], true);
                } else {
                  const listRequestInfo = lodashGet(state.config, [lineId, 'requestInfo']);
                  const lastIndex = listRequestInfo.length - 1;

                  if (step === 'IN_PROGRESS') {
                    lodashSet(
                      state.config,
                      [lineId, 'requestInfo', lastIndex, 'currentStepId'],
                      metaData.currentStepId,
                    );
                    lodashSet(
                      state.config,
                      [lineId, 'requestInfo', lastIndex, 'currentStepText'],
                      metaData.currentStepText,
                    );
                    lodashSet(state.config, [lineId, 'requestInfo', lastIndex, 'progress'], metaData.progress);
                    lodashSet(
                      state.config,
                      [lineId, 'requestInfo', lastIndex, 'realtimeRequestId'],
                      metaData.realtimeRequestId,
                    );
                  }
                  if (step === 'SUCCESS') {
                    // update selected request for all widget which are related to the current request.
                    const listRequest = lodashGet(state.config, [lineId, 'requestInfo']);
                    const currentRequest = listRequest.at(-1)!;
                    const currentMetricRequest = currentRequest.metric;

                    Object.keys(METRIC).forEach((key) => {
                      const isRelated = currentMetricRequest.some((m) => REQUIRE_METRIC[key].includes(m));
                      if (isRelated) {
                        lodashSet(state.selection, [lineId, key], currentRequest.realtimeRequestId);
                      }
                    });

                    lodashSet(state.config, [lineId, 'requestInfo', lastIndex, 'isDataReady'], true);
                    lodashSet(state.config, [lineId, 'requestInfo', lastIndex, 'lastUpdated'], Date.now());
                    lodashSet(
                      state.config,
                      [lineId, 'requestInfo', lastIndex, 'ttl'],
                      dayjsTz().endOf('day').valueOf(),
                    );
                  }
                  if (step === 'ERROR') {
                    // remove the request
                    state.config[lineId].requestInfo.pop();
                  }
                  if (step === 'CLEANUP') {
                    lodashSet(state.config, [lineId, 'isBusy'], false);
                    lodashSet(state.config, [lineId, 'requestInfo', lastIndex, 'isRealtimeRequesting'], false);
                  }
                }
              },
              false,
              'updateRealtimeRequestInfo',
            );
          },
          updateRealtimeConfig: (path: string | string[], value: any) => {
            set(
              (state) => {
                lodashSet(state.config, path, value);
              },
              false,
              'updateRealtimeConfig',
            );
          },
          updateOnDemand: (lineId: string, value: boolean) =>
            set((draftState) => {
              lodashSet(draftState.config, [lineId, 'line', 'isOnDemand'], value);
              // update time range to today if on-demand is true, else update to yesterday.
              const updaterTimeRange = useTimeRangeStore.getState().updateLineTimeRange;
              if (value) {
                updaterTimeRange(lineId, {
                  unit: ETimeRangeUnit.TODAY,
                });
              } else {
                updaterTimeRange(lineId, {
                  unit: ETimeRangeUnit.YESTERDAY,
                });
              }
            }),
          cleanupExpiredHistoricals: (isOnHydrate: boolean) =>
            set((state) => {
              const now = Date.now();
              const config = state.config;
              if (config) {
                // only need to check first element of historical since ttl is all the same.
                for (const lineId in config) {
                  const requests = config[lineId]?.requestInfo;
                  if (requests?.length > 0) {
                    const isExpired = requests.some((request) => {
                      return request.ttl < now;
                    });
                    if (isExpired) {
                      delete state.config[lineId];
                      delete state.selection[lineId];
                    } else {
                      if (isOnHydrate) {
                        const isLineBusy = state.config[lineId].isBusy;
                        if (isLineBusy) {
                          // only fetching for active tab.
                          const lastRequest = requests.at(-1);
                          if (lastRequest && lastRequest.realtimeRequestId) {
                            pollRealtimeProcess(lineId, lastRequest.realtimeRequestId, state.updateRealtimeState).then(
                              (result) => {
                                // must use global state instead of draft state inside of async function.
                                const updateRealtimeState = get().updateRealtimeState;
                                const updateOnDemand = get().updateOnDemand;
                                if (result.status === 'SUCCESS') {
                                  updateRealtimeState(lineId, 'SUCCESS', {});
                                  updateOnDemand(lineId, true);
                                } else {
                                  updateRealtimeState(lineId, 'ERROR', {});
                                }
                                updateRealtimeState(lineId, 'CLEANUP', {});
                              },
                            );
                          }
                        }
                      }
                    }
                  }
                }
              }
            }),
        }),
        { name: 'csa-RealtimeConfig', store: 'csa-RealtimeConfig' },
      ),
    ),
    {
      name: 'csa-RealtimeConfig',
      storage: createJSONStorage(() => localStorage),
      partialize: (state) => ({
        config: state.config,
        selection: state.selection,
      }),

      onRehydrateStorage: () => (state) => {
        if (state) {
          state.cleanupExpiredHistoricals(true);
        }
      },
    },
  ),
);

export const useRealtimeConfig = (lineId: string) => useRealtimeConfigStore((state) => state.config[lineId]);

export const useRealtimeLineConfig = (lineId: string): LineRealtimeMetaData | undefined =>
  useRealtimeConfigStore((state) => lodashGet(state.config[lineId], 'line'));

/**
 * Get selected request info by lineId and typeWidget
 * If there is user selected id existed, return that request.
 * If there is no user selected id existed, find the last requestInfo which is in processing (last element which provided metric)
 * @param lineId - line id
 * @param typeWidget - type of widget
 * @returns selected request info
 */
export const useGetSelectedRequestInfo = (
  lineId: string,
  typeWidget: keyof typeof REQUIRE_METRIC,
): RequestInfo | undefined => {
  const listRequest = useGetListRequestInfo(lineId, typeWidget);
  return useRealtimeConfigStore((state) => {
    const lastRequest = listRequest.at(-1);
    const isExistRequestProcessing = Boolean(lastRequest?.isRealtimeRequesting);
    if (isExistRequestProcessing) {
      // prefer to use realtime data if available
      return lastRequest;
    }
    const selectedId = lodashGet(state.selection[lineId], typeWidget);
    if (selectedId) {
      return listRequest.find((request) => request.realtimeRequestId === selectedId);
    }
  });
};

export const useSelectedRequestId = (lineId: string, type: keyof typeof REQUIRE_METRIC) =>
  useRealtimeConfigStore((state) => lodashGet(state.selection[lineId], `${type}`));

export const useGetListRequestInfo = (lineId: string, type: keyof typeof REQUIRE_METRIC): RequestInfo[] =>
  useRealtimeConfigStore((state) => {
    const requiredMetrics: TRealtimeMetric[] = REQUIRE_METRIC[type];
    const requestInfo = lodashGet(state.config[lineId], 'requestInfo') || [];
    return requestInfo.filter((request) => {
      return requiredMetrics.every((m) => request.metric.includes(m));
    });
  });
export const useLineIsBusy = (lineId: string) =>
  useRealtimeConfigStore((state) => lodashGet(state.config[lineId], 'isBusy', false));

export const useIsOnDemand = (lineId: string) =>
  useRealtimeConfigStore((state) => lodashGet(state.config[lineId], 'line.isOnDemand', false));

export const useIsOnFly = (lineId: string) =>
  useRealtimeConfigStore((state) => lodashGet(state.config[lineId], 'line.isOnFly', false));

export default useRealtimeConfigStore;
