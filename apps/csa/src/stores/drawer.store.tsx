import { LogDrawerTabs } from '@/stores/widgets.config';
import { SortingState } from '@tanstack/react-table';
import last from 'lodash/last';
import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';

export type DrawerType =
  | 'workbench'
  | 'cpeStatistics'
  | 'wifiStatistics'
  | 'wanStatistics'
  | 'clientConnection'
  | 'serviceSummary'
  | 'serviceStats'
  | 'insight'
  | 'event';

type Updatable<T> = Partial<Omit<T, 'open'>>;

type DrawerBaseState = {
  open: boolean;
};

type DrawerCommonFilter = {
  searchText: string;
  startDate: Date | null;
  endDate: Date | null;
};

type FilteredDrawerBaseState = DrawerBaseState & DrawerCommonFilter;

type DrawerWifiState = FilteredDrawerBaseState & {
  selectedBandId: string;
};

type DrawerWanState = FilteredDrawerBaseState;

type DrawerCpeState = FilteredDrawerBaseState & {
  selectedDeviceId: string;
};

type DrawerWorkbenchState = FilteredDrawerBaseState & {
  showActiveClient: boolean;
  showInactiveClient: boolean;
  expanded: true | Record<string, boolean>;
};

type DrawerServiceSummaryState = FilteredDrawerBaseState & {
  serviceTypes: string[];
  sorting: SortingState;
};
type DrawerServiceStatsState = FilteredDrawerBaseState & {
  // Additional state depends on drawer type
};

type DrawerInsightState = FilteredDrawerBaseState & {
  selectedDeviceId: string;
  category: string;
  severity: string;
  sortTime: string;
  type: string;
  stationMac: string;
};

type DrawerEventState = FilteredDrawerBaseState & {
  sourceFilter: string;
  eventTypeFilter: string;
  cpeIdFilter: string;
  viewMode: LogDrawerTabs;
};

type DrawerClientConnectionState = FilteredDrawerBaseState & {
  cpeId: string;
  deviceType: string;
  connectionInterface: string;
  parentalControl: string;
  sorting: SortingState;
  status: 'online' | 'offline' | 'all' | '';
  stats: DrawerCommonFilter & {
    cpeId: string;
    stationMac: string;
  };
};

interface DrawerStoreState {
  openHistory: DrawerType[];
  wifiStatistics: DrawerWifiState;
  wanStatistics: DrawerWanState;
  cpeStatistics: DrawerCpeState;
  workbench: DrawerWorkbenchState;
  serviceSummary: DrawerServiceSummaryState;
  serviceStats: DrawerServiceStatsState;
  insight: DrawerInsightState;
  event: DrawerEventState;
  clientConnection: DrawerClientConnectionState;
}

interface DrawerStoreAction {
  openDrawer: (type: DrawerType) => void;
  closeDrawer: (type: DrawerType) => void;
  updateDrawerCpeStats: (data: Updatable<DrawerCpeState>) => void;
  updateDrawerWanStats: (data: Updatable<DrawerWanState>) => void;
  updateDrawerWifiStats: (data: Updatable<DrawerWifiState>) => void;
  updateDrawerWorkbench: (data: Updatable<DrawerWorkbenchState>) => void;
  updateDrawerServiceSummary: (data: Updatable<DrawerServiceSummaryState>) => void;
  updateDrawerServiceStats: (data: Updatable<DrawerServiceStatsState>) => void;
  updateDrawerInsight: (data: Updatable<DrawerInsightState>) => void;
  updateDrawerEvent: (data: Updatable<DrawerEventState>) => void;
  updateDrawerClientConnection: (data: Updatable<DrawerClientConnectionState>) => void;
  updateDrawerClientConnectionStats: (data: Updatable<DrawerClientConnectionState['stats']>) => void;
  clearDrawerClientConnection: () => void;
}

const defaultDrawerBaseState: DrawerBaseState = {
  open: false,
};

const defaultDrawerCommonFilter: DrawerCommonFilter = {
  searchText: '',
  startDate: null,
  endDate: null,
};

const defaultDrawerState: FilteredDrawerBaseState = {
  ...defaultDrawerBaseState,
  ...defaultDrawerCommonFilter,
};

const defaultDrawerClientConnectionState: DrawerClientConnectionState = {
  ...defaultDrawerState,
  cpeId: 'all',
  deviceType: 'all',
  connectionInterface: 'all',
  parentalControl: 'all',
  sorting: [],
  status: 'all',
  stats: {
    ...defaultDrawerCommonFilter,
    cpeId: '',
    stationMac: '',
  },
};

export const useDrawerStore = create<DrawerStoreState & DrawerStoreAction>()(
  immer(
    devtools(
      (set) => ({
        /* ----------------------- Store Default State ----------------------- */
        openHistory: [],
        wifiStatistics: {
          ...defaultDrawerState,
          selectedBandId: '',
        },
        wanStatistics: {
          ...defaultDrawerState,
        },
        cpeStatistics: {
          ...defaultDrawerState,
          selectedDeviceId: '',
        },
        workbench: {
          ...defaultDrawerState,
          showActiveClient: true,
          showInactiveClient: true,
          expanded: true,
        },
        serviceSummary: {
          ...defaultDrawerState,
          serviceTypes: [],
          sorting: [],
        },
        serviceStats: {
          ...defaultDrawerState,
        },
        insight: {
          ...defaultDrawerState,
          selectedDeviceId: '',
          category: '',
          severity: '',
          sortTime: 'default',
          type: '',
          stationMac: '',
        },
        event: {
          ...defaultDrawerState,
          sourceFilter: 'all',
          eventTypeFilter: 'all',
          cpeIdFilter: 'all',
          viewMode: LogDrawerTabs.Logs,
        },
        clientConnection: defaultDrawerClientConnectionState,
        /* ----------------------- Store Action ----------------------- */
        openDrawer: (type: DrawerType) => {
          set((state) => {
            if (type === 'serviceStats') {
              state['serviceSummary'].open = false;
              state.openHistory = state.openHistory.filter((item) => item !== 'serviceSummary');
            } else if (type === 'serviceSummary') {
              state['serviceStats'].open = false;
              state.openHistory = state.openHistory.filter((item) => item !== 'serviceStats');
            }

            state[type].open = true;
            state.openHistory.push(type);
          });
        },

        closeDrawer: (type: DrawerType) => {
          set((state) => {
            state[type].open = false;

            if (type === 'serviceStats') {
              state['serviceSummary'].open = false;
              state.openHistory = state.openHistory.filter((item) => item !== type && item !== 'serviceSummary');
            } else if (type === 'serviceSummary') {
              state['serviceStats'].open = false;
              state.openHistory = state.openHistory.filter((item) => item !== type && item !== 'serviceStats');
            } else {
              state.openHistory = state.openHistory.filter((item) => item !== type);
            }
          });
        },
        updateDrawerCpeStats: (payload: Updatable<DrawerCpeState>) => {
          set(
            (state) => {
              state['cpeStatistics'] = {
                ...state['cpeStatistics'],
                ...payload,
              };
            },
            undefined,
            'updateDrawerCpeStats',
          );
        },
        updateDrawerWanStats: (payload: Updatable<DrawerWanState>) => {
          set(
            (state) => {
              state['wanStatistics'] = {
                ...state['wanStatistics'],
                ...payload,
              };
            },
            undefined,
            'updateDrawerWanStats',
          );
        },
        updateDrawerWifiStats: (payload: Updatable<DrawerWifiState>) => {
          set(
            (state) => {
              state['wifiStatistics'] = {
                ...state['wifiStatistics'],
                ...payload,
              };
            },
            undefined,
            'updateDrawerWifiStats',
          );
        },
        updateDrawerWorkbench: (payload: Updatable<DrawerWorkbenchState>) => {
          set(
            (state) => {
              state['workbench'] = {
                ...state['workbench'],
                ...payload,
              };
            },
            undefined,
            'updateDrawerWorkbench',
          );
        },
        updateDrawerServiceSummary: (payload: Updatable<DrawerServiceSummaryState>) => {
          set(
            (state) => {
              state['serviceSummary'] = {
                ...state['serviceSummary'],
                ...payload,
              };
            },
            undefined,
            'updateDrawerServiceSummary',
          );
        },
        updateDrawerServiceStats: (payload: Updatable<DrawerServiceStatsState>) => {
          set(
            (state) => {
              state['serviceStats'] = {
                ...state['serviceStats'],
                ...payload,
              };
            },
            undefined,
            'updateDrawerServiceStats',
          );
        },
        updateDrawerInsight: (payload: Updatable<DrawerInsightState>) => {
          set(
            (state) => {
              state['insight'] = {
                ...state['insight'],
                ...payload,
              };
            },
            undefined,
            'updateDrawerInsight',
          );
        },
        updateDrawerEvent: (payload: Updatable<DrawerEventState>) => {
          set(
            (state) => {
              state['event'] = {
                ...state['event'],
                ...payload,
              };
            },
            undefined,
            'updateDrawerEvent',
          );
        },
        updateDrawerClientConnection: (payload: Updatable<DrawerClientConnectionState>) => {
          set(
            (state) => {
              state['clientConnection'] = {
                ...state['clientConnection'],
                ...payload,
              };
            },
            undefined,
            'updateDrawerClientConnection',
          );
        },
        updateDrawerClientConnectionStats: (payload: Updatable<DrawerClientConnectionState['stats']>) => {
          set(
            (state) => {
              state['clientConnection'].stats = {
                ...state['clientConnection'].stats,
                ...payload,
              };
            },
            undefined,
            'updateDrawerClientConnectionStats',
          );
        },
        clearDrawerClientConnection: () => {
          set(
            (state) => {
              state['clientConnection'] = defaultDrawerClientConnectionState;
            },
            undefined,
            'clearDrawerClientConnection',
          );
        },
      }),
      {
        name: 'DrawerStore',
      },
    ),
  ),
);

export const useOpeningDrawer = (): DrawerType | null =>
  useDrawerStore((state) => {
    return last(state.openHistory) || null;
  });

export const useWifiStatsDrawer = (): DrawerWifiState => useDrawerStore((state) => state.wifiStatistics);
export const useWanStatsDrawer = (): DrawerWanState => useDrawerStore((state) => state.wanStatistics);
export const useCpeStatsDrawer = (): DrawerCpeState => useDrawerStore((state) => state.cpeStatistics);
export const useWorkbenchDrawer = (): DrawerWorkbenchState => useDrawerStore((state) => state.workbench);
export const useServiceSummaryDrawer = (): DrawerServiceSummaryState => useDrawerStore((state) => state.serviceSummary);
export const useServiceStatsDrawer = (): DrawerServiceStatsState => useDrawerStore((state) => state.serviceStats);
export const useInsightDrawer = (): DrawerInsightState => useDrawerStore((state) => state.insight);
export const useEventDrawer = (): DrawerEventState => useDrawerStore((state) => state.event);
export const useClientConnectionDrawer = (): DrawerClientConnectionState =>
  useDrawerStore((state) => state.clientConnection);
