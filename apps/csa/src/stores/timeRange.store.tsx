import { getDayjsFromToday, getTimezone, getDayjsFromDate } from 'services/Utils';
import { create } from 'zustand';
import { createJSONStorage, devtools, persist } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';

export enum ETimeRangeUnit {
  CUSTOM = 'custom',
  TODAY = '0',
  YESTERDAY = '1',
  THREE_DAYS_AGO = '3',
  SEVEN_DAYS_AGO = '7',
  FOURTEEN_DAYS_AGO = '14',
}

interface TimeRange {
  unit: ETimeRangeUnit;
  startDate?: Date;
  endDate?: Date;
}

function getStartDateAndEndDate(unit: ETimeRangeUnit, startDate?: Date, endDate?: Date): TimeRange {
  const result: TimeRange = {
    unit,
    startDate,
    endDate,
  };

  const timezone = localStorage.getItem('timezone');

  if (unit === ETimeRangeUnit.CUSTOM) {
    result.startDate = getDayjsFromDate(startDate)
      .subtract(2, 'day')
      .tz(timezone || getTimezone())
      .startOf('day')
      .toDate();
    result.endDate = getDayjsFromDate(endDate)
      .tz(timezone || getTimezone())
      .startOf('day')
      .toDate();
  } else {
    result.startDate = getDayjsFromToday(Number(unit) + 2)
      ?.tz(timezone || getTimezone())
      .startOf('day')
      .toDate();
    result.endDate = getDayjsFromToday(Number(unit))
      ?.tz(timezone || getTimezone())
      .startOf('day')
      .toDate();
  }
  return result;
}

interface TimeRangeStore {
  timeRanges: {
    [lineId: string]: TimeRange | undefined;
  };
  initLineTimeRange: (lineId: string, timeRange: TimeRange) => void;
  updateLineTimeRange: (lineId: string, timeRange: TimeRange) => void;
  removeLinetimeRange: (lineId: string) => void;
  onRehydrateStorageHandler: () => void;
}

const useTimeRangeStore = create<TimeRangeStore>()(
  devtools(
    persist(
      immer((set) => ({
        timeRanges: {},
        initLineTimeRange: (lineId, timeRange) =>
          set(
            (state) => {
              if (state.timeRanges[lineId]) return;
              state.timeRanges[lineId] = getStartDateAndEndDate(timeRange.unit, timeRange.startDate, timeRange.endDate);
            },
            undefined,
            'initLineTimeRange',
          ),
        updateLineTimeRange: (lineId, timeRange) =>
          set(
            (state) => {
              if (!state.timeRanges[lineId]) return;
              state.timeRanges[lineId] = getStartDateAndEndDate(timeRange.unit, timeRange.startDate, timeRange.endDate);
            },
            undefined,
            'updateLineTimeRange',
          ),
        removeLinetimeRange: (lineId) =>
          set(
            (state) => {
              delete state.timeRanges[lineId];
            },
            undefined,
            'removeLinetimeRange',
          ),
        onRehydrateStorageHandler: () =>
          set(
            (state) => {
              if (state) {
                // Recalculate time ranges for non-custom units on reload
                Object.keys(state.timeRanges).forEach((lineId) => {
                  const timeRange = state.timeRanges[lineId];
                  if (timeRange && timeRange.unit !== ETimeRangeUnit.CUSTOM) {
                    state.timeRanges[lineId] = getStartDateAndEndDate(
                      timeRange.unit,
                      timeRange.startDate,
                      timeRange.endDate,
                    );
                  }
                });
              }
            },
            undefined,
            'onRehydrateStorageHandler',
          ),
      })),
      {
        name: 'csa-timeRangeStore',
        storage: createJSONStorage(() => localStorage),
        onRehydrateStorage: (state) => {
          state?.onRehydrateStorageHandler();
        },
      },
    ),
    { name: 'timeRangeStore', store: 'timeRangeStore' },
  ),
);

export const useGetTimeRangeByLine = (lineId: string) => useTimeRangeStore((state) => state.timeRanges[lineId]);
export const useRemoveTimeRangeByLine = () => useTimeRangeStore((state) => state.removeLinetimeRange);
export const useInitLineTimeRange = () => useTimeRangeStore((state) => state.initLineTimeRange);
export const useUpdateLineTimeRange = () => useTimeRangeStore((state) => state.updateLineTimeRange);

export default useTimeRangeStore;
