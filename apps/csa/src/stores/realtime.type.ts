import { TRealtimeMetric } from '@/types/realtime.type';

export type LineRealtimeMetaData = {
  isOnDemand?: boolean;
  isOnFly?: boolean; // true -> realtime, false -> get today in historical.
  isGeneralData?: boolean;
  isBroadbandSpeed?: boolean;
  isWifiSpeed?: boolean;
  isWifiScan?: boolean;
};

export interface RequestInfo {
  ttl: number;
  metric: TRealtimeMetric[];
  realtimeRequestId?: string;
  isRealtimeRequesting?: boolean;
  isDataReady?: boolean;
  lastUpdated?: number | null;
  currentStepId?: string | null;
  currentStepText?: string | null;
  progress?: number;
}

export type RealtimeRequestStatus = 'PROCESSING' | 'SUCCESS' | 'FAILED';

export type StepState = 'INIT' | 'IN_PROGRESS' | 'SUCCESS' | 'ERROR' | 'CLEANUP';
