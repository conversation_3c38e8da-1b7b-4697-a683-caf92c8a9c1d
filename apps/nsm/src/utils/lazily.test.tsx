import { FC, useReducer } from 'react';
import { fireEvent, render } from '@testing-library/react';
import { AXONLoad } from './lazily';

it('Shows loading for some random component with loadable', async () => {
  const f = jest.fn();
  // @ts-expect-error ignore this error message
  const { Component } = AXONLoad(() => new Promise((r) => f(r)), {
    fallback: 'Loading...',
  });

  const App: FC = () => {
    const [open, setOpen] = useReducer(() => true, false);
    return <>{open ? <Component /> : <button onClick={setOpen}>Load</button>}</>;
  };

  const { findByText, getByText } = render(<App />);

  fireEvent.click(getByText('Load'));
  await findByText('Loading...');

  expect(f).toBeCalledTimes(1);
});
