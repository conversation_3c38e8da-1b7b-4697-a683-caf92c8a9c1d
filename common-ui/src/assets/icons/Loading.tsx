import { SVGProps } from 'react';

interface IProps extends SVGProps<SVGSVGElement> {
  size?: number;
}

export const Loading = (props: IProps) => {
  const { size = 1.5, ...rest } = props;
  return (
    <svg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' {...rest}>
      <path
        d='M12 2.00003V6.00003'
        stroke='currentColor'
        strokeWidth={size}
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path d='M12 18V22' stroke='currentColor' strokeWidth={size} strokeLinecap='round' strokeLinejoin='round' />
      <path
        d='M4.93002 4.92999L7.76002 7.75999'
        stroke='currentColor'
        strokeWidth={size}
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        d='M16.24 16.2399L19.07 19.0699'
        stroke='currentColor'
        strokeWidth={size}
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        d='M2.00003 12H6.00003'
        stroke='currentColor'
        strokeWidth={size}
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path d='M18 12H22' stroke='currentColor' strokeWidth={size} strokeLinecap='round' strokeLinejoin='round' />
      <path
        d='M4.93002 19.0699L7.76002 16.2399'
        stroke='currentColor'
        strokeWidth={size}
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        d='M16.24 7.75999L19.07 4.92999'
        stroke='currentColor'
        strokeWidth={size}
        strokeLinecap='round'
        strokeLinejoin='round'
      />
    </svg>
  );
};
