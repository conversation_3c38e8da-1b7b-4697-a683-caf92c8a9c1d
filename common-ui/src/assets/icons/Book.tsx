import React from 'react';

export const Book = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg xmlns='http://www.w3.org/2000/svg' width='16' height='17' viewBox='0 0 16 17' fill='none' {...props}>
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M4.33366 2.33203C4.02424 2.33203 3.72749 2.45495 3.5087 2.67374C3.28991 2.89253 3.16699 3.18928 3.16699 3.4987V11.6729C3.51269 11.4521 3.91699 11.332 4.33366 11.332H12.8337V2.33203H4.33366ZM13.8337 1.83203C13.8337 1.55589 13.6098 1.33203 13.3337 1.33203H4.33366C3.75902 1.33203 3.20792 1.5603 2.80159 1.96663C2.39527 2.37296 2.16699 2.92406 2.16699 3.4987V13.4987C2.16699 14.0733 2.39527 14.6244 2.80159 15.0308C3.20792 15.4371 3.75902 15.6654 4.33366 15.6654H13.3337C13.6098 15.6654 13.8337 15.4415 13.8337 15.1654V1.83203ZM12.8337 12.332H4.33366C4.02424 12.332 3.72749 12.4549 3.5087 12.6737C3.28991 12.8925 3.16699 13.1893 3.16699 13.4987C3.16699 13.8081 3.28991 14.1049 3.5087 14.3237C3.72749 14.5424 4.02424 14.6654 4.33366 14.6654H12.8337V12.332Z'
        fill='currentColor'
      />
    </svg>
  );
};
