import React from 'react';

export const WifiRouter = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg width='16' height='16' viewBox='0 0 16 16' fill='none' xmlns='http://www.w3.org/2000/svg' {...props}>
      <path
        opacity='0.4'
        fillRule='evenodd'
        clipRule='evenodd'
        d='M10.9996 3C9.68031 3 8.54833 3.80688 8.07243 4.95599C7.96678 5.21112 7.6743 5.33229 7.41917 5.22663C7.16404 5.12097 7.04287 4.8285 7.14853 4.57337C7.77378 3.0636 9.26188 2 10.9996 2C12.7373 2 14.2254 3.0636 14.8506 4.57337C14.9563 4.8285 14.8351 5.12097 14.58 5.22663C14.3248 5.33229 14.0324 5.21112 13.9267 4.95599C13.4508 3.80688 12.3188 3 10.9996 3Z'
        fill='currentColor'
      />
      <path
        opacity='0.7'
        fillRule='evenodd'
        clipRule='evenodd'
        d='M11.0004 4.66797C10.3188 4.66797 9.74223 5.12293 9.56019 5.74677C9.48284 6.01186 9.20523 6.16404 8.94015 6.08669C8.67506 6.00933 8.52287 5.73173 8.60023 5.46664C8.90338 4.42776 9.8625 3.66797 11.0004 3.66797C12.1383 3.66797 13.0974 4.42776 13.4005 5.46664C13.4779 5.73173 13.3257 6.00933 13.0606 6.08669C12.7955 6.16404 12.5179 6.01186 12.4406 5.74677C12.2585 5.12293 11.6819 4.66797 11.0004 4.66797Z'
        fill='currentColor'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M1.33301 10.4987C1.33301 9.24162 1.33301 8.61308 1.72353 8.22256C2.11406 7.83203 2.7426 7.83203 3.99967 7.83203H11.9997C13.2568 7.83203 13.8853 7.83203 14.2758 8.22256C14.6663 8.61308 14.6663 9.24162 14.6663 10.4987C14.6663 11.7558 14.6663 12.3843 14.2758 12.7748C13.8853 13.1654 13.2568 13.1654 11.9997 13.1654H3.99967C2.7426 13.1654 2.11406 13.1654 1.72353 12.7748C1.33301 12.3843 1.33301 11.7558 1.33301 10.4987ZM3.99967 11.1654C4.36786 11.1654 4.66634 10.8669 4.66634 10.4987C4.66634 10.1305 4.36786 9.83203 3.99967 9.83203C3.63148 9.83203 3.33301 10.1305 3.33301 10.4987C3.33301 10.8669 3.63148 11.1654 3.99967 11.1654ZM5.99967 11.1654C6.36786 11.1654 6.66634 10.8669 6.66634 10.4987C6.66634 10.1305 6.36786 9.83203 5.99967 9.83203C5.63148 9.83203 5.33301 10.1305 5.33301 10.4987C5.33301 10.8669 5.63148 11.1654 5.99967 11.1654Z'
        fill='currentColor'
      />
      <path
        d='M11.5 5.83203C11.5 5.55589 11.2761 5.33203 11 5.33203C10.7239 5.33203 10.5 5.55589 10.5 5.83203V7.83203H11.5V5.83203Z'
        fill='currentColor'
      />
    </svg>
  );
};
