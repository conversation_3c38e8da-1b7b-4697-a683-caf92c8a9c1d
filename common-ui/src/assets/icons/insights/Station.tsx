import React from 'react';

export const Station = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg width='16' height='16' viewBox='0 0 16 16' fill='none' xmlns='http://www.w3.org/2000/svg' {...props}>
      <path
        opacity='0.5'
        d='M3.44804 2.11308C2.66699 2.89413 2.66699 4.15121 2.66699 6.66536V9.33203C2.66699 11.8462 2.66699 13.1033 3.44804 13.8843C4.22909 14.6654 5.48617 14.6654 8.00033 14.6654C10.5145 14.6654 11.7716 14.6654 12.5526 13.8843C13.3337 13.1033 13.3337 11.8462 13.3337 9.33203V6.66536C13.3337 4.15121 13.3337 2.89413 12.5526 2.11308C11.7716 1.33203 10.5145 1.33203 8.00033 1.33203C5.48617 1.33203 4.22909 1.33203 3.44804 2.11308Z'
        fill='currentColor'
      />
      <path
        d='M6 2.83203C5.72386 2.83203 5.5 3.05589 5.5 3.33203C5.5 3.60817 5.72386 3.83203 6 3.83203H10C10.2761 3.83203 10.5 3.60817 10.5 3.33203C10.5 3.05589 10.2761 2.83203 10 2.83203H6Z'
        fill='currentColor'
      />
      <path
        d='M8 12.6654C8.73638 12.6654 9.33333 12.0684 9.33333 11.332C9.33333 10.5957 8.73638 9.9987 8 9.9987C7.26362 9.9987 6.66667 10.5957 6.66667 11.332C6.66667 12.0684 7.26362 12.6654 8 12.6654Z'
        fill='currentColor'
      />
    </svg>
  );
};
