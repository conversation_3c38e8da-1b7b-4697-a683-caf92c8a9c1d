import React from 'react';

export const Link = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 16 16' fill='none' {...props}>
      <g clipPath='url(#clip0_33028_54767)'>
        <path
          d='M6.6976 9.33203C5.69363 8.32416 5.78406 6.59928 6.89959 5.47942L10.1314 2.23511C11.2469 1.11525 12.9651 1.02447 13.9691 2.03234C14.973 3.04022 14.8826 4.76507 13.7671 5.88493L12.1512 7.50709'
          stroke='currentColor'
          strokeWidth='1.5'
          strokeLinecap='round'
        />
        <path
          d='M9.30207 6.66797C10.3061 7.67585 10.2156 9.40069 9.10008 10.5206L7.48421 12.1427L5.86834 13.7649C4.7528 14.8848 3.03459 14.9755 2.03061 13.9677C1.02663 12.9598 1.11706 11.2349 2.2326 10.115L3.84849 8.49286'
          stroke='currentColor'
          strokeWidth='1.5'
          strokeLinecap='round'
        />
      </g>
      <defs>
        <clipPath id='clip0_33028_54767'>
          <rect width='16' height='16' fill='white' />
        </clipPath>
      </defs>
    </svg>
  );
};
