import React from 'react';

export const FolderError = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 16 16' fill='none' {...props}>
      <g clipPath='url(#clip0_33028_54563)'>
        <path
          d='M1.33301 4.63186C1.33301 4.04351 1.33301 3.74933 1.37924 3.50429C1.58277 2.42557 2.42655 1.58179 3.50527 1.37826C3.75031 1.33203 4.04449 1.33203 4.63284 1.33203C4.89062 1.33203 5.01952 1.33203 5.14339 1.34361C5.67744 1.39356 6.18402 1.60339 6.59697 1.94571C6.69275 2.02511 6.78389 2.11625 6.96617 2.29853L7.33301 2.66537C7.87686 3.20922 8.14879 3.48114 8.47442 3.66231C8.6533 3.76184 8.84305 3.84043 9.03991 3.89655C9.39827 3.9987 9.78284 3.9987 10.552 3.9987H10.8011C12.556 3.9987 13.4334 3.9987 14.0038 4.51167C14.0562 4.55886 14.1062 4.60879 14.1534 4.66125C14.6663 5.2316 14.6663 6.10906 14.6663 7.86397V9.33203C14.6663 11.8462 14.6663 13.1033 13.8853 13.8843C13.1042 14.6654 11.8472 14.6654 9.33301 14.6654H6.66634C4.15218 14.6654 2.89511 14.6654 2.11406 13.8843C1.33301 13.1033 1.33301 11.8462 1.33301 9.33203V4.63186Z'
          stroke='currentColor'
          strokeWidth='1.5'
        />
        <path d='M7 10L9 8M9 10L7 8' stroke='currentColor' strokeWidth='1.5' strokeLinecap='round' />
      </g>
      <defs>
        <clipPath id='clip0_33028_54563'>
          <rect width='16' height='16' fill='white' />
        </clipPath>
      </defs>
    </svg>
  );
};
