import React from 'react';

export const RefreshSquare = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 16 16' fill='none' {...props}>
      <g clipPath='url(#clip0_33028_54523)'>
        <path
          d='M4.91838 7.75439H4.16838H4.91838ZM4.91838 8.37167L4.3569 8.86891C4.49218 9.02167 4.68388 9.11272 4.88777 9.12105C5.09165 9.12938 5.29014 9.05427 5.43743 8.91304L4.91838 8.37167ZM6.51905 7.87601C6.81805 7.58934 6.82804 7.11457 6.54137 6.81558C6.2547 6.51659 5.77994 6.5066 5.48095 6.79326L6.51905 7.87601ZM4.56148 6.8374C4.28686 6.52731 3.81286 6.49854 3.50277 6.77316C3.19267 7.04777 3.16391 7.52177 3.43852 7.83187L4.56148 6.8374ZM9.77093 6.17727C10.0522 6.48133 10.5267 6.4998 10.8308 6.21852C11.1349 5.93724 11.1533 5.46273 10.872 5.15867L9.77093 6.17727ZM8.02914 4.66797V3.91797C5.90246 3.91797 4.16838 5.63005 4.16838 7.75439H4.91838H5.66838C5.66838 6.46956 6.71976 5.41797 8.02914 5.41797V4.66797ZM4.91838 7.75439H4.16838L4.16838 8.37167L4.91838 8.37167L5.66838 8.37167L5.66838 7.75439L4.91838 7.75439ZM4.91838 8.37167L5.43743 8.91304L6.51905 7.87601L6 7.33464L5.48095 6.79326L4.39932 7.8303L4.91838 8.37167ZM4.91838 8.37167L5.47986 7.87444L4.56148 6.8374L4 7.33464L3.43852 7.83187L4.3569 8.86891L4.91838 8.37167ZM10.3215 5.66797L10.872 5.15867C10.1667 4.39621 9.15302 3.91797 8.02914 3.91797V4.66797V5.41797C8.71969 5.41797 9.339 5.71035 9.77093 6.17727L10.3215 5.66797Z'
          fill='currentColor'
        />
        <path
          d='M11.0795 7.62891L11.6402 7.13078C11.5047 6.9783 11.313 6.88757 11.1092 6.87949C10.9054 6.87142 10.7071 6.94669 10.56 7.08798L11.0795 7.62891ZM9.48081 8.12443C9.18206 8.41135 9.17248 8.88613 9.4594 9.18488C9.74632 9.48363 10.2211 9.49321 10.5198 9.20629L9.48081 8.12443ZM11.4396 9.16349C11.7147 9.47315 12.1888 9.50116 12.4985 9.22605C12.8081 8.95094 12.8361 8.4769 12.561 8.16724L11.4396 9.16349ZM6.21387 9.83141C5.93041 9.52938 5.45578 9.51432 5.15375 9.79778C4.85171 10.0812 4.83665 10.5559 5.12011 10.8579L6.21387 9.83141ZM7.95686 11.3326V12.0826C10.0874 12.0826 11.8295 10.3732 11.8295 8.24619H11.0795H10.3295C10.3295 9.52833 9.27551 10.5826 7.95686 10.5826V11.3326ZM11.0795 8.24619H11.8295V7.62891H11.0795H10.3295V8.24619H11.0795ZM11.0795 7.62891L10.56 7.08798L9.48081 8.12443L10.0003 8.66536L10.5198 9.20629L11.599 8.16984L11.0795 7.62891ZM11.0795 7.62891L10.5188 8.12703L11.4396 9.16349L12.0003 8.66536L12.561 8.16724L11.6402 7.13078L11.0795 7.62891ZM5.66699 10.3447L5.12011 10.8579C5.82727 11.6114 6.83791 12.0826 7.95686 12.0826V11.3326V10.5826C7.26637 10.5826 6.6473 10.2932 6.21387 9.83141L5.66699 10.3447Z'
          fill='currentColor'
        />
        <path
          d='M1.33301 7.9987C1.33301 4.856 1.33301 3.28465 2.30932 2.30834C3.28563 1.33203 4.85698 1.33203 7.99967 1.33203C11.1424 1.33203 12.7137 1.33203 13.69 2.30834C14.6663 3.28465 14.6663 4.856 14.6663 7.9987C14.6663 11.1414 14.6663 12.7127 13.69 13.6891C12.7137 14.6654 11.1424 14.6654 7.99967 14.6654C4.85698 14.6654 3.28563 14.6654 2.30932 13.6891C1.33301 12.7127 1.33301 11.1414 1.33301 7.9987Z'
          stroke='currentColor'
          strokeWidth='1.5'
        />
      </g>
      <defs>
        <clipPath id='clip0_33028_54523'>
          <rect width='16' height='16' fill='white' />
        </clipPath>
      </defs>
    </svg>
  );
};
