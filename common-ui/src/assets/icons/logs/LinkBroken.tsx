import React from 'react';

export const LinkBroken = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 16 16' fill='none' {...props}>
      <path d='M1.33301 5.33203L5.33301 6.66536' stroke='currentColor' strokeWidth='1.5' strokeLinecap='round' />
      <path d='M4 2.66797L5.33333 4.66797' stroke='currentColor' strokeWidth='1.5' strokeLinecap='round' />
      <path
        d='M7.33301 4.37371L9.79973 1.95706C10.7728 1.00372 12.5083 1.15829 13.676 2.3023C14.8437 3.44631 15.0015 5.14654 14.0284 6.09988L12.0902 7.9987'
        stroke='currentColor'
        strokeWidth='1.5'
        strokeLinecap='round'
      />
      <path
        d='M10.0003 10.3911L7.3105 13.334C6.38202 14.3498 4.72612 14.1851 3.61195 12.9661C2.49778 11.7472 2.34724 9.93548 3.27572 8.91966L4.11629 8'
        stroke='currentColor'
        strokeWidth='1.5'
        strokeLinecap='round'
      />
    </svg>
  );
};
