import React from 'react';

export const CloseCircle = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 16 16' fill='none' {...props}>
      <g clipPath='url(#clip0_33028_54553)'>
        <circle cx='8.00016' cy='7.9987' r='6.66667' stroke='currentColor' strokeWidth='1.5' />
        <path
          d='M9.66682 6.33204L6.3335 9.66536M6.33348 6.33203L9.6668 9.66535'
          stroke='currentColor'
          strokeWidth='1.5'
          strokeLinecap='round'
        />
      </g>
      <defs>
        <clipPath id='clip0_33028_54553'>
          <rect width='16' height='16' fill='white' />
        </clipPath>
      </defs>
    </svg>
  );
};
