import React from 'react';

export const RefreshCircle = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 16 16' fill='none' {...props}>
      <g clipPath='url(#clip0_33028_54548)'>
        <path
          d='M4.91838 7.75439H4.16838H4.91838ZM4.91838 8.37167L4.3569 8.86891C4.49218 9.02167 4.68388 9.11272 4.88777 9.12105C5.09165 9.12938 5.29014 9.05427 5.43743 8.91304L4.91838 8.37167ZM6.51905 7.87601C6.81805 7.58934 6.82804 7.11457 6.54137 6.81558C6.2547 6.51659 5.77994 6.5066 5.48095 6.79326L6.51905 7.87601ZM4.56148 6.8374C4.28686 6.52731 3.81286 6.49854 3.50277 6.77316C3.19267 7.04777 3.16391 7.52177 3.43852 7.83187L4.56148 6.8374ZM9.77093 6.17727C10.0522 6.48133 10.5267 6.4998 10.8308 6.21852C11.1349 5.93724 11.1533 5.46273 10.872 5.15867L9.77093 6.17727ZM8.02914 4.66797V3.91797C5.90246 3.91797 4.16838 5.63005 4.16838 7.75439H4.91838H5.66838C5.66838 6.46956 6.71976 5.41797 8.02914 5.41797V4.66797ZM4.91838 7.75439H4.16838L4.16838 8.37167L4.91838 8.37167L5.66838 8.37167L5.66838 7.75439L4.91838 7.75439ZM4.91838 8.37167L5.43743 8.91304L6.51905 7.87601L6 7.33464L5.48095 6.79326L4.39932 7.8303L4.91838 8.37167ZM4.91838 8.37167L5.47986 7.87444L4.56148 6.8374L4 7.33464L3.43852 7.83187L4.3569 8.86891L4.91838 8.37167ZM10.3215 5.66797L10.872 5.15867C10.1667 4.39621 9.15302 3.91797 8.02914 3.91797V4.66797V5.41797C8.71969 5.41797 9.339 5.71035 9.77093 6.17727L10.3215 5.66797Z'
          fill='currentColor'
        />
        <path
          d='M11.079 7.62891L11.6397 7.13078C11.5042 6.9783 11.3125 6.88757 11.1087 6.87949C10.9049 6.87142 10.7066 6.94669 10.5595 7.08798L11.079 7.62891ZM9.48032 8.12443C9.18158 8.41135 9.17199 8.88613 9.45891 9.18488C9.74583 9.48363 10.2206 9.49321 10.5194 9.20629L9.48032 8.12443ZM11.4392 9.16349C11.7143 9.47315 12.1883 9.50116 12.498 9.22605C12.8076 8.95094 12.8356 8.4769 12.5605 8.16724L11.4392 9.16349ZM6.21338 9.83141C5.92993 9.52938 5.45529 9.51432 5.15326 9.79778C4.85122 10.0812 4.83617 10.5559 5.11962 10.8579L6.21338 9.83141ZM7.95637 11.3326V12.0826C10.0869 12.0826 11.829 10.3732 11.829 8.24619H11.079H10.329C10.329 9.52833 9.27503 10.5826 7.95637 10.5826V11.3326ZM11.079 8.24619H11.829V7.62891H11.079H10.329V8.24619H11.079ZM11.079 7.62891L10.5595 7.08798L9.48032 8.12443L9.99984 8.66536L10.5194 9.20629L11.5985 8.16984L11.079 7.62891ZM11.079 7.62891L10.5183 8.12703L11.4392 9.16349L11.9998 8.66536L12.5605 8.16724L11.6397 7.13078L11.079 7.62891ZM5.6665 10.3447L5.11962 10.8579C5.82678 11.6114 6.83742 12.0826 7.95637 12.0826V11.3326V10.5826C7.26588 10.5826 6.64682 10.2932 6.21338 9.83141L5.6665 10.3447Z'
          fill='currentColor'
        />
        <circle cx='8.00016' cy='7.9987' r='6.66667' stroke='currentColor' strokeWidth='1.5' />
      </g>
      <defs>
        <clipPath id='clip0_33028_54548'>
          <rect width='16' height='16' fill='white' />
        </clipPath>
      </defs>
    </svg>
  );
};
