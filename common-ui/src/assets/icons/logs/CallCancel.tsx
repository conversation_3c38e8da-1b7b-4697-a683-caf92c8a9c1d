import React from 'react';

export const CallCancel = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 16 16' fill='none' {...props}>
      <path
        d='M13.3336 2.66798L10.667 5.33464M10.667 2.66797L13.3336 5.33462'
        stroke='currentColor'
        strokeWidth='1.5'
        strokeLinecap='round'
      />
      <path
        d='M10.0671 10.0181L9.52335 9.50162L10.0671 10.0181ZM10.3708 9.69847L10.9146 10.215H10.9146L10.3708 9.69847ZM11.9819 9.47488L11.6077 10.1249L11.9819 9.47488ZM13.2556 10.208L12.8814 10.858L13.2556 10.208ZM13.6144 12.5056L14.1582 13.0221L13.6144 12.5056ZM12.6674 13.5027L12.1236 12.9862L12.6674 13.5027ZM11.7842 13.9754L11.8579 14.7218L11.7842 13.9754ZM5.21024 10.9835L5.75403 10.4669L5.21024 10.9835ZM2.00193 4.64396L1.253 4.68415V4.68416L2.00193 4.64396ZM6.31835 5.66874L6.86214 6.18526H6.86214L6.31835 5.66874ZM6.42283 3.7954L7.03522 3.36242V3.36242L6.42283 3.7954ZM5.58217 2.60641L4.96978 3.03939L4.96978 3.03939L5.58217 2.60641ZM3.50763 2.40576L4.05143 2.92228V2.92228L3.50763 2.40576ZM2.46123 3.50742L1.91744 2.99091L1.91744 2.99091L2.46123 3.50742ZM7.37541 8.70393L7.91921 8.18742L7.37541 8.70393ZM10.0671 10.0181L10.6109 10.5347L10.9146 10.215L10.3708 9.69847L9.82698 9.18195L9.52335 9.50162L10.0671 10.0181ZM11.9819 9.47488L11.6077 10.1249L12.8814 10.858L13.2556 10.208L13.6297 9.55797L12.356 8.82486L11.9819 9.47488ZM13.6144 12.5056L13.0707 11.9891L12.1236 12.9862L12.6674 13.5027L13.2112 14.0192L14.1582 13.0221L13.6144 12.5056ZM11.7842 13.9754L11.7105 13.229C10.8187 13.3171 8.40262 13.2554 5.75403 10.4669L5.21024 10.9835L4.66645 11.5C7.68023 14.6729 10.5771 14.8482 11.8579 14.7218L11.7842 13.9754ZM5.21024 10.9835L5.75403 10.4669C3.21279 7.79149 2.80184 5.55374 2.75085 4.60376L2.00193 4.64396L1.253 4.68416C1.32221 5.97354 1.86816 8.55391 4.66645 11.5L5.21024 10.9835ZM6.12714 5.87005L6.67093 6.38657L6.86214 6.18526L6.31835 5.66874L5.77455 5.15223L5.58334 5.35353L6.12714 5.87005ZM6.42283 3.7954L7.03522 3.36242L6.19456 2.17343L5.58217 2.60641L4.96978 3.03939L5.81043 4.22838L6.42283 3.7954ZM3.50763 2.40576L2.96384 1.88925L1.91744 2.99091L2.46123 3.50742L3.00503 4.02394L4.05143 2.92228L3.50763 2.40576ZM6.12714 5.87005C5.58334 5.35353 5.58265 5.35426 5.58196 5.355C5.58172 5.35524 5.58103 5.35598 5.58056 5.35648C5.57962 5.35748 5.57867 5.3585 5.5777 5.35954C5.57578 5.36161 5.57381 5.36375 5.57179 5.36596C5.56777 5.37039 5.56357 5.3751 5.55922 5.38011C5.55052 5.39011 5.54119 5.40129 5.5314 5.41366C5.51182 5.4384 5.49035 5.46795 5.46824 5.50251C5.42386 5.57186 5.37738 5.66063 5.33855 5.76974C5.25929 5.99249 5.22037 6.27619 5.27045 6.61795C5.36825 7.28546 5.79677 8.13095 6.83162 9.22045L7.37541 8.70393L7.91921 8.18742C6.98405 7.20288 6.78844 6.63142 6.7546 6.4005C6.73886 6.29305 6.75725 6.25716 6.75174 6.27262C6.7498 6.27809 6.74431 6.2913 6.73171 6.31099C6.72548 6.32072 6.71759 6.33193 6.70762 6.34452C6.70263 6.35082 6.69712 6.35748 6.69103 6.36448C6.68799 6.36798 6.68479 6.37157 6.68145 6.37525C6.67977 6.37709 6.67806 6.37896 6.67631 6.38084C6.67543 6.38179 6.67455 6.38273 6.67365 6.38369C6.6732 6.38416 6.67252 6.38488 6.6723 6.38512C6.67162 6.38584 6.67093 6.38657 6.12714 5.87005ZM7.37541 8.70393L6.83162 9.22045C7.86198 10.3052 8.67564 10.7712 9.3398 10.8791C9.68341 10.9349 9.972 10.8916 10.1988 10.8021C10.3091 10.7586 10.3979 10.7069 10.4662 10.6585C10.5002 10.6343 10.5292 10.611 10.5531 10.59C10.5651 10.5795 10.5759 10.5695 10.5855 10.5602C10.5903 10.5556 10.5949 10.5511 10.5991 10.5468C10.6012 10.5447 10.6033 10.5426 10.6052 10.5406C10.6062 10.5396 10.6072 10.5386 10.6081 10.5376C10.6086 10.5371 10.6093 10.5364 10.6095 10.5361C10.6102 10.5354 10.6109 10.5347 10.0671 10.0181C9.52335 9.50162 9.52404 9.5009 9.52472 9.50018C9.52495 9.49994 9.52563 9.49923 9.52609 9.49875C9.52699 9.49781 9.5279 9.49687 9.5288 9.49595C9.53059 9.4941 9.53237 9.49228 9.53413 9.49051C9.53765 9.48696 9.5411 9.48356 9.54449 9.4803C9.55125 9.47378 9.55774 9.46781 9.56397 9.46235C9.57643 9.45142 9.58791 9.44242 9.59839 9.43498C9.61955 9.41998 9.63654 9.41145 9.64841 9.40676C9.6783 9.39497 9.66333 9.41199 9.58026 9.3985C9.39106 9.36777 8.85886 9.17669 7.91921 8.18742L7.37541 8.70393ZM5.58217 2.60641L6.19456 2.17343C5.4353 1.09955 3.89852 0.905203 2.96384 1.88925L3.50763 2.40576L4.05143 2.92228C4.28276 2.67873 4.71175 2.67445 4.96978 3.03939L5.58217 2.60641ZM2.00193 4.64396L2.75085 4.60376C2.74116 4.42329 2.81818 4.22066 3.00503 4.02394L2.46123 3.50742L1.91744 2.99091C1.52613 3.40288 1.21571 3.98931 1.253 4.68415L2.00193 4.64396ZM12.6674 13.5027L12.1236 12.9862C11.965 13.1532 11.8225 13.218 11.7105 13.229L11.7842 13.9754L11.8579 14.7218C12.4331 14.665 12.8883 14.3592 13.2112 14.0192L12.6674 13.5027ZM6.31835 5.66874L6.86214 6.18526C7.5942 5.41454 7.64047 4.21846 7.03522 3.36242L6.42283 3.7954L5.81043 4.22838C6.0271 4.53483 5.98457 4.93112 5.77455 5.15223L6.31835 5.66874ZM13.2556 10.208L12.8814 10.858C13.2933 11.0951 13.3638 11.6805 13.0707 11.9891L13.6144 12.5056L14.1582 13.0221C15.1436 11.9847 14.8536 10.2625 13.6297 9.55797L13.2556 10.208ZM10.3708 9.69847L10.9146 10.215C11.0901 10.0301 11.3636 9.98435 11.6077 10.1249L11.9819 9.47488L12.356 8.82486C11.5261 8.34716 10.4909 8.48299 9.82698 9.18195L10.3708 9.69847Z'
        fill='currentColor'
      />
    </svg>
  );
};
