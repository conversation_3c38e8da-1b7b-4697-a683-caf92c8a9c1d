import React from 'react';

export const CheckSquare = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 16 16' fill='none' {...props}>
      <g clipPath='url(#clip0_33028_54533)'>
        <path
          d='M1.3335 7.9987C1.3335 4.856 1.3335 3.28465 2.30981 2.30834C3.28612 1.33203 4.85747 1.33203 8.00016 1.33203C11.1429 1.33203 12.7142 1.33203 13.6905 2.30834C14.6668 3.28465 14.6668 4.856 14.6668 7.9987C14.6668 11.1414 14.6668 12.7127 13.6905 13.6891C12.7142 14.6654 11.1429 14.6654 8.00016 14.6654C4.85747 14.6654 3.28612 14.6654 2.30981 13.6891C1.3335 12.7127 1.3335 11.1414 1.3335 7.9987Z'
          stroke='currentColor'
          strokeWidth='1.5'
        />
        <path
          d='M5.6665 8.33203L6.99984 9.66536L10.3332 6.33203'
          stroke='currentColor'
          strokeWidth='1.5'
          strokeLinecap='round'
          strokeLinejoin='round'
        />
      </g>
      <defs>
        <clipPath id='clip0_33028_54533'>
          <rect width='16' height='16' fill='white' />
        </clipPath>
      </defs>
    </svg>
  );
};
