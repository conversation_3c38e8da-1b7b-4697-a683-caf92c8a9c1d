import React from 'react';

export const CallChat = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 16 16' fill='none' {...props}>
      <g clipPath='url(#clip0_33028_54543)'>
        <path
          d='M11.3337 7.9987C13.1746 7.9987 14.667 6.50631 14.667 4.66536C14.667 2.82442 13.1746 1.33203 11.3337 1.33203C9.49271 1.33203 8.00033 2.82442 8.00033 4.66536C8.00033 5.19859 8.12553 5.70258 8.34815 6.14952C8.4073 6.2683 8.427 6.40405 8.3927 6.53224L8.19416 7.27425C8.10798 7.59636 8.40266 7.89105 8.72477 7.80486L9.46679 7.60632C9.59497 7.57203 9.73073 7.59172 9.8495 7.65088C10.2964 7.87349 10.8004 7.9987 11.3337 7.9987Z'
          stroke='currentColor'
          strokeWidth='1.5'
        />
        <path
          d='M9.40015 10.6861L8.85636 10.1696L9.40015 10.6861ZM9.70378 10.3664L10.2476 10.883H10.2476L9.70378 10.3664ZM11.3149 10.1428L10.9407 10.7929L11.3149 10.1428ZM12.5886 10.876L12.2144 11.526L12.5886 10.876ZM12.9475 13.1736L13.4912 13.6901L12.9475 13.1736ZM12.0004 14.1706L11.4566 13.6541L12.0004 14.1706ZM11.1172 14.6434L11.1909 15.3897L11.1172 14.6434ZM4.54325 11.6514L5.08704 11.1349L4.54325 11.6514ZM1.33493 5.31193L0.586011 5.35212V5.35212L1.33493 5.31193ZM5.65135 6.33671L6.19515 6.85323H6.19515L5.65135 6.33671ZM5.75584 4.46337L6.36823 4.03039V4.03039L5.75584 4.46337ZM4.91518 3.27438L4.30278 3.70736L4.30278 3.70736L4.91518 3.27438ZM2.84064 3.07373L3.38443 3.59025V3.59025L2.84064 3.07373ZM1.79424 4.17539L1.25045 3.65888L1.25045 3.65888L1.79424 4.17539ZM6.70842 9.3719L7.25222 8.85539L6.70842 9.3719ZM9.40015 10.6861L9.94395 11.2026L10.2476 10.883L9.70378 10.3664L9.15999 9.84992L8.85636 10.1696L9.40015 10.6861ZM11.3149 10.1428L10.9407 10.7929L12.2144 11.526L12.5886 10.876L12.9627 10.2259L11.689 9.49283L11.3149 10.1428ZM12.9475 13.1736L12.4037 12.6571L11.4566 13.6541L12.0004 14.1706L12.5442 14.6872L13.4912 13.6901L12.9475 13.1736ZM11.1172 14.6434L11.0435 13.897C10.1517 13.985 7.73563 13.9234 5.08704 11.1349L4.54325 11.6514L3.99945 12.1679C7.01324 15.3409 9.91014 15.5162 11.1909 15.3897L11.1172 14.6434ZM4.54325 11.6514L5.08704 11.1349C2.5458 8.45946 2.13484 6.22171 2.08385 5.27173L1.33493 5.31193L0.586011 5.35212C0.655218 6.64151 1.20117 9.22188 3.99945 12.1679L4.54325 11.6514ZM5.46014 6.53802L6.00394 7.05453L6.19515 6.85323L5.65135 6.33671L5.10756 5.82019L4.91635 6.0215L5.46014 6.53802ZM5.75584 4.46337L6.36823 4.03039L5.52757 2.8414L4.91518 3.27438L4.30278 3.70736L5.14344 4.89635L5.75584 4.46337ZM2.84064 3.07373L2.29685 2.55721L1.25045 3.65888L1.79424 4.17539L2.33803 4.69191L3.38443 3.59025L2.84064 3.07373ZM5.46014 6.53802C4.91635 6.0215 4.91566 6.02223 4.91497 6.02296C4.91473 6.02321 4.91403 6.02395 4.91356 6.02445C4.91262 6.02545 4.91167 6.02647 4.91071 6.0275C4.90879 6.02958 4.90681 6.03172 4.9048 6.03393C4.90078 6.03836 4.89658 6.04307 4.89223 6.04807C4.88352 6.05808 4.8742 6.06925 4.86441 6.08162C4.84483 6.10636 4.82336 6.13592 4.80124 6.17048C4.75687 6.23982 4.71039 6.3286 4.67156 6.43771C4.5923 6.66046 4.55338 6.94416 4.60345 7.28592C4.70126 7.95343 5.12978 8.79892 6.16463 9.88842L6.70842 9.3719L7.25222 8.85539C6.31706 7.87085 6.12144 7.29939 6.08761 7.06847C6.07186 6.96102 6.09025 6.92513 6.08475 6.94059C6.0828 6.94606 6.07731 6.95927 6.06471 6.97896C6.05849 6.98869 6.0506 6.99989 6.04063 7.01249C6.03564 7.01879 6.03013 7.02544 6.02404 7.03245C6.021 7.03595 6.0178 7.03954 6.01445 7.04322C6.01278 7.04506 6.01107 7.04693 6.00932 7.04881C6.00844 7.04975 6.00755 7.0507 6.00666 7.05166C6.00621 7.05213 6.00553 7.05285 6.0053 7.05309C6.00462 7.05381 6.00394 7.05453 5.46014 6.53802ZM6.70842 9.3719L6.16463 9.88842C7.19498 10.9732 8.00864 11.4392 8.67281 11.5471C9.01642 11.6029 9.30501 11.5596 9.53179 11.4701C9.6421 11.4266 9.73087 11.3749 9.79918 11.3265C9.83325 11.3023 9.86216 11.279 9.88615 11.2579C9.89815 11.2474 9.90894 11.2374 9.91855 11.2282C9.92336 11.2235 9.92787 11.2191 9.9321 11.2148C9.93422 11.2127 9.93626 11.2106 9.93824 11.2086C9.93922 11.2075 9.94019 11.2065 9.94114 11.2056C9.94162 11.2051 9.94232 11.2043 9.94256 11.2041C9.94325 11.2033 9.94395 11.2026 9.40015 10.6861C8.85636 10.1696 8.85704 10.1689 8.85773 10.1681C8.85795 10.1679 8.85864 10.1672 8.85909 10.1667C8.86 10.1658 8.8609 10.1648 8.8618 10.1639C8.8636 10.1621 8.86538 10.1603 8.86714 10.1585C8.87066 10.1549 8.87411 10.1515 8.87749 10.1483C8.88425 10.1417 8.89075 10.1358 8.89698 10.1303C8.90943 10.1194 8.92091 10.1104 8.9314 10.103C8.95255 10.0879 8.96954 10.0794 8.98142 10.0747C9.01131 10.0629 8.99634 10.08 8.91327 10.0665C8.72407 10.0357 8.19186 9.84466 7.25222 8.85539L6.70842 9.3719ZM4.91518 3.27438L5.52757 2.8414C4.7683 1.76752 3.23153 1.57317 2.29685 2.55721L2.84064 3.07373L3.38443 3.59025C3.61577 3.3467 4.04476 3.34242 4.30278 3.70736L4.91518 3.27438ZM1.33493 5.31193L2.08385 5.27173C2.07417 5.09126 2.15118 4.88863 2.33803 4.69191L1.79424 4.17539L1.25045 3.65888C0.859137 4.07085 0.548716 4.65728 0.586011 5.35212L1.33493 5.31193ZM12.0004 14.1706L11.4566 13.6541C11.298 13.8211 11.1555 13.8859 11.0435 13.897L11.1172 14.6434L11.1909 15.3897C11.7661 15.333 12.2213 15.0272 12.5442 14.6872L12.0004 14.1706ZM5.65135 6.33671L6.19515 6.85323C6.9272 6.08251 6.97348 4.88643 6.36823 4.03039L5.75584 4.46337L5.14344 4.89635C5.36011 5.20279 5.31758 5.59909 5.10756 5.82019L5.65135 6.33671ZM12.5886 10.876L12.2144 11.526C12.6263 11.7631 12.6968 12.3484 12.4037 12.6571L12.9475 13.1736L13.4912 13.6901C14.4766 12.6527 14.1867 10.9304 12.9627 10.2259L12.5886 10.876ZM9.70378 10.3664L10.2476 10.883C10.4232 10.6981 10.6966 10.6523 10.9407 10.7929L11.3149 10.1428L11.689 9.49283C10.8591 9.01513 9.82389 9.15096 9.15999 9.84992L9.70378 10.3664Z'
          fill='currentColor'
        />
      </g>
      <defs>
        <clipPath id='clip0_33028_54543'>
          <rect width='16' height='16' fill='white' />
        </clipPath>
      </defs>
    </svg>
  );
};
