import React from 'react';

export const Heart = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 16 16' fill='none' {...props}>
      <path
        d='M5.97416 12.6087L6.43848 12.0197L5.97416 12.6087ZM7.99967 3.66855L7.45931 4.18865C7.6007 4.33555 7.79579 4.41855 7.99967 4.41855C8.20356 4.41855 8.39865 4.33555 8.54004 4.18865L7.99967 3.66855ZM10.0252 12.6087L10.4895 13.1977L10.0252 12.6087ZM5.97416 12.6087L6.43848 12.0197C5.4132 11.2115 4.33603 10.4539 3.47879 9.48864C2.64727 8.55232 2.08301 7.47977 2.08301 6.09286H1.33301H0.583008C0.583008 7.94791 1.35855 9.36013 2.35722 10.4847C3.33018 11.5803 4.57358 12.4596 5.50985 13.1977L5.97416 12.6087ZM1.33301 6.09286H2.08301C2.08301 4.75249 2.84003 3.64141 3.85318 3.1783C4.81905 2.7368 6.15131 2.82969 7.45931 4.18865L7.99967 3.66855L8.54004 3.14845C6.84815 1.39064 4.84707 1.07471 3.22958 1.81407C1.65937 2.53181 0.583008 4.19128 0.583008 6.09286H1.33301ZM5.97416 12.6087L5.50985 13.1977C5.84904 13.4651 6.22907 13.7633 6.61809 13.9906C7.00687 14.2178 7.47594 14.4181 7.99967 14.4181V13.6681V12.9181C7.85674 12.9181 7.65915 12.8616 7.37488 12.6955C7.09085 12.5295 6.79146 12.298 6.43848 12.0197L5.97416 12.6087ZM10.0252 12.6087L10.4895 13.1977C11.4258 12.4596 12.6692 11.5803 13.6421 10.4847C14.6408 9.36013 15.4163 7.94791 15.4163 6.09286H14.6663H13.9163C13.9163 7.47977 13.3521 8.55232 12.5206 9.48864C11.6633 10.4539 10.5861 11.2115 9.56087 12.0197L10.0252 12.6087ZM14.6663 6.09286H15.4163C15.4163 4.19128 14.34 2.53181 12.7698 1.81407C11.1523 1.07471 9.1512 1.39064 7.45931 3.14845L7.99967 3.66855L8.54004 4.18865C9.84804 2.82969 11.1803 2.7368 12.1462 3.1783C13.1593 3.64141 13.9163 4.75249 13.9163 6.09286H14.6663ZM10.0252 12.6087L9.56087 12.0197C9.20789 12.298 8.9085 12.5295 8.62447 12.6955C8.3402 12.8616 8.14261 12.9181 7.99967 12.9181V13.6681V14.4181C8.52341 14.4181 8.99248 14.2178 9.38126 13.9906C9.77027 13.7633 10.1503 13.4651 10.4895 13.1977L10.0252 12.6087Z'
        fill='currentColor'
      />
    </svg>
  );
};
