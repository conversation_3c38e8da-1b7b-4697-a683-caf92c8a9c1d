import React from 'react';

export const NotiLineRemove = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 16 16' fill='none' {...props}>
      <g clipPath='url(#clip0_33028_54772)'>
        <path
          d='M14.6668 6.9987V7.9987C14.6668 11.1414 14.6668 12.7127 13.6905 13.6891C12.7142 14.6654 11.1429 14.6654 8.00016 14.6654C4.85747 14.6654 3.28612 14.6654 2.30981 13.6891C1.3335 12.7127 1.3335 11.1414 1.3335 7.9987C1.3335 4.856 1.3335 3.28465 2.30981 2.30834C3.28612 1.33203 4.85747 1.33203 8.00016 1.33203H9.00016'
          stroke='currentColor'
          strokeWidth='1.5'
          strokeLinecap='round'
        />
        <path d='M4.6665 9.33203H10.6665' stroke='currentColor' strokeWidth='1.5' strokeLinecap='round' />
        <path d='M4.6665 11.668H8.6665' stroke='currentColor' strokeWidth='1.5' strokeLinecap='round' />
        <path
          d='M14.6668 1.33203L11.3335 4.66535M11.3335 1.33202L14.6668 4.66534'
          stroke='currentColor'
          strokeWidth='1.5'
          strokeLinecap='round'
        />
      </g>
      <defs>
        <clipPath id='clip0_33028_54772'>
          <rect width='16' height='16' rx='5' fill='white' />
        </clipPath>
      </defs>
    </svg>
  );
};
