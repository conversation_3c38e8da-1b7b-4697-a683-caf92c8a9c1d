import React from 'react';

export const Structure = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 16 16' fill='none' {...props}>
      <g clipPath='url(#clip0_33028_54608)'>
        <path
          d='M5.3335 3.33203C5.3335 4.4366 4.43807 5.33203 3.3335 5.33203C2.22893 5.33203 1.3335 4.4366 1.3335 3.33203C1.3335 2.22746 2.22893 1.33203 3.3335 1.33203C4.43807 1.33203 5.3335 2.22746 5.3335 3.33203Z'
          stroke='currentColor'
          strokeWidth='1.5'
        />
        <path
          d='M14.6665 3.33203C14.6665 4.4366 13.7711 5.33203 12.6665 5.33203C11.5619 5.33203 10.6665 4.4366 10.6665 3.33203C10.6665 2.22746 11.5619 1.33203 12.6665 1.33203C13.7711 1.33203 14.6665 2.22746 14.6665 3.33203Z'
          stroke='currentColor'
          strokeWidth='1.5'
        />
        <path
          d='M5.3335 12.668C5.3335 13.7725 4.43807 14.668 3.3335 14.668C2.22893 14.668 1.3335 13.7725 1.3335 12.668C1.3335 11.5634 2.22893 10.668 3.3335 10.668C4.43807 10.668 5.3335 11.5634 5.3335 12.668Z'
          stroke='currentColor'
          strokeWidth='1.5'
        />
        <path
          d='M14.6665 12.668C14.6665 13.7725 13.7711 14.668 12.6665 14.668C11.5619 14.668 10.6665 13.7725 10.6665 12.668C10.6665 11.5634 11.5619 10.668 12.6665 10.668C13.7711 10.668 14.6665 11.5634 14.6665 12.668Z'
          stroke='currentColor'
          strokeWidth='1.5'
        />
        <path d='M5.3335 12.668H10.6668' stroke='currentColor' strokeWidth='1.5' strokeLinecap='round' />
        <path d='M5.3335 3.33203H10.6668' stroke='currentColor' strokeWidth='1.5' strokeLinecap='round' />
        <path d='M12.6665 10.668L12.6665 5.33464' stroke='currentColor' strokeWidth='1.5' strokeLinecap='round' />
        <path d='M3.3335 10.668L3.3335 5.33464' stroke='currentColor' strokeWidth='1.5' strokeLinecap='round' />
      </g>
      <defs>
        <clipPath id='clip0_33028_54608'>
          <rect width='16' height='16' rx='5' fill='white' />
        </clipPath>
      </defs>
    </svg>
  );
};
