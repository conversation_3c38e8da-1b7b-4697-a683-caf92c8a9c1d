import React from 'react';

export const ServerSquareUpdate = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 16 16' fill='none' {...props}>
      <path
        d='M8.66683 14.75C9.08104 14.75 9.41683 14.4142 9.41683 14C9.41683 13.5858 9.08104 13.25 8.66683 13.25V14.75ZM2.11454 13.219L2.64487 12.6886L2.64487 12.6886L2.11454 13.219ZM13.8858 2.78105L13.3555 3.31138V3.31138L13.8858 2.78105ZM13.9168 8.66667C13.9168 9.08088 14.2526 9.41667 14.6668 9.41667C15.081 9.41667 15.4168 9.08088 15.4168 8.66667H13.9168ZM6.66683 2V2.75H9.3335V2V1.25H6.66683V2ZM1.3335 8.66667H2.0835V8H1.3335H0.583496V8.66667H1.3335ZM1.3335 8H2.0835V7.33333H1.3335H0.583496V8H1.3335ZM8.66683 14V13.25H6.66683V14V14.75H8.66683V14ZM14.6668 7.33333H13.9168V8H14.6668H15.4168V7.33333H14.6668ZM1.3335 8.66667H0.583496C0.583496 9.90254 0.581903 10.9049 0.687815 11.6927C0.796628 12.502 1.03111 13.1962 1.58421 13.7493L2.11454 13.219L2.64487 12.6886C2.41693 12.4607 2.26089 12.1358 2.17444 11.4928C2.08509 10.8282 2.0835 9.94495 2.0835 8.66667H1.3335ZM6.66683 14V13.25C5.38855 13.25 4.50529 13.2484 3.84071 13.1591C3.1977 13.0726 2.87282 12.9166 2.64487 12.6886L2.11454 13.219L1.58421 13.7493C2.13732 14.3024 2.8315 14.5369 3.64084 14.6457C4.4286 14.7516 5.43095 14.75 6.66683 14.75V14ZM9.3335 2V2.75C10.6118 2.75 11.495 2.75159 12.1596 2.84094C12.8026 2.92739 13.1275 3.08343 13.3555 3.31138L13.8858 2.78105L14.4161 2.25072C13.863 1.69762 13.1688 1.46313 12.3595 1.35432C11.5717 1.24841 10.5694 1.25 9.3335 1.25V2ZM14.6668 7.33333H15.4168C15.4168 6.09746 15.4184 5.0951 15.3125 4.30734C15.2037 3.498 14.9692 2.80382 14.4161 2.25072L13.8858 2.78105L13.3555 3.31138C13.5834 3.53933 13.7394 3.86421 13.8259 4.50721C13.9152 5.17179 13.9168 6.05505 13.9168 7.33333H14.6668ZM6.66683 2V1.25C5.43095 1.25 4.4286 1.24841 3.64084 1.35432C2.8315 1.46313 2.13732 1.69762 1.58421 2.25072L2.11454 2.78105L2.64487 3.31138C2.87282 3.08343 3.1977 2.92739 3.84071 2.84094C4.50529 2.75159 5.38855 2.75 6.66683 2.75V2ZM1.3335 7.33333H2.0835C2.0835 6.05505 2.08509 5.17179 2.17444 4.50721C2.26089 3.86421 2.41693 3.53933 2.64487 3.31138L2.11454 2.78105L1.58421 2.25072C1.03111 2.80382 0.796628 3.498 0.687815 4.30734C0.581903 5.0951 0.583496 6.09746 0.583496 7.33333H1.3335ZM1.3335 8V8.75H14.6668V8V7.25H1.3335V8ZM14.6668 8H13.9168V8.66667H14.6668H15.4168V8H14.6668Z'
        fill='currentColor'
      />
      <path d='M9 5L12 5' stroke='currentColor' strokeWidth='1.5' strokeLinecap='round' />
      <path d='M4 11.668L4 10.3346' stroke='currentColor' strokeWidth='1.5' strokeLinecap='round' />
      <path d='M4 5.66797L4 4.33464' stroke='currentColor' strokeWidth='1.5' strokeLinecap='round' />
      <path d='M6 11.668L6 10.3346' stroke='currentColor' strokeWidth='1.5' strokeLinecap='round' />
      <path d='M6 5.66797L6 4.33464' stroke='currentColor' strokeWidth='1.5' strokeLinecap='round' />
      <path
        d='M12.3365 10L12.3365 9.25L12.3365 10ZM10.3894 11.6667H9.63942V11.6667L10.3894 11.6667ZM10.3894 12L9.90171 12.5698C10.1824 12.8101 10.5964 12.8101 10.8771 12.5698L10.3894 12ZM11.2665 12.2364C11.5812 11.9671 11.618 11.4936 11.3486 11.179C11.0793 10.8643 10.6058 10.8275 10.2911 11.0969L11.2665 12.2364ZM10.4877 11.0969C10.173 10.8275 9.69958 10.8643 9.43023 11.179C9.16087 11.4936 9.19761 11.9671 9.51229 12.2364L10.4877 11.0969ZM13.3892 11.2352C13.6296 11.5726 14.0979 11.6512 14.4352 11.4108C14.7726 11.1704 14.8512 10.7021 14.6108 10.3648L13.3892 11.2352ZM12.3365 10L12.3365 9.25C10.9606 9.25 9.63942 10.2268 9.63942 11.6667H10.3894H11.1394C11.1394 11.2656 11.5617 10.75 12.3365 10.75V10ZM10.3894 11.6667L9.63942 11.6667L9.63942 12L10.3894 12L11.1394 12L11.1394 11.6667L10.3894 11.6667ZM10.3894 12L10.8771 12.5698L11.2665 12.2364L10.7788 11.6667L10.2911 11.0969L9.90171 11.4302L10.3894 12ZM10.3894 12L10.8771 11.4302L10.4877 11.0969L10 11.6667L9.51229 12.2364L9.90171 12.5698L10.3894 12ZM14 10.8L14.6108 10.3648C14.1159 9.67024 13.2615 9.25 12.3365 9.25L12.3365 10V10.75C12.8206 10.75 13.2004 10.9703 13.3892 11.2352L14 10.8Z'
        fill='currentColor'
      />
      <path
        d='M12.33 14L12.33 14.75L12.33 14ZM14.2771 12.3333H15.0271H14.2771ZM14.2771 12L14.7648 11.4302C14.4841 11.1899 14.0701 11.1899 13.7894 11.4302L14.2771 12ZM13.4 11.7636C13.0853 12.0329 13.0485 12.5064 13.3179 12.821C13.5872 13.1357 14.0607 13.1725 14.3754 12.9031L13.4 11.7636ZM14.1788 12.9031C14.4935 13.1725 14.9669 13.1357 15.2363 12.821C15.5056 12.5064 15.4689 12.0329 15.1542 11.7636L14.1788 12.9031ZM11.2773 12.7648C11.0369 12.4274 10.5686 12.3488 10.2313 12.5892C9.89393 12.8296 9.81534 13.2979 10.0557 13.6352L11.2773 12.7648ZM12.33 14L12.33 14.75C13.7059 14.75 15.0271 13.7732 15.0271 12.3333H14.2771H13.5271C13.5271 12.7344 13.1048 13.25 12.33 13.25V14ZM14.2771 12.3333H15.0271V12H14.2771H13.5271V12.3333H14.2771ZM14.2771 12L13.7894 11.4302L13.4 11.7636L13.8877 12.3333L14.3754 12.9031L14.7648 12.5698L14.2771 12ZM14.2771 12L13.7894 12.5698L14.1788 12.9031L14.6665 12.3333L15.1542 11.7636L14.7648 11.4302L14.2771 12ZM10.6665 13.2L10.0557 13.6352C10.5506 14.3298 11.405 14.75 12.33 14.75L12.33 14V13.25C11.8459 13.25 11.4661 13.0297 11.2773 12.7648L10.6665 13.2Z'
        fill='currentColor'
      />
    </svg>
  );
};
