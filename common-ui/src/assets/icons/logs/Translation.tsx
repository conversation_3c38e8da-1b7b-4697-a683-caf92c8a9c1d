import React from 'react';

export const Translation = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 16 16' fill='none' {...props}>
      <path
        d='M12.7611 3.33336C13.94 4.53586 14.6668 6.18305 14.6668 8.00002C14.6668 9.83857 13.9226 11.5033 12.7189 12.7093M3.3335 12.761C2.09925 11.551 1.3335 9.86495 1.3335 8.00002C1.3335 6.15667 2.08164 4.48805 3.29085 3.28125'
        stroke='currentColor'
        strokeWidth='1.5'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        d='M10.8566 5.36135C11.5639 6.03788 12 6.9646 12 7.98683C12 9.0212 11.5535 9.95778 10.8313 10.6363M5.2 10.6654C4.45946 9.98464 4 9.03605 4 7.98683C4 6.94975 4.44889 6.01098 5.17441 5.33203'
        stroke='currentColor'
        strokeWidth='1.5'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <circle cx='7.99984' cy='8.0013' r='1.33333' stroke='currentColor' strokeWidth='1.5' />
    </svg>
  );
};
