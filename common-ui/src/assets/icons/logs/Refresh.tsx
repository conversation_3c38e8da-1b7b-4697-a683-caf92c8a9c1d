import React from 'react';

export const Refresh = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 16 16' fill='none' {...props}>
      <path
        d='M2.45288 7.55556H1.70288V7.55556L2.45288 7.55556ZM2.45288 8.66667L1.92464 9.19908C2.21705 9.4892 2.68872 9.4892 2.98112 9.19908L2.45288 8.66667ZM4.101 8.08797C4.39504 7.79623 4.39691 7.32135 4.10517 7.02731C3.81343 6.73327 3.33856 6.73141 3.04451 7.02315L4.101 8.08797ZM1.86125 7.02315C1.56721 6.73141 1.09234 6.73327 0.800599 7.02731C0.508858 7.32135 0.510724 7.79623 0.804765 8.08797L1.86125 7.02315ZM12.1971 5.05946C12.414 5.41232 12.8759 5.52252 13.2288 5.30558C13.5816 5.08865 13.6918 4.62674 13.4749 4.27388L12.1971 5.05946ZM8.05225 2V1.25C4.55116 1.25 1.70288 4.06755 1.70288 7.55556H2.45288H3.20288C3.20288 4.90706 5.36845 2.75 8.05225 2.75V2ZM2.45288 7.55556L1.70288 7.55556L1.70288 8.66667H2.45288L3.20288 8.66667L3.20288 7.55556H2.45288ZM2.45288 8.66667L2.98112 9.19908L4.101 8.08797L3.57276 7.55556L3.04451 7.02315L1.92464 8.13426L2.45288 8.66667ZM2.45288 8.66667L2.98112 8.13426L1.86125 7.02315L1.33301 7.55556L0.804765 8.08797L1.92464 9.19908L2.45288 8.66667ZM12.836 4.66667L13.4749 4.27388C12.3601 2.46049 10.3477 1.25 8.05225 1.25V2V2.75C9.80879 2.75 11.3457 3.67466 12.1971 5.05946L12.836 4.66667Z'
        fill='currentColor'
      />
      <path
        d='M13.5429 7.33203L14.0702 6.79862C13.778 6.50984 13.3079 6.50984 13.0157 6.79862L13.5429 7.33203ZM11.8915 7.90973C11.5969 8.2009 11.5942 8.67577 11.8854 8.97037C12.1765 9.26497 12.6514 9.26774 12.946 8.97656L11.8915 7.90973ZM14.1399 8.97656C14.4345 9.26774 14.9093 9.26497 15.2005 8.97037C15.4917 8.67577 15.4889 8.2009 15.1943 7.90973L14.1399 8.97656ZM3.75836 10.9382C3.54083 10.5857 3.07873 10.4763 2.72624 10.6938C2.37374 10.9113 2.26434 11.3734 2.48187 11.7259L3.75836 10.9382ZM7.92215 13.9987V14.7487C11.4324 14.7487 14.2929 11.9338 14.2929 8.44314H13.5429H12.7929C12.7929 11.0889 10.6205 13.2487 7.92215 13.2487V13.9987ZM13.5429 8.44314H14.2929V7.33203H13.5429H12.7929V8.44314H13.5429ZM13.5429 7.33203L13.0157 6.79862L11.8915 7.90973L12.4188 8.44314L12.946 8.97656L14.0702 7.86545L13.5429 7.33203ZM13.5429 7.33203L13.0157 7.86545L14.1399 8.97656L14.6671 8.44314L15.1943 7.90973L14.0702 6.79862L13.5429 7.33203ZM3.12012 11.332L2.48187 11.7259C3.60106 13.5395 5.62032 14.7487 7.92215 14.7487V13.9987V13.2487C6.15651 13.2487 4.61285 12.3228 3.75836 10.9382L3.12012 11.332Z'
        fill='currentColor'
      />
    </svg>
  );
};
