import React from 'react';

export const CheckCircle = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 16 16' fill='none' {...props}>
      <g clipPath='url(#clip0_33028_54558)'>
        <circle cx='7.99967' cy='7.9987' r='6.66667' stroke='currentColor' strokeWidth='1.5' />
        <path
          d='M5.66699 8.33203L7.00033 9.66536L10.3337 6.33203'
          stroke='currentColor'
          strokeWidth='1.5'
          strokeLinecap='round'
          strokeLinejoin='round'
        />
      </g>
      <defs>
        <clipPath id='clip0_33028_54558'>
          <rect width='16' height='16' fill='white' />
        </clipPath>
      </defs>
    </svg>
  );
};
