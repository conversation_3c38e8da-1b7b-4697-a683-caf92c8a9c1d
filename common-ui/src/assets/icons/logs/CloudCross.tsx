import React from 'react';

export const CloudCross = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 16 16' fill='none' {...props}>
      <path
        d='M4.19015 12.6667C2.61219 12.6667 1.33301 11.4025 1.33301 9.84314C1.33301 8.28374 2.61219 7.01961 4.19015 7.01961C4.37956 7.01961 4.56467 7.03782 4.74378 7.07258M9.58698 5.35147C9.98416 5.21274 10.4116 5.13726 10.8568 5.13726C11.2933 5.13726 11.7126 5.20979 12.1031 5.34332M4.74378 7.07258C4.59105 6.6652 4.50761 6.22458 4.50761 5.76471C4.50761 3.68552 6.21319 2 8.31714 2C10.2769 2 11.8911 3.46247 12.1031 5.34332M4.74378 7.07258C5.12006 7.14559 5.46986 7.2916 5.77745 7.49507M12.1031 5.34332C13.595 5.85349 14.6663 7.25408 14.6663 8.90196C14.6663 10.7066 13.3814 12.2147 11.6663 12.5815'
        stroke='currentColor'
        strokeWidth='1.5'
        strokeLinecap='round'
      />
      <path
        d='M9 11.668L8 12.668M8 12.668L7 13.668M8 12.668L7 11.668M8 12.668L8.99999 13.668'
        stroke='currentColor'
        strokeWidth='1.5'
        strokeLinecap='round'
      />
    </svg>
  );
};
