import React from 'react';

export const FolderCheck = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 16 16' fill='none' {...props}>
      <g clipPath='url(#clip0_33028_54568)'>
        <path
          d='M1.3335 4.63186C1.3335 4.04351 1.3335 3.74933 1.37973 3.50429C1.58326 2.42557 2.42704 1.58179 3.50575 1.37826C3.7508 1.33203 4.04497 1.33203 4.63333 1.33203C4.89111 1.33203 5.02001 1.33203 5.14388 1.34361C5.67793 1.39356 6.18451 1.60339 6.59746 1.94571C6.69324 2.02511 6.78438 2.11625 6.96666 2.29853L7.3335 2.66537C7.87735 3.20922 8.14928 3.48114 8.47491 3.66231C8.65379 3.76184 8.84354 3.84043 9.0404 3.89655C9.39876 3.9987 9.78332 3.9987 10.5524 3.9987H10.8016C12.5565 3.9987 13.4339 3.9987 14.0043 4.51167C14.0567 4.55886 14.1067 4.60879 14.1539 4.66125C14.6668 5.2316 14.6668 6.10906 14.6668 7.86397V9.33203C14.6668 11.8462 14.6668 13.1033 13.8858 13.8843C13.1047 14.6654 11.8477 14.6654 9.3335 14.6654H6.66683C4.15267 14.6654 2.89559 14.6654 2.11454 13.8843C1.3335 13.1033 1.3335 11.8462 1.3335 9.33203V4.63186Z'
          stroke='currentColor'
          strokeWidth='1.5'
        />
        <path
          d='M6.6665 9L7.55539 10L9.33317 8'
          stroke='currentColor'
          strokeWidth='1.5'
          strokeLinecap='round'
          strokeLinejoin='round'
        />
      </g>
      <defs>
        <clipPath id='clip0_33028_54568'>
          <rect width='16' height='16' fill='white' />
        </clipPath>
      </defs>
    </svg>
  );
};
