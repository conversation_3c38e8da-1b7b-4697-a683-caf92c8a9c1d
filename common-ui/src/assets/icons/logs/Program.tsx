import React from 'react';

export const Program = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg xmlns='http://www.w3.org/2000/svg' width='20' height='20' viewBox='0 0 20 20' fill='none' {...props}>
      <path
        opacity='0.5'
        d='M1.66699 10.0013C1.66699 6.07293 1.66699 4.10875 2.88738 2.88836C4.10777 1.66797 6.07195 1.66797 10.0003 1.66797C13.9287 1.66797 15.8929 1.66797 17.1133 2.88836C18.3337 4.10875 18.3337 6.07293 18.3337 10.0013C18.3337 13.9297 18.3337 15.8939 17.1133 17.1142C15.8929 18.3346 13.9287 18.3346 10.0003 18.3346C6.07195 18.3346 4.10777 18.3346 2.88738 17.1142C1.66699 15.8939 1.66699 13.9297 1.66699 10.0013Z'
        fill='currentColor'
      />
      <path
        d='M11.2396 5.37301C11.5731 5.46235 11.7709 5.80506 11.6816 6.13848L9.52476 14.1879C9.43542 14.5213 9.09271 14.7191 8.75929 14.6298C8.42588 14.5405 8.22801 14.1978 8.31735 13.8643L10.4742 5.81496C10.5635 5.48154 10.9062 5.28368 11.2396 5.37301Z'
        fill='currentColor'
      />
      <path
        d='M12.4743 7.05949C12.7184 6.81541 13.1141 6.81541 13.3582 7.05949L13.5318 7.2331C14.0613 7.76251 14.5028 8.20399 14.8061 8.60159C15.1263 9.02125 15.3509 9.46465 15.3509 10.0014C15.3509 10.5382 15.1263 10.9816 14.8061 11.4013C14.5028 11.7989 14.0613 12.2403 13.5319 12.7697L13.5318 12.7697L13.3582 12.9434C13.1141 13.1874 12.7184 13.1874 12.4743 12.9434C12.2302 12.6993 12.2302 12.3036 12.4743 12.0595L12.6173 11.9165C13.1853 11.3485 13.566 10.9659 13.8123 10.643C14.0477 10.3346 14.1009 10.155 14.1009 10.0014C14.1009 9.84785 14.0477 9.66829 13.8123 9.35981C13.566 9.03691 13.1853 8.65439 12.6173 8.08635L12.4743 7.94337C12.2302 7.69929 12.2302 7.30356 12.4743 7.05949Z'
        fill='currentColor'
      />
      <path
        d='M6.64114 7.05949C6.88522 6.81541 7.28095 6.81541 7.52502 7.05949C7.7691 7.30356 7.7691 7.69929 7.52502 7.94337L7.38205 8.08635C6.814 8.65439 6.43336 9.03691 6.18699 9.35981C5.95163 9.66829 5.89844 9.84785 5.89844 10.0014C5.89844 10.155 5.95163 10.3346 6.18699 10.643C6.43336 10.9659 6.814 11.3485 7.38205 11.9165L7.52502 12.0595C7.7691 12.3036 7.7691 12.6993 7.52502 12.9434C7.28095 13.1874 6.88522 13.1874 6.64114 12.9434L6.46752 12.7698C5.93807 12.2403 5.49657 11.7989 5.19322 11.4013C4.87302 10.9816 4.64844 10.5382 4.64844 10.0014C4.64844 9.46465 4.87302 9.02125 5.19322 8.60159C5.49657 8.20399 5.93807 7.76252 6.46752 7.2331L6.64114 7.05949Z'
        fill='currentColor'
      />
    </svg>
  );
};
