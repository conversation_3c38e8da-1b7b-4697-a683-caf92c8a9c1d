export const Stroke = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg xmlns='http://www.w3.org/2000/svg' width='8' height='8' viewBox='0 0 8 8' fill='none' {...props}>
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M0.632324 0.471191C0.994954 0.108561 1.58289 0.108561 1.94552 0.471191L3.86035 2.38602L5.77518 0.471191C6.13781 0.108561 6.72575 0.108561 7.08838 0.471191C7.45101 0.833821 7.45101 1.42176 7.08838 1.78439L5.17355 3.69922L7.08838 5.61405C7.45101 5.97668 7.45101 6.56462 7.08838 6.92725C6.72575 7.28988 6.13781 7.28988 5.77518 6.92725L3.86035 5.01242L1.94552 6.92725C1.58289 7.28988 0.994953 7.28988 0.632324 6.92725C0.269694 6.56462 0.269694 5.97668 0.632324 5.61405L2.54715 3.69922L0.632324 1.78439C0.269694 1.42176 0.269694 0.833821 0.632324 0.471191Z'
        fill='currentColor'
      />
    </svg>
  );
};
