import { ComponentProps } from 'react';

export const LinearDangerTriangle = (props: ComponentProps<'svg'>) => {
  return (
    <svg width='58' height='52' viewBox='0 0 58 52' fill='none' xmlns='http://www.w3.org/2000/svg' {...props}>
      <path
        d='M29 6.5C31.3111 6.5 33.2561 7.78907 35.4619 10.6191C37.1203 12.7469 38.8771 15.6812 41.0098 19.4473L43.2773 23.4912L44.0781 24.9248C46.7544 29.7179 48.7297 33.2594 49.7451 36.0381C50.6936 38.6338 50.7582 40.4386 49.8828 41.9287L49.6943 42.2227C48.6131 43.7891 46.7029 44.6358 43.457 45.0674C40.214 45.4986 35.7799 45.5 29.7998 45.5H28.2002C22.2201 45.5 17.786 45.4986 14.543 45.0674C11.4997 44.6627 9.63022 43.8934 8.51758 42.5078L8.30566 42.2227C7.23794 40.6756 7.24312 38.8069 8.25488 36.0381C9.01642 33.9542 10.3176 31.4411 12.0498 28.293L13.9219 24.9248L14.7227 23.4912C17.9412 17.7269 20.3269 13.4561 22.5381 10.6191C24.6749 7.8776 26.567 6.58228 28.7842 6.50391L29 6.5Z'
        fill='#DC2626'
        stroke='url(#paint0_linear_25158_32773)'
      />
      <path
        opacity='0.25'
        d='M29 0.5C32.1229 0.5 34.7271 2.22455 37.6445 5.91602C40.2028 9.15299 42.936 13.8174 46.4199 19.9502L47.9609 22.668L49.0156 24.5312C52.5412 30.7582 55.1525 35.3749 56.4961 39.001C57.8357 42.6166 57.8604 45.1023 56.4121 47.1719C54.9483 49.2636 52.3716 50.3736 48.0791 50.9365C43.7906 51.4989 37.9325 51.5 30.0547 51.5H27.9453C20.0675 51.5 14.2094 51.4989 9.9209 50.9365C5.76264 50.3912 3.21457 49.3325 1.72852 47.3652L1.58789 47.1719C0.139585 45.1023 0.164284 42.6166 1.50391 39.001C2.67952 35.8281 4.82586 31.8968 7.70605 26.792L8.98438 24.5312L10.0391 22.668C14.2792 15.1789 17.4318 9.61536 20.3555 5.91602C23.2729 2.22455 25.8771 0.5 29 0.5Z'
        fill='#DC2626'
        stroke='url(#paint1_linear_25158_32773)'
      />
      <path
        d='M22.3117 24.7615C25.2301 19.5872 26.6893 17 29 17C31.3107 17 32.7699 19.5872 35.6883 24.7615L36.0519 25.4063C38.4771 29.7061 39.6897 31.856 38.5937 33.428C37.4978 35 34.7864 35 29.3637 35H28.6363C23.2136 35 20.5022 35 19.4063 33.428C18.3103 31.856 19.5229 29.7061 21.9481 25.4063L22.3117 24.7615Z'
        stroke='white'
        strokeWidth='1.5'
      />
      <path d='M29 22V27' stroke='white' strokeWidth='1.5' strokeLinecap='round' />
      <circle cx='29' cy='30' r='1' fill='white' />
      <defs>
        <linearGradient id='paint0_linear_25158_32773' x1='29' y1='6' x2='29' y2='46' gradientUnits='userSpaceOnUse'>
          <stop offset='0.4' stopColor='#1A1F29' stopOpacity='0.08' />
          <stop offset='1' stopColor='#1A1F29' stopOpacity='0.16' />
        </linearGradient>
        <linearGradient id='paint1_linear_25158_32773' x1='29' y1='0' x2='29' y2='52' gradientUnits='userSpaceOnUse'>
          <stop offset='0.4' stopColor='#1A1F29' stopOpacity='0.08' />
          <stop offset='1' stopColor='#1A1F29' stopOpacity='0.16' />
        </linearGradient>
      </defs>
    </svg>
  );
};
