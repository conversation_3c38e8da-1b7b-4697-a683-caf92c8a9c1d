import { SVGProps } from 'react';

interface IProps extends SVGProps<SVGSVGElement> {
  size?: number;
}

export const IconIllustrationSuccess = (props: IProps) => {
  const { strokeWidth = 1.5, ...rest } = props;

  return (
    <svg xmlns='http://www.w3.org/2000/svg' width={58} height={52} viewBox='0 0 58 52' fill='none' {...rest}>
      <path
        d='M9 26C9 16.5719 9 11.8579 11.9289 8.92893C14.8579 6 19.5719 6 29 6C38.4281 6 43.1421 6 46.0711 8.92893C49 11.8579 49 16.5719 49 26C49 35.4281 49 40.1421 46.0711 43.0711C43.1421 46 38.4281 46 29 46C19.5719 46 14.8579 46 11.9289 43.0711C9 40.1421 9 35.4281 9 26Z'
        fill='currentColor'
      />
      <path
        opacity='0.25'
        d='M3 26C3 13.7435 3 7.61522 6.80761 3.80761C10.6152 0 16.7435 0 29 0C41.2565 0 47.3848 0 51.1924 3.80761C55 7.61522 55 13.7435 55 26C55 38.2565 55 44.3848 51.1924 48.1924C47.3848 52 41.2565 52 29 52C16.7435 52 10.6152 52 6.80761 48.1924C3 44.3848 3 38.2565 3 26Z'
        fill='currentColor'
      />
      <path
        d='M19 26C19 21.286 19 18.9289 20.4645 17.4645C21.9289 16 24.286 16 29 16C33.714 16 36.0711 16 37.5355 17.4645C39 18.9289 39 21.286 39 26C39 30.714 39 33.0711 37.5355 34.5355C36.0711 36 33.714 36 29 36C24.286 36 21.9289 36 20.4645 34.5355C19 33.0711 19 30.714 19 26Z'
        stroke='white'
        strokeWidth={strokeWidth}
      />
      <path
        d='M25.5 26.5L27.5 28.5L32.5 23.5'
        stroke='white'
        strokeWidth={strokeWidth}
        strokeLinecap='round'
        strokeLinejoin='round'
      />
    </svg>
  );
};
