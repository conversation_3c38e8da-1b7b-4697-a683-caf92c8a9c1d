import { AxonButton, AxonButtonVariant } from '@/components/AxonButton';
import {
  AxonDropdownMenu,
  AxonDropdownMenuContent,
  AxonDropdownMenuItem,
  AxonDropdownMenuTrigger,
} from '@/components/AxonDropdown';
import { cn } from '@/utils';
import { CheckIcon, Globe } from 'lucide-react';
import { useState } from 'react';

type TLang = 'en' | 'de';

type AxonDropdownLanguageProps = {
  variant?: AxonButtonVariant;
  className?: string;
};

const LANGUAGES: TLang[] = ['en', 'de'];

function AxonDropdownLanguage({ variant = 'default', className }: AxonDropdownLanguageProps) {
  const currentLang = localStorage.getItem('lang') ?? process.env.LANGUAGE_ENV ?? 'de';
  const [language, setLanguage] = useState<string>(currentLang);

  const onChangeLang = (lang: TLang) => {
    if (lang === language) return;
    localStorage.setItem('lang', lang);
    setLanguage(lang);
    // Reload the page to apply the language change from API
    window.location.reload();
  };

  return (
    <AxonDropdownMenu>
      <AxonDropdownMenuTrigger asChild data-testid='header-settings-language-dropdown' aria-label='Language'>
        <AxonButton variant={variant} className={cn('flex flex-row items-center gap-x-3 rounded-md p-2', className)}>
          <Globe />
          {language === 'en' ? 'English' : 'Deutsch'}
        </AxonButton>
      </AxonDropdownMenuTrigger>
      <AxonDropdownMenuContent align='start' side='bottom'>
        {LANGUAGES.map((lang) => (
          <AxonDropdownMenuItem key={lang} onClick={() => onChangeLang(lang)}>
            {language === lang ? <CheckIcon /> : <div className='size-4' />}
            {lang === 'en' ? 'English' : 'Deutsch'}
          </AxonDropdownMenuItem>
        ))}
      </AxonDropdownMenuContent>
    </AxonDropdownMenu>
  );
}

export { AxonDropdownLanguage };
