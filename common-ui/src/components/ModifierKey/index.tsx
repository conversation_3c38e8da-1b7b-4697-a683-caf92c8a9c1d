import { Command } from '@/assets';
import { cn } from '@/utils';
import { isMac } from '@/utils/platform.util';
import { useTranslation } from 'react-i18next';

interface ModifierKeyProps extends React.HTMLAttributes<HTMLSpanElement> {
  /** Custom modifier key element */
  modifier?: React.ReactNode;
  /** Custom aria label for modifier key element */
  modifierAriaLabel?: string;
}

/**
 * A component that displays a modifier key (Command on Mac, Ctrl on other platforms)
 * followed by additional content, typically used for keyboard shortcuts display.
 *
 * @example
 * ```tsx
 * <ModifierKey>S</ModifierKey> // Displays "⌘ S" on Mac or "Ctrl S" on other platforms
 * <ModifierKey className="text-sm">Enter</ModifierKey>
 * <ModifierKey modifier={<CustomIcon />}>Enter</ModifierKey>
 * ```
 */
export const ModifierKey = ({
  className,
  children,
  modifier: customModifier,
  modifierAriaLabel,
  ...props
}: ModifierKeyProps) => {
  const { t } = useTranslation();

  const defaultAriaLabel = isMac() ? t('ariaLabel.commandKey') : t('ariaLabel.controlKey');
  const ariaLabel = modifierAriaLabel ?? defaultAriaLabel;

  const modifier = customModifier ?? (isMac() ? <Command aria-hidden /> : 'Ctrl');

  return (
    <span
      className={cn(
        'inline-flex items-center gap-1 leading-none [&>span>:where(.lucide)]:size-[1em] [&>span>svg]:size-[1em]',
        className,
      )}
      {...props}>
      <span aria-label={ariaLabel} role='img'>
        {modifier}
      </span>
      <span>{children}</span>
    </span>
  );
};
