export const isMac = (): boolean => {
  if (typeof window === 'undefined') {
    return false;
  }

  // NOTE: Firefox and Safari don't support this API yet
  if ('userAgentData' in navigator && navigator.userAgentData) {
    const userAgentData = navigator.userAgentData as any;

    if (userAgentData.platform) {
      return userAgentData.platform.toLowerCase() === 'macos';
    }
  }

  const userAgent = navigator.userAgent;

  if (/Mac|iPhone|iPad|iPod/.test(userAgent)) {
    return true;
  }

  return false;
};

export const getModifierKeyText = (): string => {
  return isMac() ? 'Cmd' : 'Ctrl';
};

export const getModifierKeySymbol = (): string => {
  return isMac() ? '⌘' : 'Ctrl';
};
