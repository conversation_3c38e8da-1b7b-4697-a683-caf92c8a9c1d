import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { HelmetProvider } from 'react-helmet-async';
import { BrowserRouter, BrowserRouterProps } from 'react-router-dom';
import { StrictMode } from 'react';

import i18n from '@/i18n';
import { ThemeProvider, ThemeProviderProps } from './ThemeProvider';
import { useChangeLanguageEvent } from 'services/Hooks';
import { AxonToaster } from '@/components';

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
      retry: 3,
      retryDelay: 1 * 1000,
    },
  },
});

interface WrapperProviderProps {
  children: React.ReactNode;
  themeProps?: Omit<ThemeProviderProps, 'children'>;
  routerProps?: Omit<BrowserRouterProps, 'children'>;
}

export function AppProviders(props: WrapperProviderProps) {
  const { children, themeProps, routerProps } = props;

  useChangeLanguageEvent(i18n);

  return (
    <StrictMode>
      <ThemeProvider {...themeProps}>
        <QueryClientProvider client={queryClient}>
          <BrowserRouter {...routerProps}>
            <HelmetProvider>{children}</HelmetProvider>
          </BrowserRouter>
          <ReactQueryDevtools initialIsOpen={false} />
          <AxonToaster richColors />
        </QueryClientProvider>
      </ThemeProvider>
    </StrictMode>
  );
}
